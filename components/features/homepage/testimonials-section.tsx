"use client";
import { <PERSON><PERSON>, Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { subtitle, title } from "@/components/primitives";
import { testimonials } from "@/lib/constants";

export default function TestimonialsSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2, delayChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.6, ease: "easeOut" } },
  };

  return (
    <section className="py-24 md:py-32 bg-gradient-to-b from-white to-default-50 dark:from-background dark:to-default-950/20">
      <div className="max-w-4xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className={title({ size: "lg" })}>Loved by professionals worldwide</h2>
          <p className={subtitle({ class: "mt-6 max-w-2xl mx-auto text-lg" })}>
            Don't just take our word for it. Here's what our users have to say about their success.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8 md:space-y-0 md:grid md:grid-cols-3 md:gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`md:col-span-1 ${index === 1 ? "md:col-start-2" : ""}`}
            >
              <Card className="h-full shadow-lg border-default-200 dark:border-default-800 bg-white/80 dark:bg-default-100/80 backdrop-blur-md">
                <CardBody className="p-8 flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-full flex items-center justify-center text-primary-500 mb-6">
                    <Icon icon="heroicons:chat-bubble-left-right-20-solid" className="w-8 h-8" />
                  </div>
                  <p className="text-default-700 italic text-lg mb-6">"{testimonial.content}"</p>
                  <div className="flex items-center gap-4">
                    <Avatar name={testimonial.name} size="md" />
                    <div>
                      <p className="font-bold text-default-900">{testimonial.name}</p>
                      <p className="text-sm text-default-600">
                        {testimonial.role} at {testimonial.company}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
