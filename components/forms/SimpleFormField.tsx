"use client";

import { Input } from "@heroui/react";
import { CheckboxField, DatePickerComponent, TrixEditorField } from "@/components/forms";
import { useFormStore } from "@/lib/form-store";

interface SimpleFormFieldProps {
  name: string;
  type: string;
  label?: string;
  placeholder?: string;
  description?: string;
  className?: string;
  value: any;
  collectionName: string;
  itemIndex: number;
  resumeData?: any;
  itemContext?: any;
  schemaEntity?: string;
}

export function SimpleFormField({
  name,
  type,
  label,
  placeholder,
  description,
  className,
  value,
  collectionName,
  itemIndex,
  resumeData,
  itemContext,
  schemaEntity,
}: SimpleFormFieldProps) {
  const { updateNestedField } = useFormStore();

  const handleChange = (newValue: any) => {
    updateNestedField(`${collectionName}[${itemIndex}][${name}]`, newValue);
  };

  const commonProps = {
    label,
    description,
    className,
    variant: "bordered" as const,
  };

  switch (type) {
    case "string":
      return (
        <Input
          {...commonProps}
          name={name}
          placeholder={placeholder}
          value={value || ""}
          onValueChange={handleChange}
        />
      );

    case "number":
      return (
        <Input
          {...commonProps}
          name={name}
          type="number"
          placeholder={placeholder}
          value={value || ""}
          onValueChange={handleChange}
        />
      );

    case "date":
      return <DatePickerComponent {...commonProps} name={name} defaultValue={value} onChange={handleChange} />;

    case "boolean":
      return (
        <div className="flex items-center space-x-2 col-span-full">
          <CheckboxField name={name} defaultSelected={value} onChange={handleChange}>
            {label}
          </CheckboxField>
        </div>
      );

    case "textarea":
      return (
        <TrixEditorField
          label={label || ""}
          className="col-span-full"
          id={`${collectionName}_${itemIndex}_${name}`}
          name={name}
          value={value || ""}
          onChange={handleChange}
          enableAI={true}
          resumeContext={resumeData}
          itemContext={itemContext}
          schemaEntity={schemaEntity}
        />
      );

    default:
      return null;
  }
}
