import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/auth-clerk";
import { PADDLE_PRODUCTS } from "@/lib/paddle";

export async function POST(_request: NextRequest) {
  try {
    // Require authentication
    const user = await requireAuth();

    // Check if user is already premium
    if (user.isPremium) {
      return NextResponse.json({ error: "User already has premium access" }, { status: 400 });
    }

    // Return the checkout data for the client
    // The actual Paddle checkout will be initialized on the client side
    return NextResponse.json({
      success: true,
      data: {
        priceId: PADDLE_PRODUCTS.lifetime.priceId,
        customData: {
          userId: user.clerkId,
        },
        customer: {
          email: user.emailAddress,
        },
      },
    });
  } catch (error) {
    console.error("Checkout initialization error:", error);
    return NextResponse.json({ error: "Failed to initialize checkout" }, { status: 500 });
  }
}
