import { TemplateRegistry } from "@/components";
import { TemplateRendererProps } from "./types";

interface Props<T> extends TemplateRendererProps<T> {
  registry: TemplateRegistry<T>;
}

export function TemplateRenderer<T = any>({
  registry,
  templateSlug,
  templateProps,
  fallbackComponent: FallbackComponent,
  className = "",
}: Props<T>) {
  const templateEntry = registry.get(templateSlug);

  if (!templateEntry) {
    if (FallbackComponent) {
      return <FallbackComponent {...templateProps} className={className} />;
    }

    return (
      <div className={`template-error p-8 text-center text-gray-500 ${className}`}>
        Template not found: {templateSlug}
      </div>
    );
  }

  const { component: TemplateComponent } = templateEntry;

  return <TemplateComponent {...templateProps} className={className} />;
}
