"use client";

import { But<PERSON>, Toolt<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useSidebarStore } from "@/stores/sidebar-store";

export const SidebarToggle = () => {
  const { isCollapsed, setCollapsed } = useSidebarStore();

  const handleToggle = () => {
    setCollapsed(!isCollapsed);
  };

  return (
    <Tooltip content={isCollapsed ? "Expand sidebar" : "Collapse sidebar"} placement="right">
      <Button
        isIconOnly
        size="sm"
        radius="lg"
        variant="ghost"
        className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800"
        onPress={handleToggle}
      >
        <Icon
          icon={isCollapsed ? "tabler:chevrons-right" : "tabler:chevrons-left"}
          className="w-4 h-4 transition-transform duration-200"
        />
      </Button>
    </Tooltip>
  );
};
