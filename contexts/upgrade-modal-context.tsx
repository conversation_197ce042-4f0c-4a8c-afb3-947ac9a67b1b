"use client";

import { createContext, ReactNode, useContext, useState } from "react";
import { UpgradeModal } from "@/components/payment/upgrade-modal";

interface UpgradeModalContextType {
  showUpgradeModal: (feature?: "pdf" | "website" | "general") => void;
  hideUpgradeModal: () => void;
}

const UpgradeModalContext = createContext<UpgradeModalContextType | undefined>(undefined);

export function UpgradeModalProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [feature, setFeature] = useState<"pdf" | "website" | "general">("general");

  const showUpgradeModal = (featureType: "pdf" | "website" | "general" = "general") => {
    setFeature(featureType);
    setIsOpen(true);
  };

  const hideUpgradeModal = () => {
    setIsOpen(false);
  };

  return (
    <UpgradeModalContext.Provider value={{ showUpgradeModal, hideUpgradeModal }}>
      {children}
      <UpgradeModal isOpen={isOpen} onClose={hideUpgradeModal} feature={feature} />
    </UpgradeModalContext.Provider>
  );
}

export function useUpgradeModal() {
  const context = useContext(UpgradeModalContext);
  if (!context) {
    throw new Error("useUpgradeModal must be used within an UpgradeModalProvider");
  }
  return context;
}
