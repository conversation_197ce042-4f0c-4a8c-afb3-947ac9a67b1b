"use client";
import { Card, CardBody } from "@heroui/react";
import { motion } from "framer-motion";
import { subtitle, title } from "@/components/primitives";
import { features } from "@/lib/constants";

export default function FeaturesSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <section className="py-24 md:py-32 bg-gradient-to-b from-white to-default-50 dark:from-background dark:to-default-950/20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-16 items-start">
          <div className="sticky top-24 text-left">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
              <h2 className={title({ size: "lg" })}>Everything you need to succeed</h2>
              <p className={subtitle({ class: "mt-6 max-w-md text-lg" })}>
                Our platform is packed with features to help you create a standout resume and land your dream job.
              </p>
            </motion.div>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 gap-8"
          >
            {features.map((feature) => (
              <motion.div key={feature.key} variants={itemVariants}>
                <Card className="group border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white/60 dark:bg-default-100/60 backdrop-blur-sm">
                  <CardBody className="p-8 flex flex-row items-start gap-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-xl flex items-center justify-center text-3xl flex-shrink-0 mt-1">
                      {feature.icon}
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-xl font-bold text-default-900 mb-2">{feature.title}</h3>
                      <p className="text-default-600 leading-relaxed">{feature.description}</p>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
