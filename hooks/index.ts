// PostHog Feature Flags - Modern approach (recommended for new components)

// Legacy compatibility (for existing components)
// Simple feature access wrapper
export { useFeatureFlag, useFeatureFlags } from "./use-feature-flags";
export {
  type FeatureFlagKey,
  useAllFeatureFlags,
  useFeatureExperiment,
  useFeatureFlagEnabled,
  useFeatureFlagPayload,
  useFeatureFlagVariantKey,
  useFeatureRollout,
} from "./use-posthog-feature-flags";

// Existing feature system exports removed - migrated to PostHog-only implementation

// Analytics hooks
export {
  useAnalytics,
  useComponentAnalytics,
  useFeatureDemoAnalytics,
  usePerformanceMonitoring,
  usePremiumConversionAnalytics,
  useShowcaseAnalytics,
  useViralAnalytics,
} from "./use-analytics";
