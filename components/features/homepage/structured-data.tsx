export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "QuickCV",
    description:
      "Create professional, ATS-optimized resumes that help you land your dream job. Choose from 12+ templates with real-time editing and instant PDF export.",
    url: "https://quickcv.app",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "100",
      priceCurrency: "USD",
      description: "Professional resume builder with lifetime access",
    },
    creator: {
      "@type": "Organization",
      name: "QuickCV",
      url: "https://quickcv.app",
    },
    featureList: [
      "ATS-optimized resume templates",
      "Multi-language support",
      "Real-time editing",
      "PDF export",
      "Auto-save functionality",
      "Professional templates",
    ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      ratingCount: "2847",
      bestRating: "5",
      worstRating: "1",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "<PERSON>",
        },
        reviewBody: "QuickCV helped me land my dream job! The ATS optimization feature is a game-changer.",
      },
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "Ahmed Hassan",
        },
        reviewBody: "The multilingual support and RTL layout made it perfect for my Arabic resume.",
      },
    ],
  };

  return <script dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }} type="application/ld+json" />;
}
