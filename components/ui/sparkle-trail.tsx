"use client";

import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface SparkleParticle {
  id: string;
  x: number;
  y: number;
  size: number;
  delay: number;
  color: string;
}

interface SparkleTrailProps {
  x: number;
  y: number;
  duration?: number;
  particleCount?: number;
  onComplete?: () => void;
  className?: string;
}

export function SparkleTrail({
  x,
  y,
  duration = 2000,
  particleCount = 8,
  onComplete,
  className = "",
}: SparkleTrailProps) {
  const [sparkles, setSparkles] = useState<SparkleParticle[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Generate sparkle particles around the click position
    const newSparkles: SparkleParticle[] = [];
    const colors = ["#FFD700", "#FFA500", "#FF69B4", "#00CED1", "#98FB98"];

    for (let i = 0; i < particleCount; i++) {
      const angle = (i / particleCount) * Math.PI * 2;
      const distance = Math.random() * 40 + 20;

      newSparkles.push({
        id: `sparkle-${i}`,
        x: x + Math.cos(angle) * distance,
        y: y + Math.sin(angle) * distance,
        size: Math.random() * 12 + 8,
        delay: i * 0.1,
        color: colors[Math.floor(Math.random() * colors.length)],
      });
    }

    setSparkles(newSparkles);

    // Auto cleanup
    const timer = setTimeout(() => {
      setIsVisible(false);
      onComplete?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [x, y, particleCount, duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 pointer-events-none z-50 ${className}`}>
      <AnimatePresence>
        {sparkles.map((sparkle) => (
          <motion.div
            key={sparkle.id}
            initial={{
              x: x,
              y: y,
              scale: 0,
              opacity: 0,
              rotate: 0,
            }}
            animate={{
              x: sparkle.x,
              y: sparkle.y,
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
              rotate: 360,
            }}
            transition={{
              duration: duration / 1000,
              delay: sparkle.delay,
              ease: "easeOut",
            }}
            className="absolute"
            style={{
              width: sparkle.size,
              height: sparkle.size,
            }}
          >
            <Icon
              icon="heroicons:sparkles-20-solid"
              className="w-full h-full drop-shadow-lg"
              style={{ color: sparkle.color }}
            />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Central burst effect */}
      <motion.div
        initial={{ scale: 0, opacity: 1 }}
        animate={{ scale: [0, 1.5, 0], opacity: [1, 0.5, 0] }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="absolute w-16 h-16 rounded-full bg-gradient-radial from-yellow-300/30 to-transparent"
        style={{
          left: x - 32,
          top: y - 32,
        }}
      />
    </div>
  );
}

export default SparkleTrail;
