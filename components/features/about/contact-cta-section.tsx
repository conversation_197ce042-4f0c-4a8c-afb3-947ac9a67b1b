"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Icon } from "@iconify/react";
import Link from "next/link";

export default function ContactCTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-700 text-white">
      <div className="max-w-3xl mx-auto px-6 text-center">
        <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
        <p className="text-xl mb-8 text-blue-100">
          Join thousands of professionals who've transformed their careers with QuickCV.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/contact">
            <Button size="lg" className="bg-white text-blue-600 font-semibold px-8 py-3 hover:bg-blue-50">
              <Icon icon="tabler:mail" className="w-5 h-5 mr-2" />
              Contact Us
            </Button>
          </Link>
          <Link href="/resumes">
            <Button
              variant="bordered"
              size="lg"
              className="border-white text-white font-semibold px-8 py-3 hover:bg-white/10"
            >
              <Icon icon="tabler:rocket" className="w-5 h-5 mr-2" />
              Try QuickCV
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
