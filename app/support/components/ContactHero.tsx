import { Icon } from "@iconify/react";
import { subtitle, title } from "@/components/primitives";
import styles from "../styles/contact.module.css";

export default function ContactHero() {
  return (
    <div className={`${styles.heroSection} text-center`}>
      {/* Enhanced Support Badge with better dark mode colors */}
      <div className={styles.supportBadge}>
        <Icon icon="tabler:headset" className={styles.supportIcon} />
        <span className={styles.supportText}>24/7 Support Available</span>
      </div>

      {/* Enhanced title with gradient styling and improved typography */}
      <h1
        className={title({
          size: "lg",
          class: `mb-6 ${styles.heroTitle}`,
        })}
      >
        Get Help When You Need It
      </h1>

      {/* Enhanced description with better readability */}
      <p className={subtitle({ class: `mb-8 max-w-3xl mx-auto ${styles.heroDescription}` })}>
        Our support team is here to help you with any questions or issues. Get quick responses and expert assistance.
      </p>

      {/* Enhanced Quick Stats with better visual hierarchy */}
      <div className={styles.statsContainer}>
        <div className={styles.statItem}>
          <Icon icon="tabler:clock" className={`${styles.statIcon} ${styles.statIconGreen}`} />
          <span className={styles.statText}>Less than 4 hour response</span>
        </div>
        <div className={styles.statItem}>
          <Icon icon="tabler:users" className={`${styles.statIcon} ${styles.statIconBlue}`} />
          <span className={styles.statText}>Expert support team</span>
        </div>
        <div className={styles.statItem}>
          <Icon icon="tabler:star" className={`${styles.statIcon} ${styles.statIconYellow}`} />
          <span className={styles.statText}>98% satisfaction rate</span>
        </div>
      </div>
    </div>
  );
}
