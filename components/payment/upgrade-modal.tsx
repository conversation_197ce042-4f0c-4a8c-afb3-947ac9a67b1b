"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import type { Paddle } from "@paddle/paddle-js";
import { useCallback, useEffect, useState } from "react";
import { PREMIUM_FEATURES } from "@/config/payment-features";
import { initializePaddle, PADDLE_PRODUCTS } from "@/lib/paddle";

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature?: "pdf" | "website" | "general";
}

export function UpgradeModal({ isOpen, onClose, feature = "general" }: UpgradeModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paddle, setPaddle] = useState<Paddle | null>(null);

  // Initialize Paddle on component mount
  useEffect(() => {
    const initPaddle = async () => {
      const paddleInstance = await initializePaddle();
      if (paddleInstance) {
        setPaddle(paddleInstance);
      }
    };
    initPaddle();
  }, []);

  const handleCheckout = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get checkout data from our API
      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to initialize checkout");
      }

      const { data } = await response.json();

      if (!data.priceId) {
        throw new Error("Price ID is missing from checkout data");
      }

      // Initialize Paddle checkout
      if (!paddle) {
        throw new Error("Payment system not initialized");
      }

      // Start with minimal configuration
      const checkoutConfig: any = {
        items: [
          {
            priceId: data.priceId,
            quantity: 1,
          },
        ],
      };

      // Add customer email if available - Paddle might require this
      if (data.customer?.email) {
        // Ensure email is a valid string
        const email = data.customer.email.trim();
        if (email && email.includes("@")) {
          checkoutConfig.customer = {
            email: email,
          };
        }
      }

      // Add custom data with proper structure
      if (data.customData?.userId) {
        checkoutConfig.customData = {
          userId: String(data.customData.userId),
        };
      }

      // Add settings with success URL including transaction ID
      checkoutConfig.settings = {
        successUrl: `${window.location.origin}/payment/success?transaction={transaction_id}`,
        allowLogout: false, // Don't allow changing email if we have one
        showAddDiscounts: false, // Disable discount codes if not needed
      };

      paddle.Checkout.open(checkoutConfig);

      // Close the modal after opening checkout
      onClose();
    } catch (err) {
      console.error("Checkout error:", err);
      setError(err instanceof Error ? err.message : "Failed to start checkout");
    } finally {
      setIsLoading(false);
    }
  }, [paddle, onClose]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      classNames={{
        backdrop: "bg-black/50 backdrop-blur-sm",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-2xl font-bold">Upgrade to Premium</h2>
              <p className="text-sm text-default-500">Unlock all premium features with a one-time payment</p>
            </ModalHeader>
            <ModalBody>
              {/* Pricing Card */}
              <Card className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/50 dark:to-primary-800/50 border-primary-200 dark:border-primary-700">
                <CardBody className="text-center p-6">
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-5xl font-bold text-primary-700 dark:text-primary-200">$100</span>
                    <span className="text-xl text-default-600 dark:text-default-300">lifetime</span>
                  </div>
                  <p className="mt-2 text-default-600 dark:text-default-300">One-time payment, no subscription</p>
                </CardBody>
              </Card>

              {/* Features List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                {PREMIUM_FEATURES.map((feature, index) => (
                  <div key={index} className="flex gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Icon icon={feature.icon} className="w-5 h-5 text-primary" />
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold">{feature.titleKey}</h4>
                      <p className="text-sm text-default-500">{feature.descriptionKey}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Error Message */}
              {error && (
                <div className="mt-4 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg">
                  <div className="flex items-center gap-2 text-danger">
                    <Icon icon="lucide:alert-circle" className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                  </div>
                </div>
              )}

              {/* Feature-specific message */}
              {feature !== "general" && (
                <div className="mt-4 p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg">
                  <div className="flex items-center gap-2 text-warning-700 dark:text-warning">
                    <Icon icon="lucide:info" className="w-4 h-4" />
                    <span className="text-sm">
                      {feature === "pdf"
                        ? "PDF export is locked. Upgrade to download professional resumes."
                        : "Website publishing is locked. Upgrade to create your professional website."}
                    </span>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleCheckout}
                isLoading={isLoading}
                startContent={!isLoading && <Icon icon="lucide:credit-card" />}
              >
                Upgrade Now
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
