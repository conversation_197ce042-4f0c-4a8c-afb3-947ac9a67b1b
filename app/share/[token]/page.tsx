import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPublicResumeById } from "@/actions/public-resume";
import { PublicResumeView } from "@/components/features/sharing/public-resume-view";
import { getClientIP, hashIP } from "@/lib/ip-utils";
import { ShareTokenService } from "@/lib/share-token";

interface PublicResumePageProps {
  params: Promise<{
    token: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PublicResumePageProps): Promise<Metadata> {
  const { token } = await params;

  // Get client IP for rate limiting
  const clientIP = await getClientIP();
  const hashedIP = hashIP(clientIP);

  // Get resume ID from token with rate limiting
  const resumeId = await ShareTokenService.getResumeIdByToken(token, hashedIP);

  if (!resumeId) {
    return {
      title: "Resume Not Found",
      description: "The requested resume could not be found.",
    };
  }

  // Get resume data
  const result = await getPublicResumeById(resumeId);

  if (!result.success || !result.data) {
    return {
      title: "Resume Not Found",
      description: "The requested resume could not be found.",
    };
  }

  const resume = result.data;
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();
  const title = resume.jobTitle || fullName;
  const description = resume.bio
    ? resume.bio.replace(/<[^>]*>/g, "").substring(0, 160)
    : `Professional resume for ${fullName}`;

  const url = `${process.env.NEXT_PUBLIC_APP_URL || "https://quickcv.com"}/share/${token}`;

  return {
    title: `${title} | ${fullName} - QuickCV`,
    description,
    openGraph: {
      title: `${title} | ${fullName}`,
      description,
      url,
      siteName: "QuickCV",
      type: "profile",
      images: resume.photo
        ? [
            {
              url: resume.photo,
              width: 400,
              height: 400,
              alt: fullName,
            },
          ]
        : [],
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | ${fullName}`,
      description,
      images: resume.photo ? [resume.photo] : [],
    },
    alternates: {
      canonical: url,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function PublicResumePage({ params }: PublicResumePageProps) {
  const { token } = await params;

  // Get client IP for rate limiting
  const clientIP = await getClientIP();
  const hashedIP = hashIP(clientIP);

  // Get resume ID from token with rate limiting (this also increments view count)
  const resumeId = await ShareTokenService.getResumeIdByToken(token, hashedIP);

  if (!resumeId) {
    notFound();
  }

  // Fetch the resume data
  const result = await getPublicResumeById(resumeId);

  if (!result.success || !result.data) {
    notFound();
  }

  const resume = result.data;

  // Add structured data for SEO
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: fullName,
    jobTitle: resume.jobTitle,
    url: `${process.env.NEXT_PUBLIC_APP_URL || "https://quickcv.com"}/share/${token}`,
    ...(resume.email && { email: resume.email }),
    ...(resume.website && { url: resume.website }),
    ...(resume.photo && { image: resume.photo }),
    ...(resume.bio && {
      description: resume.bio.replace(/<[^>]*>/g, "").substring(0, 200),
    }),
    ...((resume.city || resume.country) && {
      address: {
        "@type": "PostalAddress",
        ...(resume.city && { addressLocality: resume.city }),
        ...(resume.country && { addressCountry: resume.country }),
      },
    }),
    ...(resume.experiences &&
      resume.experiences.length > 0 && {
        workExperience: resume.experiences.map((exp: any) => ({
          "@type": "WorkExperience",
          name: exp.title,
          description: exp.description?.replace(/<[^>]*>/g, ""),
          employer: {
            "@type": "Organization",
            name: exp.company,
          },
          ...(exp.startDate && { startDate: exp.startDate }),
          ...(exp.endDate && { endDate: exp.endDate }),
        })),
      }),
    ...(resume.educations &&
      resume.educations.length > 0 && {
        education: resume.educations.map((edu: any) => ({
          "@type": "EducationalOccupationalCredential",
          name: edu.degree,
          educationalCredentialAwarded: edu.degree,
          provider: {
            "@type": "EducationalOrganization",
            name: edu.institution,
          },
          ...(edu.startDate && { startDate: edu.startDate }),
          ...(edu.endDate && { endDate: edu.endDate }),
        })),
      }),
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Render the public resume view */}
      <PublicResumeView resume={resume} token={token} />
    </>
  );
}
