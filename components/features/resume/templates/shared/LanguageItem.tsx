import React from "react";
import { Language } from "@/db/schema";

export interface LanguageItemProps {
  language: Language;
  showLevel?: boolean;
  showBars?: boolean;
  className?: string;
}

export const LanguageItem: React.FC<LanguageItemProps> = ({
  language,
  showLevel = true,
  showBars = false,
  className = "",
}) => {
  const getProficiencyLevel = (proficiency: number): string => {
    if (proficiency >= 90) return "Native Speaker";
    if (proficiency >= 70) return "Fluent";
    if (proficiency >= 50) return "Intermediate";
    if (proficiency >= 30) return "Basic";
    return "";
  };

  return (
    <div className={`language-item flex justify-between items-center mb-2 ${className}`}>
      <span className="font-medium text-gray-800">{language.name}</span>
      {showLevel && <span className="text-gray-600 text-sm">{getProficiencyLevel(language.proficiency)}</span>}
      {showBars && (
        <div className="flex-1 mx-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${language.proficiency}%` }} />
          </div>
        </div>
      )}
    </div>
  );
};
