import type { ResumeSection } from "@/types/resume";

export interface LinkedInProfile {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  headline?: string;
  summary?: string;
  location?: string;
  emailAddress?: string;
  positions?: LinkedInPosition[];
  educations?: LinkedInEducation[];
  skills?: LinkedInSkill[];
  languages?: LinkedInLanguage[];
}

export interface LinkedInPosition {
  id: string;
  title: string;
  companyName: string;
  description?: string;
  startDate?: {
    month?: number;
    year?: number;
  };
  endDate?: {
    month?: number;
    year?: number;
  };
  isCurrent?: boolean;
  location?: string;
}

export interface LinkedInEducation {
  id: string;
  schoolName: string;
  degree?: string;
  fieldOfStudy?: string;
  startDate?: {
    month?: number;
    year?: number;
  };
  endDate?: {
    month?: number;
    year?: number;
  };
  description?: string;
}

export interface LinkedInSkill {
  name: string;
}

export interface LinkedInLanguage {
  name: string;
  proficiency?: string;
}

export class LinkedInService {
  private accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  private async makeRequest(endpoint: string): Promise<any> {
    const response = await fetch(`https://api.linkedin.com/v2${endpoint}`, {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`LinkedIn API error: ${response.status}`);
    }

    return response.json();
  }

  async getProfile(): Promise<LinkedInProfile> {
    const importedSections = {
      personalInfo: false,
      positions: 0,
      educations: 0,
      skills: 0,
    };

    let profile: any;
    try {
      // Get basic profile information
      profile = await this.makeRequest(
        "/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams),headline,summary,location)",
      );
      importedSections.personalInfo = true;
    } catch (error) {
      console.error("Failed to fetch basic profile:", error);
      throw new Error(
        "Unable to access LinkedIn profile. Please ensure you have authorized the application and your profile is accessible.",
      );
    }

    // Get email address
    let emailAddress: string | undefined;
    try {
      const emailResponse = await this.makeRequest("/emailAddress?q=members&projection=(elements*(handle~))");
      emailAddress = emailResponse?.elements?.[0]?.["handle~"]?.emailAddress;
    } catch (error) {
      console.warn("Could not fetch email address:", error);
      // Try alternative email endpoint
      try {
        const altEmailResponse = await this.makeRequest("/people/~/emailAddress");
        emailAddress = altEmailResponse?.emailAddress;
      } catch (altError) {
        console.warn("Alternative email fetch also failed:", altError);
      }
    }

    // Note: Work experience, education, and skills require r_fullprofile permission
    // which needs LinkedIn approval. Basic profile only includes name, headline, email, and photo.
    const positions: LinkedInPosition[] = [];
    const educations: LinkedInEducation[] = [];
    const skills: LinkedInSkill[] = [];

    // Log what was successfully imported
    console.log("LinkedIn import summary:", importedSections);

    return {
      id: profile.id,
      firstName: profile.firstName?.localized?.en_US || profile.firstName || "",
      lastName: profile.lastName?.localized?.en_US || profile.lastName || "",
      profilePicture: profile.profilePicture?.["displayImage~"]?.elements?.[0]?.identifiers?.[0]?.identifier,
      headline: profile.headline?.localized?.en_US || profile.headline || "",
      summary: profile.summary?.localized?.en_US || profile.summary || "",
      location: profile.location?.name || "",
      emailAddress,
      positions,
      educations,
      skills,
    };
  }
}

export function parseLinkedInToResume(linkedInProfile: LinkedInProfile): Partial<ResumeSection> {
  // Format dates
  const formatDate = (dateObj?: { month?: number; year?: number }): string => {
    if (!dateObj?.year) return "";
    const month = dateObj.month ? String(dateObj.month).padStart(2, "0") : "01";
    return `${dateObj.year}-${month}-01`;
  };

  // Parse work experiences
  const experiences =
    linkedInProfile.positions?.map((position) => ({
      jobTitle: position.title,
      company: position.companyName,
      description: position.description || "",
      startDate: formatDate(position.startDate),
      endDate: position.isCurrent ? "" : formatDate(position.endDate),
      location: position.location || "",
    })) || [];

  // Parse education
  const educations =
    linkedInProfile.educations?.map((education) => ({
      institution: education.schoolName,
      degree: education.degree || "",
      fieldOfStudy: education.fieldOfStudy || "",
      startDate: formatDate(education.startDate),
      endDate: formatDate(education.endDate),
      description: education.description || "",
    })) || [];

  // Parse skills
  const skills =
    linkedInProfile.skills?.map((skill) => ({
      name: skill.name,
      proficiency: 3, // Default proficiency (1-5 scale, 3 = Intermediate)
    })) || [];

  return {
    firstName: linkedInProfile.firstName,
    lastName: linkedInProfile.lastName,
    jobTitle: linkedInProfile.headline || "",
    bio: linkedInProfile.summary || "",
    email: linkedInProfile.emailAddress || "",
    city: linkedInProfile.location || "",
    experiences,
    educations,
    skills,
  };
}
