import { <PERSON><PERSON> } from "@heroui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/card";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import {
  ContactSupportClassificationSection,
  ContactSupportMessageDetailsSection,
  ContactSupportPersonalInfoSection,
} from "@/components/contact-support/ContactSupportFormFields";
import { ContactSupportFormMessage } from "@/components/contact-support/ContactSupportFormMessage";
import { contactSupportFormConfig, FormCategory, FormPriority } from "@/config/contact-support";
import FileUpload from "./FileUpload";

interface UploadedFile {
  name: string;
  url: string;
  size: number;
  type: string;
}

import styles from "../styles/contact.module.css";
import FormProgress from "./FormProgress";

interface ContactFormProps {
  categories: FormCategory[];
  priorities: FormPriority[];
  onSubmit: (formData: FormData) => Promise<void>;
  isLoading: boolean;
  message: { type: string; content: string };
  onClearMessage: () => void;
}

export default function ContactForm({
  categories,
  priorities,
  onSubmit,
  isLoading,
  message,
  onClearMessage,
}: ContactFormProps) {
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedPriority, setSelectedPriority] = useState("");
  const [attachments, setAttachments] = useState<UploadedFile[]>([]);
  const [responseTime, setResponseTime] = useState("2-4");
  // Removed uploadProgress state as it's now handled by FileUpload component
  const [showExpectedResponse, setShowExpectedResponse] = useState(false);
  const [formProgress, setFormProgress] = useState(0);

  // Calculate form progress
  const calculateProgress = () => {
    const form = document.getElementById("contact-form") as HTMLFormElement;
    if (!form) return;

    const formData = new FormData(form);
    const fields = ["name", "email", "subject", "message"];
    const filledFields = fields.filter((field) => {
      const value = formData.get(field)?.toString().trim();
      return value && value.length > 0;
    });
    const categorySelected = selectedCategory ? 1 : 0;
    const prioritySelected = selectedPriority ? 1 : 0;
    const progress = ((filledFields.length + categorySelected + prioritySelected) / (fields.length + 2)) * 100;
    setFormProgress(progress);
  };

  useEffect(() => {
    calculateProgress();
  }, [selectedCategory, selectedPriority]);

  // Update expected response time based on priority
  useEffect(() => {
    setResponseTime(contactSupportFormConfig.responseTimes[selectedPriority] || "2-4");
    setShowExpectedResponse(!!selectedPriority);
  }, [selectedPriority]);

  const handleFormSubmit = async (formData: FormData) => {
    onClearMessage();

    // Add category and priority to form data
    formData.append("category", selectedCategory);
    formData.append("priority", selectedPriority);

    // Handle file attachments - now using UploadThing URLs
    if (attachments.length > 0) {
      const attachmentUrls = attachments.map((file) => file.url);
      formData.append("attachmentUrls", JSON.stringify(attachmentUrls));
    }

    try {
      await onSubmit(formData);

      // Reset form state on success
      const form = document.getElementById("contact-form") as HTMLFormElement;
      form?.reset();
      setSelectedCategory("");
      setSelectedPriority("");
      setAttachments([]);
      setFormProgress(0);
    } catch (error) {
      // Error handled by parent component
    }
  };

  const handleAttachmentsChange = (newAttachments: UploadedFile[]) => {
    setAttachments(newAttachments);
  };

  const resetForm = () => {
    const form = document.getElementById("contact-form") as HTMLFormElement;
    form?.reset();
    setSelectedCategory("");
    setSelectedPriority("");
    setAttachments([]);
    onClearMessage();
    setFormProgress(0);
  };

  return (
    <div>
      <Card className={styles.formSection}>
        <CardHeader className="flex flex-col items-start">
          <div className="flex items-center justify-between w-full">
            <h2 className="text-xl font-bold">Contact Support</h2>
            <FormProgress progress={formProgress} />
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Fill out the form below and we'll get back to you as soon as possible
          </p>
        </CardHeader>
        <CardBody>
          <form id="contact-form" action={handleFormSubmit} className="space-y-6">
            {/* Personal Information */}
            <ContactSupportPersonalInfoSection onChange={calculateProgress} />

            {/* Issue Classification */}
            <ContactSupportClassificationSection
              categories={categories}
              priorities={priorities}
              selectedCategory={selectedCategory}
              selectedPriority={selectedPriority}
              onCategoryChange={(keys) => setSelectedCategory(Array.from(keys)[0] as string)}
              onPriorityChange={(keys) => setSelectedPriority(Array.from(keys)[0] as string)}
              showPriorityDescription={true}
            />

            {/* Expected Response Time */}
            {showExpectedResponse && (
              <div
                className={`${styles.expectedResponse} p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800`}
              >
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Icon icon="tabler:clock" className="w-4 h-4" />
                  <span className="font-medium text-sm">Expected response within {responseTime} hours</span>
                </div>
              </div>
            )}

            {/* Message Details */}
            <ContactSupportMessageDetailsSection onChange={calculateProgress} />

            {/* File Attachments */}
            <FileUpload
              attachments={attachments}
              onAttachmentsChange={handleAttachmentsChange}
              onMessage={onClearMessage}
            />

            {/* Status Messages */}
            <ContactSupportFormMessage message={message} showSuccessSteps={true} />

            {/* Submit Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Icon icon="tabler:shield-check" className="w-4 h-4" />
                <span>Your information is secure and private</span>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="bordered"
                  onPress={resetForm}
                  startContent={<Icon icon="tabler:refresh" className="w-4 h-4" />}
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  size="lg"
                  isLoading={isLoading}
                  isDisabled={formProgress < contactSupportFormConfig.validation.minFormProgress}
                  startContent={!isLoading && <Icon icon="tabler:send" className="w-4 h-4" />}
                  className={`${styles.submitButton} bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium px-8`}
                >
                  {isLoading ? "Sending..." : "Send Message"}
                </Button>
              </div>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}
