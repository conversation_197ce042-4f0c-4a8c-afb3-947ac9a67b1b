"use server";

import { auth } from "@clerk/nextjs/server";
import { validateContactSupportFormData } from "@/config/contact-support";
import { db } from "@/db";

// Shared function to get user ID from Clerk authentication
export async function getUserFromAuth() {
  const { userId } = await auth();
  let userIdValue = null;

  if (userId) {
    // Find user by clerk ID
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, userId),
    });
    userIdValue = user?.id || null;
  }

  return { userId, userIdValue };
}

// Shared function to validate and extract form data for contact/support forms
export async function validateAndExtractContactSupportFormData(
  formData: FormData,
  formType: "contact" | "support" = "contact",
) {
  const validation = validateContactSupportFormData(formData, formType);

  if (!validation.success) {
    return validation;
  }

  const { name, email, subject, message, category, priority } = validation.data!;
  const { userIdValue } = await getUserFromAuth();

  return {
    success: true,
    data: {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      subject: subject.trim(),
      message: message.trim(),
      category,
      priority,
      userId: userIdValue,
    },
  };
}

// Shared function to handle file attachments from UploadThing URLs for contact/support forms
export async function processContactSupportAttachments(formData: FormData) {
  let attachmentUrls: string[] = [];
  const attachmentUrlsData = formData.get("attachmentUrls") as string;

  if (attachmentUrlsData) {
    try {
      attachmentUrls = JSON.parse(attachmentUrlsData);

      // Validate URLs are from UploadThing
      for (const url of attachmentUrls) {
        if (!url.startsWith("https://utfs.io/")) {
          return { success: false, error: "Invalid file attachment detected." };
        }
      }
    } catch (error) {
      console.error("Attachment URL parsing error:", error);
      return { success: false, error: "Failed to process file attachments." };
    }
  }

  return { success: true, attachmentUrls };
}
