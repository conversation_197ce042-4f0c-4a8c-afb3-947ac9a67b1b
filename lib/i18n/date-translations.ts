// Date-related translations for internationalization
// This provides translations for date formatting and duration calculations

interface DateTranslations {
  present: string;
  expected: string;
  ongoing: string;
  current: string;
  lessThanMonth: string;
  month: string;
  months: string;
  year: string;
  years: string;
  duration: {
    oneMonth: string;
    multipleMonths: string;
    oneYear: string;
    multipleYears: string;
    yearAndMonth: string;
    yearsAndMonth: string;
    yearAndMonths: string;
    yearsAndMonths: string;
  };
}

// English translations
const EN_TRANSLATIONS: DateTranslations = {
  present: "Present",
  expected: "Expected",
  ongoing: "Ongoing", 
  current: "Current",
  lessThanMonth: "Less than a month",
  month: "month",
  months: "months",
  year: "year",
  years: "years",
  duration: {
    oneMonth: "1 month",
    multipleMonths: "{count} months",
    oneYear: "1 year",
    multipleYears: "{count} years", 
    yearAndMonth: "{years} year {months} month",
    yearsAndMonth: "{years} years {months} month",
    yearAndMonths: "{years} year {months} months",
    yearsAndMonths: "{years} years {months} months"
  }
};

// Arabic translations
const AR_TRANSLATIONS: DateTranslations = {
  present: "حتى الآن",
  expected: "متوقع",
  ongoing: "مستمر",
  current: "حالي",
  lessThanMonth: "أقل من شهر",
  month: "شهر",
  months: "أشهر", 
  year: "سنة",
  years: "سنوات",
  duration: {
    oneMonth: "شهر واحد",
    multipleMonths: "{count} أشهر",
    oneYear: "سنة واحدة",
    multipleYears: "{count} سنوات",
    yearAndMonth: "سنة و شهر",
    yearsAndMonth: "{years} سنوات و شهر",
    yearAndMonths: "سنة و {months} أشهر", 
    yearsAndMonths: "{years} سنوات و {months} أشهر"
  }
};

// Translation registry
const TRANSLATIONS: Record<string, DateTranslations> = {
  'en': EN_TRANSLATIONS,
  'en-US': EN_TRANSLATIONS,
  'ar': AR_TRANSLATIONS,
  'ar-SA': AR_TRANSLATIONS,
};

// Get translations for a specific locale
export const getDateTranslations = (locale: string): DateTranslations => {
  return TRANSLATIONS[locale] || TRANSLATIONS['en'];
};

// Format duration string with translations
export const formatDurationTranslation = (
  years: number,
  months: number,
  translations: DateTranslations
): string => {
  if (years === 0 && months === 0) {
    return translations.lessThanMonth;
  }

  if (years === 0) {
    return months === 1 
      ? translations.duration.oneMonth
      : translations.duration.multipleMonths.replace('{count}', months.toString());
  }

  if (months === 0) {
    return years === 1
      ? translations.duration.oneYear
      : translations.duration.multipleYears.replace('{count}', years.toString());
  }

  // Both years and months present
  if (years === 1 && months === 1) {
    return translations.duration.yearAndMonth
      .replace('{years}', years.toString())
      .replace('{months}', months.toString());
  } else if (years === 1) {
    return translations.duration.yearAndMonths
      .replace('{years}', years.toString())
      .replace('{months}', months.toString());
  } else if (months === 1) {
    return translations.duration.yearsAndMonth
      .replace('{years}', years.toString())
      .replace('{months}', months.toString());
  } else {
    return translations.duration.yearsAndMonths
      .replace('{years}', years.toString())
      .replace('{months}', months.toString());
  }
};

// Section titles translations
interface SectionTranslations {
  summary: string;
  experience: string;
  education: string;
  skills: string;
  languages: string;
  projects: string;
  certifications: string;
  profiles: string;
  references: string;
  volunteering: string;
  awards: string;
  hobbies: string;
}

const EN_SECTION_TRANSLATIONS: SectionTranslations = {
  summary: "Professional Summary",
  experience: "Professional Experience",
  education: "Education",
  skills: "Technical Skills",
  languages: "Languages",
  projects: "Projects",
  certifications: "Certifications",
  profiles: "Professional Profiles",
  references: "Professional References",
  volunteering: "Volunteer Experience",
  awards: "Awards and Achievements",
  hobbies: "Interests"
};

const AR_SECTION_TRANSLATIONS: SectionTranslations = {
  summary: "الملخص المهني",
  experience: "الخبرة المهنية",
  education: "التعليم",
  skills: "المهارات التقنية",
  languages: "اللغات",
  projects: "المشاريع", 
  certifications: "الشهادات",
  profiles: "الملفات المهنية",
  references: "المراجع المهنية",
  volunteering: "الأعمال التطوعية",
  awards: "الجوائز والإنجازات",
  hobbies: "الاهتمامات"
};

const SECTION_TRANSLATIONS: Record<string, SectionTranslations> = {
  'en': EN_SECTION_TRANSLATIONS,
  'en-US': EN_SECTION_TRANSLATIONS,
  'ar': AR_SECTION_TRANSLATIONS,
  'ar-SA': AR_SECTION_TRANSLATIONS,
};

export const getSectionTranslations = (locale: string): SectionTranslations => {
  return SECTION_TRANSLATIONS[locale] || SECTION_TRANSLATIONS['en'];
};