import { eq, lt, sql } from "drizzle-orm";
import { db } from "@/db";
import { type NewShareToken, type ShareToken, shareTokens } from "@/db/schema";
import { RateLimiterError, rateLimitManager } from "./rate-limiter";

/**
 * Service for managing resume sharing tokens
 * Provides secure, temporary URLs for resume sharing
 */
export class ShareTokenService {
  /**
   * Generate a cryptographically secure random token
   */
  static generateSecureToken(length: number = 14): string {
    const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";

    // Use crypto.getRandomValues for secure random generation
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);

    for (let i = 0; i < length; i++) {
      result += chars[array[i] % chars.length];
    }

    return result;
  }

  /**
   * Generate expiration date (30 days from now by default)
   */
  static generateExpirationDate(days: number = 30): string {
    const expireDate = new Date();
    expireDate.setDate(expireDate.getDate() + days);
    return expireDate.toISOString();
  }

  /**
   * Create or refresh a share token for a resume
   */
  static async createOrRefreshToken(resumeId: number): Promise<ShareToken | null> {
    try {
      // Check if a valid token already exists
      const existingToken = await db.select().from(shareTokens).where(eq(shareTokens.resumeId, resumeId)).limit(1);

      const now = new Date().toISOString();

      // If token exists and hasn't expired, return it
      if (existingToken[0] && existingToken[0].expiresAt > now) {
        return existingToken[0];
      }

      // Generate new token
      const token = ShareTokenService.generateSecureToken();
      const expiresAt = ShareTokenService.generateExpirationDate();

      const newTokenData: NewShareToken = {
        token,
        resumeId,
        expiresAt,
        viewCount: 0,
      };

      // If existing token, update it; otherwise create new
      if (existingToken[0]) {
        const [updatedToken] = await db
          .update(shareTokens)
          .set({
            token,
            expiresAt,
            viewCount: 0,
            updatedAt: now,
          })
          .where(eq(shareTokens.id, existingToken[0].id))
          .returning();

        return updatedToken;
      } else {
        const [createdToken] = await db.insert(shareTokens).values(newTokenData).returning();

        return createdToken;
      }
    } catch (error) {
      console.error("Error creating/refreshing share token:", error);
      // If database table doesn't exist yet, return null
      return null;
    }
  }

  /**
   * Get resume ID by token (and validate expiration)
   * Now includes rate limiting by IP address
   */
  static async getResumeIdByToken(token: string, ipAddress?: string): Promise<number | null> {
    // Apply rate limiting if IP address is provided
    if (ipAddress) {
      try {
        await rateLimitManager.checkShareTokenLimit(ipAddress);
      } catch (error) {
        if (error instanceof RateLimiterError) {
          console.warn(`Rate limit exceeded for IP ${ipAddress}: ${error.message}`);
          return null; // Return null to trigger not found, preventing abuse
        }
        throw error;
      }
    }

    const tokenRecord = await db.select().from(shareTokens).where(eq(shareTokens.token, token)).limit(1);

    if (!tokenRecord[0]) {
      return null;
    }

    const now = new Date().toISOString();

    // Check if token has expired
    if (tokenRecord[0].expiresAt <= now) {
      // Clean up expired token
      await ShareTokenService.deleteToken(token);
      return null;
    }

    // Increment view count
    await ShareTokenService.incrementViewCount(token);

    return tokenRecord[0].resumeId;
  }

  /**
   * Increment view count for a token
   */
  static async incrementViewCount(token: string): Promise<void> {
    await db
      .update(shareTokens)
      .set({
        viewCount: sql`${shareTokens.viewCount} + 1`,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(shareTokens.token, token));
  }

  /**
   * Delete a specific token
   */
  static async deleteToken(token: string): Promise<void> {
    await db.delete(shareTokens).where(eq(shareTokens.token, token));
  }

  /**
   * Clean up expired tokens (should be run periodically)
   */
  static async cleanupExpiredTokens(): Promise<number> {
    const now = new Date().toISOString();

    const deletedTokens = await db.delete(shareTokens).where(lt(shareTokens.expiresAt, now)).returning();

    return deletedTokens.length;
  }

  /**
   * Get all tokens for a resume (for analytics)
   */
  static async getTokensForResume(resumeId: number): Promise<ShareToken[]> {
    return await db.select().from(shareTokens).where(eq(shareTokens.resumeId, resumeId));
  }

  /**
   * Get token statistics for a resume
   */
  static async getTokenStats(resumeId: number): Promise<{
    totalViews: number;
    activeTokens: number;
    expiredTokens: number;
  }> {
    const tokens = await ShareTokenService.getTokensForResume(resumeId);
    const now = new Date().toISOString();

    const totalViews = tokens.reduce((sum, token) => sum + token.viewCount, 0);
    const activeTokens = tokens.filter((token) => token.expiresAt > now).length;
    const expiredTokens = tokens.filter((token) => token.expiresAt <= now).length;

    return {
      totalViews,
      activeTokens,
      expiredTokens,
    };
  }

  /**
   * Generate shareable URL for a resume
   */
  static async generateShareableUrl(
    resumeId: number,
    baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  ): Promise<string> {
    const tokenRecord = await ShareTokenService.createOrRefreshToken(resumeId);

    // If token creation failed (e.g., database table doesn't exist), fallback to old URL format
    if (!tokenRecord) {
      console.log("Token creation failed, falling back to resume ID URL");
      return `${baseUrl}/cv/${resumeId}`;
    }

    return `${baseUrl}/share/${tokenRecord.token}`;
  }
}
