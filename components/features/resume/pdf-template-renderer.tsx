"use client";

import React from "react";

// Direct template imports for client-side rendering
import {
  AzurillTemplate,
  BronzorTemplate,
  ChikoritaTemplate,
  DittoTemplate,
  GengarTemplate,
  GlalieTemplate,
  KakunaTemplate,
  LeafishTemplate,
  NosepassTemplate,
  OnyxTemplate,
  PikachuTemplate,
  RhyhornTemplate,
} from "@/components/features/resume/templates";

interface PDFTemplateRendererProps {
  resume: any;
  className?: string;
}

export function PDFTemplateRenderer({ resume, className }: PDFTemplateRendererProps) {
  // Client-side template mapping
  const templateComponents = {
    azurill: AzurillTemplate,
    bronzor: BronzorTemplate,
    chikorita: ChikoritaTemplate,
    ditto: DittoTemplate,
    gengar: GengarTemplate,
    glalie: GlalieTemplate,
    kakuna: KakunaTemplate,
    leafish: LeafishTemplate,
    nosepass: NosepassTemplate,
    onyx: OnyxTemplate,
    pikachu: PikachuTemplate,
    rhyhorn: RhyhornTemplate,
  };

  // Get template slug from resume templateId
  const templateSlug = resume.template?.slug || "bronzor";
  const TemplateComponent = templateComponents[templateSlug as keyof typeof templateComponents] || BronzorTemplate;

  return <TemplateComponent className={className} resume={resume} />;
}
