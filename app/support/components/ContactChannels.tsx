import { Badge } from "@heroui/badge";
import { But<PERSON> } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Icon } from "@iconify/react";
import { ContactChannel } from "@/config/contact";
import styles from "../styles/contact.module.css";

interface ContactChannelsProps {
  channels: ContactChannel[];
}

export default function ContactChannels({ channels }: ContactChannelsProps) {
  const handleChannelClick = (channel: ContactChannel) => {
    if (channel.action) {
      channel.action();
    } else if (channel.link) {
      window.open(channel.link, "_blank");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, channel: ContactChannel) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleChannelClick(channel);
    }
  };

  return (
    <div className="space-y-6">
      {/* Multiple Contact Channels */}
      <Card className="h-fit">
        <CardHeader className="flex flex-row items-center justify-between">
          <h2 className="text-xl font-bold">Contact Options</h2>
          <Chip size="sm" color="success" variant="flat">
            Available
          </Chip>
        </CardHeader>
        <CardBody className="space-y-4">
          {channels.map((channel) => (
            <div key={channel.id} className="relative">
              <div
                className={`${styles.contactChannel} flex items-start gap-4 p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-colors cursor-pointer group`}
                onClick={() => handleChannelClick(channel)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => handleKeyDown(e, channel)}
              >
                <div
                  className={`p-2 rounded-lg transition-colors ${
                    channel.primary
                      ? "bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50"
                      : "bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700"
                  }`}
                >
                  <Icon
                    icon={channel.icon}
                    className={`w-5 h-5 ${
                      channel.primary ? "text-blue-600 dark:text-blue-400" : "text-gray-600 dark:text-gray-400"
                    }`}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">{channel.title}</h3>
                    {channel.online !== undefined && (
                      <Badge content="" color={channel.online ? "success" : "default"} size="sm" placement="top-right">
                        <div className={`w-2 h-2 rounded-full ${channel.online ? "bg-green-500" : "bg-gray-400"}`} />
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{channel.description}</p>
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500">
                      <Icon icon="tabler:clock" className="w-3 h-3" />
                      <span>{channel.responseTime}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500">
                      <Icon icon="tabler:calendar" className="w-3 h-3" />
                      <span>{channel.availability}</span>
                    </div>
                  </div>
                </div>
                {channel.primary && (
                  <Chip size="sm" color="primary" variant="flat">
                    Recommended
                  </Chip>
                )}
              </div>
            </div>
          ))}
        </CardBody>
      </Card>

      {/* FAQ and Knowledge Base */}
      <Card>
        <CardBody className="text-center p-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon="tabler:help" className="w-8 h-8 text-white" />
          </div>
          <h3 className="font-bold mb-2">Need Quick Answers?</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Check our FAQ and help center for instant answers to common questions
          </p>
          <div className="space-y-2">
            <Button
              as="a"
              href="/support"
              color="primary"
              variant="flat"
              startContent={<Icon icon="tabler:book" className="w-4 h-4" />}
              fullWidth
            >
              Browse Help Center
            </Button>
            <Button
              as="a"
              href="/support#search"
              variant="bordered"
              startContent={<Icon icon="tabler:search" className="w-4 h-4" />}
              fullWidth
            >
              Search FAQs
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
