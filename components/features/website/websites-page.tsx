"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ody,
  CardH<PERSON><PERSON>,
  Chip,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  <PERSON>,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { UpgradeModal } from "@/components/payment";
import EmptyState from "@/components/shared/empty-state";
import { FullWebsite } from "@/db/schema";
import { type SortOption, useFilterSort } from "@/hooks/use-filter-sort";
import WebsitePageHeader from "./website-page-header";

interface WebsitesPageProps {
  websites: FullWebsite[];
}

export function WebsitesPage({ websites }: WebsitesPageProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<number | null>(null);
  const { isOpen: isUpgradeOpen, onOpen: onUpgradeOpen, onClose: onUpgradeClose } = useDisclosure();

  const utils = trpc.useUtils();
  const togglePublicMutation = trpc.websites.toggleWebsitePublic.useMutation();
  const deleteWebsiteMutation = trpc.websites.deleteWebsite.useMutation();

  // Use the custom hook for filtering and sorting
  const {
    filter,
    setFilter,
    sort,
    setSort,
    filteredAndSortedItems: filteredAndSortedWebsites,
    resetFilters,
  } = useFilterSort(websites, {
    filterFn: (website, filterValue): boolean => {
      switch (filterValue) {
        case "published":
          return !!website.isPublic;
        case "unpublished":
          return !website.isPublic;
        case "recent": {
          const createdDate = new Date(website.createdAt);
          const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);
          return daysSinceCreation <= 7;
        }
        default:
          return true;
      }
    },
    getItemName: (website) => website.resume?.title || "",
  });

  const handleTogglePublic = async (websiteId: number) => {
    setIsLoading(websiteId);
    try {
      const result = await togglePublicMutation.mutateAsync({ websiteId });
      if (result.success) {
        // Get the current website to determine the action
        const website = websites.find((w) => w.id === websiteId);
        toast.success(website?.isPublic ? "Website unpublished successfully" : "Website published successfully");
      }
      await utils.websites.getUserWebsites.invalidate();
      router.refresh();
    } catch (error: any) {
      console.error("Error toggling website public status:", error);
      // Check if it's a FORBIDDEN error (premium required)
      if (error?.data?.code === "FORBIDDEN") {
        onUpgradeOpen();
      } else {
        toast.error("Failed to update website status");
      }
    } finally {
      setIsLoading(null);
    }
  };

  const handleDeleteWebsite = async (websiteId: number) => {
    if (!confirm("Are you sure you want to delete this website?")) return;

    setIsLoading(websiteId);
    try {
      const result = await deleteWebsiteMutation.mutateAsync({ websiteId });
      if (result.success) {
        toast.success("Website deleted successfully");
      }
      await utils.websites.getUserWebsites.invalidate();
      router.refresh();
    } catch (error) {
      console.error("Error deleting website:", error);
      toast.error("Failed to delete website");
    } finally {
      setIsLoading(null);
    }
  };

  const getWebsiteUrl = (slug: string) => {
    return `${window.location.origin}/cv/${slug}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("URL copied to clipboard!");
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || "";
    const last = lastName?.charAt(0) || "";
    return (first + last).toUpperCase() || "W";
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <WebsitePageHeader
        websiteCount={websites?.length || 0}
        onFilterChange={setFilter}
        onSortChange={setSort}
        onExportAll={() => toast.success("Export feature coming soon!")}
        onReset={resetFilters}
        currentFilter={filter}
        currentSort={sort}
        filteredCount={filteredAndSortedWebsites.length}
        showCreateButton={true}
      />

      {websites?.length === 0 ? (
        <EmptyState
          icon="tabler:world-plus"
          title="No websites created yet"
          description="Create your first website to showcase your resume online"
          variant="gradient"
          size="lg"
          actionButton={
            <Button
              as={Link}
              href="/websites/builder"
              color="primary"
              size="lg"
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-200"
              startContent={<Icon icon="tabler:rocket" className="w-5 h-5" />}
            >
              Create Your First Website
            </Button>
          }
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedWebsites?.map((website) => (
            <Card
              key={website.id}
              className="group relative overflow-hidden hover:shadow-2xl transition-all duration-500 border border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50 backdrop-blur-xl hover:scale-[1.02] hover:border-purple-500/30 dark:hover:border-purple-400/30"
            >
              {/* Decorative gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none" />

              {/* Animated background pattern */}
              <div className="absolute inset-0 opacity-5 dark:opacity-10">
                <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-400 via-transparent to-transparent" />
              </div>

              <CardHeader className="pb-3 relative z-10">
                <div className="flex items-center gap-4 w-full">
                  <div className="relative">
                    <Avatar
                      name={getInitials(website.resume?.firstName, website.resume?.lastName)}
                      className="bg-gradient-to-br from-purple-600 to-blue-600 text-white shadow-lg ring-4 ring-white/20 dark:ring-gray-800/20"
                      size="lg"
                    />
                    {/* Status indicator */}
                    <div
                      className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
                        website.isPublic ? "bg-green-500" : "bg-gray-400"
                      } ${website.isPublic ? "animate-pulse" : ""}`}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-bold text-lg bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent truncate">
                      {`${website.resume?.firstName || ""} ${website.resume?.lastName || ""}`.trim() || "Untitled"}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate font-medium">
                      {website.resume?.jobTitle || "No job title"}
                    </p>
                  </div>
                  <Chip
                    size="sm"
                    variant="flat"
                    className={
                      website.isPublic
                        ? "bg-gradient-to-r from-emerald-400 to-green-500 text-white shadow-md animate-in fade-in zoom-in-95"
                        : "bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300"
                    }
                    startContent={
                      <Icon icon={website.isPublic ? "tabler:world-check" : "tabler:lock"} className="w-3 h-3" />
                    }
                  >
                    {website.isPublic ? "Published" : "Draft"}
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="pt-0 relative z-10">
                {/* Template Info Card */}
                <div className="mb-4 p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl border border-purple-200/30 dark:border-purple-700/30 group-hover:border-purple-300/50 dark:group-hover:border-purple-600/50 transition-colors duration-300">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="p-1 bg-purple-500/10 rounded-lg">
                      <Icon icon="tabler:layout-dashboard" className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="text-xs text-purple-700 dark:text-purple-300 uppercase tracking-wider font-semibold">
                      Template
                    </span>
                  </div>
                  <p className="font-bold text-gray-900 dark:text-gray-100 text-sm">
                    {website.websiteTemplate?.name || "Unknown Template"}
                  </p>
                </div>

                {/* Website URL Card */}
                {website.isPublic && (
                  <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl border border-blue-200/30 dark:border-blue-700/30 overflow-hidden group-hover:border-blue-300/50 dark:group-hover:border-blue-600/50 transition-colors duration-300">
                    <div className="flex items-center justify-between gap-2 mb-3">
                      <span className="text-xs text-blue-700 dark:text-blue-300 uppercase tracking-wider flex items-center gap-2 font-semibold">
                        <div className="p-1 bg-blue-500/10 rounded-lg">
                          <Icon icon="tabler:world-www" className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        Website URL
                      </span>
                      <Tooltip content="Copy URL" placement="left">
                        <Button
                          size="sm"
                          isIconOnly
                          variant="light"
                          className="min-w-unit-7 w-unit-7 h-unit-7 bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-700 transition-all duration-200 shadow-sm"
                          onPress={() => copyToClipboard(getWebsiteUrl(website.slug))}
                        >
                          <Icon icon="tabler:copy" className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </Button>
                      </Tooltip>
                    </div>
                    <Link
                      href={getWebsiteUrl(website.slug)}
                      target="_blank"
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium break-all block transition-colors duration-200 hover:underline decoration-2 underline-offset-2"
                    >
                      {getWebsiteUrl(website.slug)}
                    </Link>
                  </div>
                )}

                {/* Action Buttons with Enhanced Styling */}
                <div className="flex gap-2 mt-auto">
                  <Button
                    as={Link}
                    href={`/websites/${website.id}/builder`}
                    size="sm"
                    variant="flat"
                    className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg font-semibold group/btn"
                    startContent={
                      <Icon
                        icon="tabler:pencil"
                        className="w-4 h-4 group-hover/btn:rotate-12 transition-transform duration-300"
                      />
                    }
                  >
                    Edit
                  </Button>

                  <Dropdown>
                    <DropdownTrigger>
                      <Button
                        size="sm"
                        isIconOnly
                        variant="flat"
                        className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 transition-all duration-300 shadow-md hover:shadow-lg"
                      >
                        <Icon icon="tabler:dots-vertical" className="w-4 h-4" />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="Website actions" className="min-w-[200px]">
                      <DropdownItem
                        key="view"
                        startContent={<Icon icon="tabler:external-link" className="w-4 h-4" />}
                        as={Link}
                        href={getWebsiteUrl(website.slug)}
                        target="_blank"
                        className={
                          !website.isPublic
                            ? "opacity-50 pointer-events-none"
                            : "hover:bg-blue-50 dark:hover:bg-blue-900/20"
                        }
                      >
                        View Website
                      </DropdownItem>
                      <DropdownItem
                        key="toggle"
                        startContent={
                          <Icon icon={website.isPublic ? "tabler:eye-off" : "tabler:eye"} className="w-4 h-4" />
                        }
                        onPress={() => handleTogglePublic(website.id)}
                        className={
                          isLoading === website.id ? "opacity-50" : "hover:bg-purple-50 dark:hover:bg-purple-900/20"
                        }
                      >
                        {website.isPublic ? "Unpublish" : "Publish"}
                      </DropdownItem>
                      <DropdownItem
                        key="analytics"
                        startContent={<Icon icon="tabler:chart-bar" className="w-4 h-4" />}
                        className="opacity-50 pointer-events-none"
                      >
                        Analytics (Coming Soon)
                      </DropdownItem>
                      <DropdownItem
                        key="delete"
                        className="text-danger hover:bg-danger-50 dark:hover:bg-danger-900/20"
                        color="danger"
                        startContent={<Icon icon="tabler:trash" className="w-4 h-4" />}
                        onPress={() => handleDeleteWebsite(website.id)}
                      >
                        Delete
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}

      {/* Upgrade Modal */}
      <UpgradeModal isOpen={isUpgradeOpen} onClose={onUpgradeClose} feature="website" />
    </div>
  );
}
