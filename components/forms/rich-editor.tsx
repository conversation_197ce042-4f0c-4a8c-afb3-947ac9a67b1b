"use client";
import { But<PERSON>, cn } from "@heroui/react";
import { Icon } from "@iconify/react";
import Color from "@tiptap/extension-color";
import ListItem from "@tiptap/extension-list-item";
import Placeholder from "@tiptap/extension-placeholder";
import TextStyle from "@tiptap/extension-text-style";
import { Editor, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useEffect, useState } from "react";
import { aiService } from "@/lib/ai-service";
import { AIDescriptionModal } from "./ai-description-modal";

interface RichEditorProps {
  value: string;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  readOnly?: boolean;
  id?: string;
  name?: string;
  onChange?: (value: string) => void;
  // AI Integration props
  enableAI?: boolean;
  fieldName?: string;
  resumeContext?: any;
  itemContext?: any;
  schemaEntity?: string;
}

export function RichEditor({
  value,
  name,
  placeholder = "Write something...",
  className,
  minHeight = "200px",
  readOnly = false,
  id,
  onChange,
  enableAI = false,
  fieldName,
  resumeContext,
  itemContext,
  schemaEntity,
}: RichEditorProps) {
  const [content, setContent] = useState(value);

  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      TextStyle,
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false, // Marks are not preserved when attributes are kept
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false, // Marks are not preserved when attributes are kept
        },
      }),

      Placeholder.configure({
        placeholder,
      }),
    ],
    editorProps: {
      attributes: {
        class: "prose prose-sm dark:prose-invert max-w-none w-full h-full min-h-[200px] p-2 wysiwyg",
      },
    },
    content: value,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
      if (onChange) {
        onChange(html);
      }
    },
  });

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      // Use a timeout to prevent rapid updates
      const timeoutId = setTimeout(() => {
        editor.commands.setContent(value || "");
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [value, editor]);

  if (!editor) {
    return null;
  }

  return (
    <div
      className={cn(
        "!w-full !max-w-none rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800",
        className,
      )}
    >
      <div className="p-4 focus:outline-none !w-full !max-w-none relative" style={{ minHeight }}>
        <MenuBar
          editor={editor}
          enableAI={enableAI}
          fieldName={fieldName}
          resumeContext={resumeContext}
          itemContext={itemContext}
          schemaEntity={schemaEntity}
          onAIContentGenerated={(content) => {
            editor.commands.setContent(content);
            if (onChange) {
              onChange(content);
            }
          }}
        />
        <div className="!w-full !max-w-none relative">
          <EditorContent
            className="tiptop text-gray-900 dark:text-gray-100 !w-full !max-w-none pb-16"
            editor={editor}
            id={id}
          />

          {/* Floating AI Button inside editor area */}
          <FloatingAIButton
            editor={editor}
            enableAI={enableAI}
            fieldName={fieldName}
            resumeContext={resumeContext}
            itemContext={itemContext}
            schemaEntity={schemaEntity}
            onAIContentGenerated={(content) => {
              editor.commands.setContent(content);
              if (onChange) {
                onChange(content);
              }
            }}
          />
        </div>

        {/* Hidden input to include content in form submission */}
        <input name={name} type="hidden" value={content || ""} />
      </div>
    </div>
  );
}

interface MenuBarProps {
  editor: Editor;
  enableAI?: boolean;
  fieldName?: string;
  resumeContext?: any;
  itemContext?: any;
  schemaEntity?: string;
  onAIContentGenerated?: (content: string) => void;
}

const MenuBar = ({
  editor,
  enableAI = false,
  fieldName,
  resumeContext,
  itemContext,
  schemaEntity,
  onAIContentGenerated,
}: MenuBarProps) => {
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);

  const contextType = aiService.getContextTypeFromField(fieldName || "", schemaEntity);
  const extractedResumeContext = aiService.extractResumeContext(resumeContext);
  const extractedItemContext = aiService.extractItemContext(itemContext, contextType);

  // React Compiler will optimize this function automatically
  const handleAIGenerated = (description: string) => {
    if (onAIContentGenerated) {
      onAIContentGenerated(description);
    }
  };

  const isAIAvailable = enableAI && fieldName && aiService.isAIAvailableForField(fieldName);

  return (
    <div className="!w-full !max-w-none flex flex-wrap items-center gap-1 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 p-1 rounded-t-md">
      <Button
        className={editor.isActive("bold") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : ""}
        size="sm"
        title="Bold"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleBold().run()}
      >
        <Icon height={16} icon="mdi:format-bold" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("paragraph") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : ""
        }
        size="sm"
        title="Paragraph"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().setParagraph().run()}
      >
        <Icon height={16} icon="mdi:format-paragraph" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 1 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 1"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-1" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 2 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 2"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-2" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 3 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 3"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-3" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 4 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 4"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 4 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-4" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 5 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 5"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 5 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-5" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("heading", { level: 6 })
            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            : ""
        }
        size="sm"
        title="Heading 6"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 6 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-6" width={16} />
      </Button>
      <Button
        className={editor.isActive("italic") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : ""}
        size="sm"
        title="Italic"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleItalic().run()}
      >
        <Icon height={16} icon="mdi:format-italic" width={16} />
      </Button>
      <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
      <Button
        className={
          editor.isActive("bulletList") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : ""
        }
        size="sm"
        title="Bullet List"
        type="button"
        variant="ghost"
        onPress={() => {
          editor.chain().focus().toggleBulletList().run();
        }}
      >
        <Icon height={16} icon="mdi:format-list-bulleted" width={16} />
      </Button>
      <Button
        className={
          editor.isActive("orderedList") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : ""
        }
        size="sm"
        title="Ordered List"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleOrderedList().run()}
      >
        <Icon height={16} icon="mdi:format-list-numbered" width={16} />
      </Button>

      <Button
        disabled={!editor.can().undo()}
        size="sm"
        title="Undo"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().undo().run()}
      >
        <Icon height={16} icon="mdi:undo" width={16} />
      </Button>
      <Button
        disabled={!editor.can().redo()}
        size="sm"
        title="Redo"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().redo().run()}
      >
        <Icon height={16} icon="mdi:redo" width={16} />
      </Button>

      {/* AI Modal */}
      {isAIAvailable && (
        <AIDescriptionModal
          isOpen={isAIModalOpen}
          onClose={() => setIsAIModalOpen(false)}
          onAccept={handleAIGenerated}
          contextType={contextType}
          resumeContext={extractedResumeContext}
          itemContext={extractedItemContext}
          currentDescription={editor.getHTML()}
          language="en"
        />
      )}
    </div>
  );
};

interface FloatingAIButtonProps {
  editor: Editor;
  enableAI?: boolean;
  fieldName?: string;
  resumeContext?: any;
  itemContext?: any;
  schemaEntity?: string;
  onAIContentGenerated?: (content: string) => void;
}

const FloatingAIButton = ({
  editor,
  enableAI = false,
  fieldName,
  resumeContext,
  itemContext,
  schemaEntity,
  onAIContentGenerated,
}: FloatingAIButtonProps) => {
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const contextType = aiService.getContextTypeFromField(fieldName || "", schemaEntity);
  const extractedResumeContext = aiService.extractResumeContext(resumeContext);
  const extractedItemContext = aiService.extractItemContext(itemContext, contextType);

  // React Compiler will optimize this function automatically
  const handleAIGenerated = (description: string) => {
    if (onAIContentGenerated) {
      onAIContentGenerated(description);
    }
  };

  const isAIAvailable = enableAI && fieldName && aiService.isAIAvailableForField(fieldName);
  const isEmpty = editor.isEmpty;
  const shouldShow = isAIAvailable && (isEmpty || isFocused);

  // Track editor focus state
  useEffect(() => {
    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    editor.on("focus", handleFocus);
    editor.on("blur", handleBlur);

    return () => {
      editor.off("focus", handleFocus);
      editor.off("blur", handleBlur);
    };
  }, [editor]);

  if (!shouldShow) {
    return null;
  }

  return (
    <>
      {/* Floating AI Button */}
      <div className="absolute bottom-4 right-4 z-10 animate-in fade-in slide-in-from-bottom-2 duration-300">
        <Button
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 backdrop-blur-sm"
          size="lg"
          onPress={() => setIsAIModalOpen(true)}
          startContent={<Icon height={20} icon="mdi:magic-staff" width={20} />}
        >
          Write with AI
        </Button>
      </div>

      {/* AI Modal */}
      <AIDescriptionModal
        isOpen={isAIModalOpen}
        onClose={() => setIsAIModalOpen(false)}
        onAccept={handleAIGenerated}
        contextType={contextType}
        resumeContext={extractedResumeContext}
        itemContext={extractedItemContext}
        currentDescription={editor.getHTML()}
        language="en"
      />
    </>
  );
};
