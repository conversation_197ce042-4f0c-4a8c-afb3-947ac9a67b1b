"use client";

import { SignedIn, SignedOut, SignInButton } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import NextLink from "next/link";
import { useEffect, useState } from "react";
import { Logo } from "@/components/shared/common/icons";
import { CustomUserButton } from "@/components/shared/custom-user-button";
import { useSidebarStore } from "@/stores/sidebar-store";

export const MobileHeader = () => {
  const { toggle, isMobile } = useSidebarStore();
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Only show on mobile
  if (!isMobile) {
    return null;
  }

  return (
    <>
      <Card
        className={`
          fixed top-0 left-0 right-0 z-50 transition-all duration-300
          ${
            scrolled
              ? "bg-white/80 dark:bg-gray-900/80 shadow-lg border-b border-gray-200/20 dark:border-gray-700/20"
              : "bg-transparent"
          } backdrop-blur-lg
        `}
        radius="none"
      >
        <div className="flex items-center justify-between p-4">
          {/* Logo */}
          <NextLink href="/" className="flex items-center gap-2">
            <div className="relative">
              <Logo className="w-8 h-8" />
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30" />
            </div>
            <p className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              QuickCV
            </p>
          </NextLink>

          {/* Right side buttons */}
          <div className="flex items-center gap-2">
            <SignedOut>
              <SignInButton mode="modal">
                <Button
                  color="primary"
                  size="sm"
                  variant="shadow"
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                  isIconOnly
                >
                  <Icon icon="tabler:login" className="w-4 h-4" />
                </Button>
              </SignInButton>
            </SignedOut>
            <SignedIn>
              <CustomUserButton size="sm" />
            </SignedIn>

            {/* Hamburger Menu Button */}
            <Button
              isIconOnly
              size="sm"
              radius="lg"
              variant="ghost"
              className="text-gray-700 dark:text-gray-300"
              onPress={toggle}
            >
              <Icon icon="tabler:menu-2" className="w-6 h-6" />
            </Button>
          </div>
        </div>
      </Card>

      {/* Animated gradient line */}
      <div
        className="fixed top-16 left-0 right-0 h-[2px] w-full bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 transition-opacity duration-300 z-40"
        style={{ opacity: scrolled ? 0.5 : 0 }}
      />
    </>
  );
};
