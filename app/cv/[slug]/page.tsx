import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPublicWebsite } from "@/actions/websites";
import { getWebsiteTemplate } from "@/components/features/website/templates/template-registry";
import { FullResume } from "@/db/schema";

interface PublicWebsitePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PublicWebsitePageProps): Promise<Metadata> {
  const { slug } = await params;
  const result = await getPublicWebsite(slug);

  if (!result.success || !result.data) {
    return {
      title: "CV Not Found",
      description: "The requested CV could not be found.",
    };
  }

  const { website } = result.data;
  const { resume } = website;
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();
  const title = resume.jobTitle || fullName;
  const description = resume.bio
    ? resume.bio.replace(/<[^>]*>/g, "").substring(0, 160)
    : `Professional CV for ${fullName}`;

  const url = `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/${slug}`;

  return {
    title: `${title} | ${fullName}`,
    description,
    openGraph: {
      title: `${title} | ${fullName}`,
      description,
      url,
      siteName: "QuickCV",
      type: "profile",
      images: resume.photo
        ? [
            {
              url: resume.photo,
              width: 400,
              height: 400,
              alt: fullName,
            },
          ]
        : [],
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | ${fullName}`,
      description,
      images: resume.photo ? [resume.photo] : [],
    },
    alternates: {
      canonical: url,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function PublicWebsitePage({ params }: PublicWebsitePageProps) {
  const { slug } = await params;

  // Fetch the public website data
  const result = await getPublicWebsite(slug);

  if (!result.success || !result.data) {
    notFound();
  }

  const { website, resume } = result.data;
  const { websiteTemplate } = website;

  // Get the appropriate template component
  const TemplateComponent = getWebsiteTemplate(websiteTemplate?.slug || "");

  if (!TemplateComponent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Template Not Available</h1>
          <p className="text-gray-600">The requested template is not available or has been removed.</p>
        </div>
      </div>
    );
  }

  // Add structured data for SEO
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: fullName,
    jobTitle: resume.jobTitle,
    url: `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/${slug}`,
    ...(resume.email && { email: resume.email }),
    ...(resume.website && { url: resume.website }),
    ...(resume.photo && { image: resume.photo }),
    ...(resume.bio && {
      description: resume.bio.replace(/<[^>]*>/g, "").substring(0, 200),
    }),
    ...((resume.city || resume.country) && {
      address: {
        "@type": "PostalAddress",
        ...(resume.city && { addressLocality: resume.city }),
        ...(resume.country && { addressCountry: resume.country }),
      },
    }),
    ...(resume.experiences &&
      resume.experiences.length > 0 && {
        workExperience: resume.experiences.map((exp: any) => ({
          "@type": "WorkExperience",
          name: exp.title,
          description: exp.description?.replace(/<[^>]*>/g, ""),
          employer: {
            "@type": "Organization",
            name: exp.company,
          },
          ...(exp.startDate && { startDate: exp.startDate }),
          ...(exp.endDate && { endDate: exp.endDate }),
        })),
      }),
    ...(resume.educations &&
      resume.educations.length > 0 && {
        education: resume.educations.map((edu: any) => ({
          "@type": "EducationalOccupationalCredential",
          name: edu.degree,
          educationalCredentialAwarded: edu.degree,
          provider: {
            "@type": "EducationalOrganization",
            name: edu.institution,
          },
          ...(edu.startDate && { startDate: edu.startDate }),
          ...(edu.endDate && { endDate: edu.endDate }),
        })),
      }),
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Render the website template */}
      <TemplateComponent resume={resume as FullResume} website={website} />
    </>
  );
}
