"use client";

import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface HeartParticle {
  id: string;
  x: number;
  y: number;
  size: number;
  delay: number;
  color: string;
  drift: number;
}

interface FloatingHeartsProps {
  count?: number;
  duration?: number;
  onComplete?: () => void;
  colors?: string[];
  className?: string;
}

const DEFAULT_HEART_COLORS = [
  "#FF69B4", // Hot Pink
  "#FF1493", // Deep Pink
  "#FF6347", // Tomato
  "#FFB6C1", // Light Pink
  "#FFC0CB", // Pink
  "#FF91A4", // Salmon Pink
];

export function FloatingHearts({
  count = 10,
  duration = 4000,
  onComplete,
  colors = DEFAULT_HEART_COLORS,
  className = "",
}: FloatingHeartsProps) {
  const [hearts, setHearts] = useState<HeartParticle[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Generate floating hearts
    const newHearts: HeartParticle[] = [];
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    for (let i = 0; i < count; i++) {
      newHearts.push({
        id: `heart-${i}`,
        x: Math.random() * screenWidth,
        y: screenHeight + 50, // Start below screen
        size: Math.random() * 20 + 20,
        delay: i * 0.2,
        color: colors[Math.floor(Math.random() * colors.length)],
        drift: (Math.random() - 0.5) * 100, // Horizontal drift
      });
    }

    setHearts(newHearts);

    // Auto cleanup
    const timer = setTimeout(() => {
      setIsVisible(false);
      onComplete?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [count, colors, duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 pointer-events-none z-40 overflow-hidden ${className}`}>
      <AnimatePresence>
        {hearts.map((heart) => (
          <motion.div
            key={heart.id}
            initial={{
              x: heart.x,
              y: heart.y,
              scale: 0,
              opacity: 0,
              rotate: 0,
            }}
            animate={{
              x: heart.x + heart.drift,
              y: -100, // Float up and off screen
              scale: [0, 1, 0.8],
              opacity: [0, 1, 0],
              rotate: [0, 15, -15, 0],
            }}
            transition={{
              duration: duration / 1000,
              delay: heart.delay,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="absolute"
            style={{
              width: heart.size,
              height: heart.size,
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <Icon
                icon="heroicons:heart-20-solid"
                className="w-full h-full drop-shadow-lg"
                style={{ color: heart.color }}
              />
            </motion.div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Gentle background glow */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.1, 0] }}
        transition={{ duration: 2, repeat: 2 }}
        className="absolute inset-0 bg-gradient-radial from-pink-300/10 to-transparent"
      />
    </div>
  );
}

export default FloatingHearts;
