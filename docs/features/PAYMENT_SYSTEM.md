# QuickCV Payment System Documentation

## Overview

This document outlines the implementation of a lifetime premium payment system for QuickCV using Paddle as the payment gateway. Users can upgrade for a one-time payment of $100 to unlock PDF export and website publishing features.

## Features

### Free Users
- Create unlimited resumes
- Edit and customize resumes
- Use all templates
- Preview resumes
- Create website drafts (private only)

### Premium Users ($100 Lifetime)
- All free features
- **PDF Export**: Download resumes as professional PDFs
- **Website Publishing**: Publish resume websites publicly
- **Future Features**: Access to all upcoming premium features

## Technical Implementation

### Database Changes

Added premium-related fields to the `users` table:
```sql
ALTER TABLE users ADD is_premium integer DEFAULT false NOT NULL;
ALTER TABLE users ADD purchased_at text;
ALTER TABLE users ADD payment_id text;
ALTER TABLE users ADD payment_gateway text;
```

### Key Components

#### 1. Payment Gateway Integration
- **File**: `lib/paddle.ts`
- **Purpose**: Paddle client initialization and configuration
- **Features**: Environment-based setup (sandbox/production)
- **Note**: Uses async initialization with `initializePaddle()` from Paddle SDK

#### 2. API Endpoints

**Checkout Endpoint** (`/api/checkout`)
- Initializes payment session
- Returns checkout data for Paddle
- Validates user authentication

**Webhook Endpoint** (`/api/webhook/paddle`)
- Handles payment confirmations from Paddle
- Updates user premium status
- Processes refunds (revokes premium access)
- Verifies webhook signatures for security

#### 3. Premium Access Control

**PDF Export Protection** (`/api/pdf/route.ts`)
```typescript
// Check if user has premium access
if (!user.isPremium) {
    return NextResponse.json(
        { error: "Premium subscription required for PDF export" },
        { status: 403 }
    );
}
```

**Website Publishing Protection** (`/app/server/routers/websites.ts`)
```typescript
// Check if user is trying to publish and doesn't have premium
if (newPublicStatus === 1 && !ctx.user.isPremium) {
    throw new TRPCError({
        code: "FORBIDDEN",
        message: "Premium subscription required to publish websites",
    });
}
```

#### 4. UI Components

**UpgradeModal** (`components/payment/upgrade-modal.tsx`)
- Displays pricing and features
- Integrates with Paddle checkout using correct API structure
- Context-aware messaging (PDF vs Website features)
- Handles async Paddle initialization
- Success URL configured in `settings.successUrl`

**PremiumBadge** (`components/payment/premium-badge.tsx`)
- Visual indicator for premium users
- Can be added to navbar or user profile

#### 5. User Experience Flow

1. Free user attempts premium feature (PDF export/website publish)
2. System shows upgrade modal with benefits
3. User clicks "Upgrade Now" → Paddle checkout opens
4. User completes payment → Webhook updates database
5. User redirected to success page → Features immediately unlocked

## Testing Guide

### Prerequisites

1. **Paddle Account Setup**
   - Create a Paddle account at https://paddle.com
   - Set up a test environment (sandbox)
   - Create a product for "QuickCV Premium - Lifetime" ($100)
   - Get the following credentials:
     - Client token (public key)
     - API key (secret)
     - Webhook secret
     - Product ID and Price ID

2. **Environment Configuration**

Add to your `.env` file:
```env
# Paddle Payment Configuration
NEXT_PUBLIC_PADDLE_ENVIRONMENT=sandbox
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=your_paddle_client_token_here
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret_here
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRICE_ID=your_paddle_pro_monthly_price_id_here
NEXT_PUBLIC_PADDLE_PRO_MONTHLY_PRODUCT_ID=your_paddle_pro_monthly_product_id_here
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRICE_ID=your_paddle_pro_yearly_price_id_here
NEXT_PUBLIC_PADDLE_PRO_YEARLY_PRODUCT_ID=your_paddle_pro_yearly_product_id_here
```

### Testing Steps

#### 1. Database Setup
```bash
# Ensure migrations are applied
bun run db:migrate

# Verify the users table has new columns
bun run db:studio
# Check that users table includes: plan_id, subscription_id, payment_id, payment_gateway
```

#### 2. Basic Functionality Test

1. **Start the development server**
   ```bash
   bun run dev
   ```

2. **Test Free User Experience**
   - Sign up/login as a new user
   - Create a resume
   - Try to export PDF → Should show upgrade modal
   - Try to publish website → Should show upgrade modal

3. **Test Upgrade Modal**
   - Verify modal displays correctly with pricing
   - Check that feature-specific messages appear
   - Ensure "Upgrade Now" button is present

#### 3. Payment Flow Testing

1. **Webhook Setup** (Required for testing)
   - Use ngrok or similar to expose your local server:
     ```bash
     ngrok http 3000
     ```
   - In Paddle dashboard, set webhook URL to:
     `https://your-ngrok-url.ngrok.io/api/webhook/paddle`
   - Enable webhook events: `transaction.completed`, `transaction.updated`

2. **Test Payment Flow**
   - Click "Upgrade Now" in modal
   - Verify Paddle checkout opens
   - Use Paddle test card: `4000 0000 0000 0002`
   - Complete the payment process
   - Should redirect to `/payment/success`

3. **Verify Premium Activation**
   - Check database: user's `is_premium` should be `true`
   - Try PDF export → Should work without modal
   - Try website publishing → Should work without modal

#### 4. API Endpoint Testing

**Test Checkout Endpoint**
```bash
# Test authenticated request
curl -X POST http://localhost:3000/api/checkout \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie"

# Should return checkout data with priceId
```

**Test Webhook Endpoint**
```bash
# Test webhook (use actual Paddle webhook format)
curl -X POST http://localhost:3000/api/webhook/paddle \
  -H "Content-Type: application/json" \
  -H "Paddle-Signature: test-signature" \
  -d '{"event_type": "transaction.completed", "data": {...}}'
```

#### 5. Error Handling Tests

1. **Invalid Payment Scenarios**
   - Test with expired/invalid cards
   - Verify error messages display correctly
   - Ensure user remains on free tier

2. **Network Issues**
   - Test with poor connection
   - Verify graceful degradation
   - Check retry mechanisms

3. **Webhook Failures**
   - Test invalid signatures
   - Test malformed payloads
   - Verify security measures

#### 6. User Interface Testing

1. **Responsive Design**
   - Test upgrade modal on mobile/tablet/desktop
   - Verify payment success page responsive design

2. **Accessibility**
   - Test keyboard navigation through upgrade flow
   - Verify screen reader compatibility
   - Check color contrast for premium badges

3. **Internationalization**
   - Test with different locales (if applicable)
   - Verify payment amounts display correctly

### Production Deployment

#### 1. Environment Switch
```env
# Change to production environment
NEXT_PUBLIC_PADDLE_ENVIRONMENT=production

# Update with production Paddle credentials
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=prod_token
PADDLE_API_KEY=prod_api_key
# ... other production credentials
```

#### 2. Webhook Configuration
- Update Paddle webhook URL to production domain
- Verify SSL certificate is valid
- Test webhook delivery in production

#### 3. Go-Live Checklist
- [ ] All environment variables set to production values
- [ ] Paddle webhooks configured and tested
- [ ] Database migrations applied to production
- [ ] SSL certificates valid and working
- [ ] Payment flow tested end-to-end in production
- [ ] Monitoring and logging configured for payment events

## Troubleshooting

### Common Issues

1. **Upgrade Modal Not Showing**
   - Check user authentication
   - Verify TRPC user query is working
   - Check browser console for errors

2. **Paddle Checkout Not Opening**
   - Verify Paddle client token is correct
   - Check browser console for Paddle errors
   - Ensure environment variables are loaded

3. **Webhook Not Received**
   - Verify webhook URL is accessible
   - Check webhook signature verification
   - Ensure HTTPS is used in production

4. **Premium Features Still Locked After Payment**
   - Check webhook processing logs
   - Verify database was updated correctly
   - Ensure user session is refreshed

### Debug Commands

```bash
# Check environment variables
env | grep PADDLE

# View recent webhook logs
docker logs your-app-container | grep "Paddle webhook"

# Check user premium status in database
bun run db:studio
# Query: SELECT * FROM users WHERE clerk_id = 'user_id';

# Test API endpoints directly
curl -X GET http://localhost:3000/api/pdf
# Should return health check for Browserless service
```

## Security Considerations

1. **Webhook Signature Verification**
   - All webhooks verify HMAC signatures
   - Invalid signatures are rejected

2. **Environment Variables**
   - Sensitive keys are not exposed to client
   - Production secrets stored securely

3. **User Authorization**
   - All premium features check user authentication
   - Database queries verify user ownership

4. **Payment Data**
   - No sensitive payment data stored locally
   - Paddle handles all PCI compliance

## Monitoring and Analytics

### Key Metrics to Track

1. **Conversion Metrics**
   - Upgrade modal views
   - Checkout initiations
   - Successful payments
   - Conversion rate (modal → payment)

2. **Feature Usage**
   - PDF export attempts (free vs premium)
   - Website publish attempts (free vs premium)
   - Premium feature utilization

3. **Error Tracking**
   - Payment failures
   - Webhook delivery issues
   - API endpoint errors

### Recommended Tools

- **Payment Analytics**: Paddle dashboard
- **Application Monitoring**: Sentry, LogRocket
- **Performance**: Vercel Analytics, Google Analytics
- **Database Monitoring**: Turso dashboard

## Future Enhancements

1. **Additional Premium Features**
   - Custom templates
   - Advanced analytics
   - Priority support
   - Bulk operations

2. **Payment Options**
   - Multiple currency support
   - Regional pricing
   - Student discounts

3. **User Experience**
   - Payment history page
   - Billing management
   - Usage statistics

This documentation provides a complete guide for testing and maintaining the QuickCV payment system. Follow the testing steps carefully to ensure everything works correctly before going live.

2. Once ngrok is installed, start the tunnel:

  # Start ngrok tunnel on port 3000 (where your Next.js app runs)
  ngrok http 3000

  3. You'll see output like this:
  Session Status                online
  Account                       <EMAIL> (Plan: Free)
  Version                       3.5.0
  Region                        United States (us)
  Latency                       50ms
  Web Interface                 http://127.0.0.1:4040
  Forwarding                    https://abc123xyz.ngrok-free.app -> http://localhost:3000

  4. Use the ngrok URL for Paddle webhook:
  - Copy the HTTPS forwarding URL (e.g., https://abc123xyz.ngrok-free.app)
  - In Paddle webhook settings, use: https://abc123xyz.ngrok-free.app/api/webhook/paddle

  Important notes:
  - The ngrok URL changes each time you restart ngrok (unless you have a paid account)
  - Keep ngrok running while testing webhooks
  - You can see all webhook requests in the ngrok web interface at http://127.0.0.1:4040

## Test cards 

Card type	Card number	CVC
Valid Visa debit card	4000 0566 5566 5556	100
Valid card without 3DS	4242 4242 4242 4242	100
Valid card with 3DS	4000 0038 0000 0446	100
Declined card	4000 0000 0000 0002	100
Successful initial transaction, subsequent declined transactions
https://developer.paddle.com/concepts/payment-methods/credit-debit-card


🔐 Webhook Signature Verification

  Location: /app/api/webhook/paddle/route.ts (Line 78)

  // Enable signature verification for security
  const SKIP_SIGNATURE_VERIFICATION = false; // IMPORTANT: Keep this false in production!

  To control webhook signature verification:
  - Production (secure): Keep SKIP_SIGNATURE_VERIFICATION = false
  - Development/debugging: Set SKIP_SIGNATURE_VERIFICATION = true (NOT recommended)

  The signature verification uses Paddle's format (ts=timestamp;h1=signature) and validates using HMAC-SHA256 with your webhook secret.

  📋 Important Notes:

  1. Webhook signature is now ENABLED - This is crucial for security in production
  2. All debug console.log statements removed - Cleaner logs in production
  3. Test files removed - No unnecessary code in your codebase

  🚀 Next Steps for Production:

  1. Update environment variables in production:
    - Set NEXT_PUBLIC_PADDLE_ENVIRONMENT=production
    - Update all Paddle keys to production values
    - Ensure PADDLE_WEBHOOK_SECRET matches your production webhook
  2. Update Paddle webhook URL in production dashboard to your live domain
  3. Test the full payment flow in production to ensure everything works

  The payment system is now clean, secure, and ready for production use!