import React from "react";
import { WebsiteTemplateProps } from "@/components/shared/template-system/types";
import { WebsiteTemplate } from "@/db/schema";
import { getTemplateStyle } from "@/lib/template-styles";
import { AcademicTemplate } from "./academic-template";
import { CreativeTemplate } from "./creative-template";
import { ElegantTemplate } from "./elegant-template";
import { ExecutiveTemplate } from "./executive-template";
import { MinimalTemplate } from "./minimal-template";
import { ProfessionalTemplate } from "./professional-template";
import { TechTemplate } from "./tech-template";

// Website template registry mapping slugs to components
export const websiteTemplateRegistry: Record<string, React.FC<WebsiteTemplateProps>> = {
  elegant: ElegantTemplate,
  professional: ProfessionalTemplate,
  modern: ExecutiveTemplate,
  creative: CreativeTemplate,
  minimal: MinimalTemplate,
  tech: TechTemplate,
  academic: AcademicTemplate,
};

// Get website template component by slug
export const getWebsiteTemplate = (slug: string): React.FC<WebsiteTemplateProps> | null => {
  return websiteTemplateRegistry[slug] || null;
};

// Get all available website templates
export const getAvailableWebsiteTemplates = () => {
  return Object.keys(websiteTemplateRegistry);
};

// Website template selector component
export interface WebsiteTemplateSelectorProps {
  selectedTemplate?: string;
  onTemplateSelect: (slug: string) => void;
  className?: string;
  websiteTemplates: WebsiteTemplate[];
}

export const WebsiteTemplateSelector: React.FC<WebsiteTemplateSelectorProps> = ({
  selectedTemplate,
  onTemplateSelect,
  className = "",
  websiteTemplates,
}) => {
  return (
    <div className={`website-template-selector ${className}`}>
      {/* Minimal Help Text */}
      <p className="mb-4 text-xs text-center text-gray-500 dark:text-gray-400">
        Choose a template that best matches your style
      </p>
      {/* Compact Template Grid */}
      <div className="grid grid-cols-1 gap-3">
        {websiteTemplates?.map((template) => {
          const style = getTemplateStyle(template.slug);
          if (!style) return null;

          const isSelected = selectedTemplate === template.slug;

          return (
            <button
              key={template.slug}
              type="button"
              className={`group relative border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                isSelected
                  ? "border-primary-500 bg-primary-50/50 dark:bg-primary-900/20 shadow-sm"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-sm"
              }`}
              onClick={() => onTemplateSelect(template.slug)}
              aria-label={`Select ${style.name} template`}
            >
              {/* Minimal Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* Template Icon */}
                  <div className={`text-2xl ${isSelected ? "scale-110" : ""} transition-transform`}>{style.icon}</div>

                  {/* Template Name and Description */}
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">{style.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">{style.description}</p>
                  </div>
                </div>

                {/* Selection Indicator */}
                <div className={`transition-all duration-200 ${isSelected ? "opacity-100" : "opacity-0"}`}>
                  <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Expandable Details - Only for selected template */}
              {isSelected && (
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 animate-slide-up">
                  <div className="flex flex-wrap gap-2">
                    {style.features.slice(0, 4).map((feature: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

// Website template preview component
export interface WebsiteTemplatePreviewProps {
  templateSlug: string;
  resume: any;
  website: any;
  className?: string;
}

export const WebsiteTemplatePreview: React.FC<WebsiteTemplatePreviewProps> = ({
  templateSlug,
  resume,
  website,
  className = "",
}) => {
  const TemplateComponent = getWebsiteTemplate(templateSlug);

  if (!TemplateComponent) {
    return (
      <div className={`template-preview-error p-8 text-center text-gray-500 ${className}`}>
        Template not found: {templateSlug}
      </div>
    );
  }

  return (
    <div className={`website-template-preview ${className}`}>
      <TemplateComponent resume={resume} website={website} />
    </div>
  );
};
