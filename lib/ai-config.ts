/**
 * AI Configuration Module
 *
 * This module manages all AI-related configuration settings from environment variables
 * with sensible defaults and validation. It provides type-safe access to configuration
 * values and handles environment-specific settings.
 *
 * Features:
 * - Environment variable parsing with defaults
 * - Type-safe configuration access
 * - Validation of critical settings
 * - Development vs production configurations
 * - Configuration change detection
 *
 * <AUTHOR> AI Team
 * @version 1.0.0
 */

export interface OllamaConfig {
  baseUrl: string;
  model: string;
  timeout: number;
  maxRetries: number;
}

export interface RateLimitConfig {
  freeHourly: number;
  premiumHourly: number;
  burstMinute: number;
  globalHourly: number;
}

export interface GenerationConfig {
  defaultTemperature: number;
  maxTokens: {
    short: number;
    medium: number;
    detailed: number;
  };
}

export interface MonitoringConfig {
  enableUsageTracking: boolean;
  enablePerformanceMonitoring: boolean;
  logLevel: "debug" | "info" | "warn" | "error";
}

export interface CacheConfig {
  enableResponseCache: boolean;
  ttlSeconds: number;
  maxEntries: number;
}

export interface AIConfig {
  ollama: OllamaConfig;
  rateLimit: RateLimitConfig;
  generation: GenerationConfig;
  monitoring: MonitoringConfig;
  cache: CacheConfig;
  isDevelopment: boolean;
  isProduction: boolean;
}

/**
 * Parse environment variable as number with fallback
 */
function parseEnvNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse environment variable as float with fallback
 */
function parseEnvFloat(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse environment variable as boolean with fallback
 */
function parseEnvBoolean(value: string | undefined, defaultValue: boolean): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === "true";
}

/**
 * Validate log level
 */
function parseLogLevel(value: string | undefined): "debug" | "info" | "warn" | "error" {
  const validLevels = ["debug", "info", "warn", "error"] as const;
  if (value && validLevels.includes(value as any)) {
    return value as "debug" | "info" | "warn" | "error";
  }
  return "info";
}

/**
 * Load and validate AI configuration from environment variables
 */
function loadAIConfig(): AIConfig {
  const isDevelopment = process.env.NODE_ENV === "development";
  const isProduction = process.env.NODE_ENV === "production";

  const config: AIConfig = {
    ollama: {
      baseUrl: process.env.OLLAMA_BASE_URL || "http://localhost:11434",
      model: process.env.OLLAMA_MODEL || "llama2",
      timeout: parseEnvNumber(process.env.OLLAMA_TIMEOUT, 30000),
      maxRetries: parseEnvNumber(process.env.OLLAMA_MAX_RETRIES, 3),
    },
    rateLimit: {
      freeHourly: parseEnvNumber(process.env.AI_RATE_LIMIT_FREE_HOURLY, 0), // Free users get 0 AI generations
      premiumHourly: parseEnvNumber(process.env.AI_RATE_LIMIT_PREMIUM_HOURLY, 100),
      burstMinute: parseEnvNumber(process.env.AI_RATE_LIMIT_BURST_MINUTE, 5),
      globalHourly: parseEnvNumber(process.env.AI_RATE_LIMIT_GLOBAL_HOURLY, 1000),
    },
    generation: {
      defaultTemperature: parseEnvFloat(process.env.AI_DEFAULT_TEMPERATURE, 0.7),
      maxTokens: {
        short: parseEnvNumber(process.env.AI_MAX_TOKENS_SHORT, 200),
        medium: parseEnvNumber(process.env.AI_MAX_TOKENS_MEDIUM, 400),
        detailed: parseEnvNumber(process.env.AI_MAX_TOKENS_DETAILED, 800),
      },
    },
    monitoring: {
      enableUsageTracking: parseEnvBoolean(process.env.AI_ENABLE_USAGE_TRACKING, true),
      enablePerformanceMonitoring: parseEnvBoolean(process.env.AI_ENABLE_PERFORMANCE_MONITORING, true),
      logLevel: parseLogLevel(process.env.AI_LOG_LEVEL),
    },
    cache: {
      enableResponseCache: parseEnvBoolean(process.env.AI_ENABLE_RESPONSE_CACHE, false),
      ttlSeconds: parseEnvNumber(process.env.AI_CACHE_TTL_SECONDS, 3600),
      maxEntries: parseEnvNumber(process.env.AI_CACHE_MAX_ENTRIES, 1000),
    },
    isDevelopment,
    isProduction,
  };

  // Validate critical settings
  validateConfig(config);

  return config;
}

/**
 * Validate configuration values
 */
function validateConfig(config: AIConfig): void {
  const errors: string[] = [];

  // Validate Ollama settings
  if (!config.ollama.baseUrl) {
    errors.push("OLLAMA_BASE_URL is required");
  }
  if (!config.ollama.model) {
    errors.push("OLLAMA_MODEL is required");
  }
  if (config.ollama.timeout < 1000) {
    errors.push("OLLAMA_TIMEOUT must be at least 1000ms");
  }
  if (config.ollama.maxRetries < 1) {
    errors.push("OLLAMA_MAX_RETRIES must be at least 1");
  }

  // Validate rate limits
  if (config.rateLimit.freeHourly < 1) {
    errors.push("AI_RATE_LIMIT_FREE_HOURLY must be at least 1");
  }
  if (config.rateLimit.premiumHourly < config.rateLimit.freeHourly) {
    errors.push("AI_RATE_LIMIT_PREMIUM_HOURLY must be greater than free tier limit");
  }
  if (config.rateLimit.burstMinute < 1) {
    errors.push("AI_RATE_LIMIT_BURST_MINUTE must be at least 1");
  }

  // Validate generation settings
  if (config.generation.defaultTemperature < 0 || config.generation.defaultTemperature > 1) {
    errors.push("AI_DEFAULT_TEMPERATURE must be between 0 and 1");
  }
  if (config.generation.maxTokens.short < 50) {
    errors.push("AI_MAX_TOKENS_SHORT must be at least 50");
  }
  if (config.generation.maxTokens.medium < config.generation.maxTokens.short) {
    errors.push("AI_MAX_TOKENS_MEDIUM must be greater than short tokens");
  }
  if (config.generation.maxTokens.detailed < config.generation.maxTokens.medium) {
    errors.push("AI_MAX_TOKENS_DETAILED must be greater than medium tokens");
  }

  // Validate cache settings
  if (config.cache.enableResponseCache && config.cache.ttlSeconds < 60) {
    errors.push("AI_CACHE_TTL_SECONDS must be at least 60 seconds when caching is enabled");
  }
  if (config.cache.enableResponseCache && config.cache.maxEntries < 10) {
    errors.push("AI_CACHE_MAX_ENTRIES must be at least 10 when caching is enabled");
  }

  if (errors.length > 0) {
    console.error("AI Configuration Validation Errors:");
    errors.forEach((error) => console.error(`  - ${error}`));

    if (config.isProduction) {
      throw new Error(`Invalid AI configuration: ${errors.join(", ")}`);
    } else {
      console.warn("Continuing with default values in development mode...");
    }
  }
}

/**
 * Get recommended Ollama models based on use case
 */
export function getRecommendedModels(): Record<string, { model: string; description: string; size: string }> {
  return {
    general: {
      model: "llama2",
      description: "Good general purpose model, balanced performance",
      size: "~3.8GB",
    },
    quality: {
      model: "llama2:13b",
      description: "Higher quality responses, slower generation",
      size: "~7.3GB",
    },
    fast: {
      model: "mistral",
      description: "Fast and efficient, good for production",
      size: "~4.1GB",
    },
    technical: {
      model: "codellama",
      description: "Specialized for technical/code content",
      size: "~3.8GB",
    },
    chat: {
      model: "neural-chat",
      description: "Optimized for conversational responses",
      size: "~4.1GB",
    },
    advanced: {
      model: "starling-lm",
      description: "High-quality responses with good reasoning",
      size: "~4.1GB",
    },
  };
}

/**
 * Check if current model is available and suggest alternatives
 */
export function validateModelChoice(model: string): {
  isValid: boolean;
  suggestion?: string;
  reason?: string;
} {
  const recommended = getRecommendedModels();
  const validModels = Object.values(recommended).map((m) => m.model);

  if (validModels.includes(model)) {
    return { isValid: true };
  }

  // Check for common variations
  const baseModel = model.split(":")[0];
  if (validModels.some((m) => m.startsWith(baseModel))) {
    return {
      isValid: true,
      suggestion: model,
      reason: "Model variant detected, should work if available locally",
    };
  }

  return {
    isValid: false,
    suggestion: "llama2",
    reason: `Model "${model}" not in recommended list. Consider using a recommended model for better results.`,
  };
}

/**
 * Get configuration summary for logging/debugging
 */
export function getConfigSummary(config: AIConfig): Record<string, any> {
  return {
    environment: config.isDevelopment ? "development" : "production",
    ollama: {
      baseUrl: config.ollama.baseUrl,
      model: config.ollama.model,
      timeout: `${config.ollama.timeout}ms`,
    },
    rateLimit: {
      free: `${config.rateLimit.freeHourly}/hour`,
      premium: `${config.rateLimit.premiumHourly}/hour`,
      burst: `${config.rateLimit.burstMinute}/minute`,
    },
    generation: {
      temperature: config.generation.defaultTemperature,
      tokens: config.generation.maxTokens,
    },
    features: {
      caching: config.cache.enableResponseCache,
      monitoring: config.monitoring.enablePerformanceMonitoring,
      tracking: config.monitoring.enableUsageTracking,
    },
  };
}

// Export singleton configuration
export const aiConfig = loadAIConfig();

// Log configuration summary in development
if (aiConfig.isDevelopment && aiConfig.monitoring.logLevel === "debug") {
  console.log("AI Configuration:", JSON.stringify(getConfigSummary(aiConfig), null, 2));
}

// Export configuration validation function for health checks
export { validateConfig };
