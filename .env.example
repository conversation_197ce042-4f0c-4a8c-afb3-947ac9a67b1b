# Clerk Configuration
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Database Configuration
TURSO_CONNECTION_URL=http://127.0.0.1:8080
TURSO_AUTH_TOKEN=your_turso_auth_token_here

# Next.js Configuration
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/resumes
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/resumes
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# UploadThing Configuration
UPLOADTHING_TOKEN=your_uploadthing_token_here
UPLOADTHING_APP_ID=your_app_id_here

# Analytics (Optional)
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# PDF Generation Configuration
# Coolify Browserless service WebSocket endpoint with authentication token
BROWSER_WS_ENDPOINT=ws://browserless-your-service-id.**************.sslip.io?token=your_browserless_token

# Production Environment Variables
NODE_ENV=development
VERCEL=

# Paddle Payment Configuration
NEXT_PUBLIC_PADDLE_ENVIRONMENT=sandbox
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=your_paddle_client_token_here
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret_here
NEXT_PUBLIC_PADDLE_LIFETIME_PRICE_ID=your_paddle_lifetime_price_id_here
NEXT_PUBLIC_PADDLE_LIFETIME_PRODUCT_ID=your_paddle_lifetime_product_id_here

# LinkedIn OAuth Configuration
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here
LINKEDIN_REDIRECT_URI=http://localhost:3000/api/linkedin/callback

# ===================================
# AI CONFIGURATION
# ===================================

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=30000
OLLAMA_MAX_RETRIES=3

# Alternative models you can use:
# - llama2 (Default - good general purpose)
# - llama2:13b (Larger model - better quality, slower)
# - mistral (Fast and efficient)
# - codellama (Code-focused model)
# - neural-chat (Intel's neural chat model)
# - starling-lm (Berkeley's Starling model)

# AI Rate Limiting Configuration
AI_RATE_LIMIT_FREE_HOURLY=10        # Free tier: 10 requests per hour
AI_RATE_LIMIT_PREMIUM_HOURLY=100    # Premium tier: 100 requests per hour
AI_RATE_LIMIT_BURST_MINUTE=5        # Burst protection: 5 requests per minute
AI_RATE_LIMIT_GLOBAL_HOURLY=1000    # Global API limit: 1000 requests per hour

# AI Generation Settings
AI_DEFAULT_TEMPERATURE=0.7           # Default creativity level (0.0-1.0)
AI_MAX_TOKENS_SHORT=200             # Short descriptions (~150 words)
AI_MAX_TOKENS_MEDIUM=400            # Medium descriptions (~300 words)
AI_MAX_TOKENS_DETAILED=800          # Detailed descriptions (~600 words)

# AI Monitoring and Analytics
AI_ENABLE_USAGE_TRACKING=true        # Track AI usage for analytics
AI_ENABLE_PERFORMANCE_MONITORING=true # Monitor AI performance metrics
AI_LOG_LEVEL=info                    # Logging level: debug, info, warn, error

# AI Cache Configuration (Optional - for performance)
AI_ENABLE_RESPONSE_CACHE=false       # Cache AI responses to improve performance
AI_CACHE_TTL_SECONDS=3600           # Cache time-to-live in seconds (1 hour)
AI_CACHE_MAX_ENTRIES=1000           # Maximum number of cached responses

# Optional: Redis Configuration (for distributed rate limiting)
REDIS_URL=                           # Redis connection string for distributed rate limiting
REDIS_PASSWORD=                      # Redis password if required

PDF_TOKEN_SECRET=