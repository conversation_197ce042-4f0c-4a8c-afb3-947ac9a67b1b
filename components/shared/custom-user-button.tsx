"use client";

import { <PERSON><PERSON>, Button, Skeleton } from "@heroui/react";
import { forwardRef } from "react";
import { useCurrentUser } from "@/hooks/use-current-user";

interface CustomUserButtonProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  showSkeleton?: boolean;
}

export const CustomUserButton = forwardRef<HTMLButtonElement, CustomUserButtonProps>(
  ({ size = "md", className = "", showSkeleton = true, ...props }, ref) => {
    const { user, isLoading } = useCurrentUser();

    // Show skeleton while loading if enabled
    if (isLoading && showSkeleton) {
      const skeletonSize = size === "sm" ? "w-8 h-8" : size === "lg" ? "w-12 h-12" : "w-10 h-10";
      return (
        <Skeleton className={`rounded-full ${skeletonSize}`}>
          <div className={`rounded-full bg-default-200 ${skeletonSize}`} />
        </Skeleton>
      );
    }

    // Don't render if no user data
    if (!user) {
      return null;
    }

    const avatarSize = size === "sm" ? "sm" : size === "lg" ? "lg" : "md";
    const buttonSize = size === "sm" ? "min-w-unit-8" : size === "lg" ? "min-w-unit-12" : "min-w-unit-10";

    return (
      <Button ref={ref} variant="light" className={`p-0 ${buttonSize} gap-2 ${className}`} {...props}>
        <Avatar
          name={user.emailAddress}
          size={avatarSize}
          className="flex-shrink-0 transition-transform hover:scale-105"
          classNames={{
            base: "ring-2 ring-transparent hover:ring-primary/20 transition-all",
            name: "text-xs font-medium",
          }}
        />
      </Button>
    );
  },
);

CustomUserButton.displayName = "CustomUserButton";
