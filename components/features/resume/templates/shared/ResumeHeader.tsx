import Image from "next/image";
import React from "react";
import { getFullName } from "./utils";

export interface ResumeHeaderProps {
  firstName: string;
  lastName: string;
  jobTitle: string;
  photo?: string;
  showPhoto?: boolean;
  layout?: "centered" | "left" | "split";
  variant?: "simple" | "accent" | "background";
  children?: React.ReactNode; // for contact info
  className?: string;
  locale?: string;
}

export const ResumeHeader: React.FC<ResumeHeaderProps> = ({
  firstName,
  lastName,
  jobTitle,
  photo,
  showPhoto = true,
  layout = "centered",
  variant: _variant = "simple",
  children,
  className = "",
  locale = "en-US",
}) => {
  const fullName = getFullName(firstName, lastName, locale);

  const layoutClasses = {
    centered: "text-center",
    left: "text-left",
    split: "flex justify-between items-center",
  };

  return (
    <header className={`resume-header ${layoutClasses[layout]} mb-6 ${className}`}>
      <div className="flex-1">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{fullName}</h1>
        <p className="text-xl text-gray-700 mb-4">{jobTitle}</p>
        {children}
      </div>
      {showPhoto && photo && (
        <div className="ms-6">
          <Image alt={fullName} className="w-24 h-24 rounded-full object-cover" height={96} src={photo} width={96} />
        </div>
      )}
    </header>
  );
};
