"use client";

import { useRouter } from "@bprogress/next/app";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalHeader,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { ShareModal } from "@/components/features/sharing";
import { useRoutes } from "@/config/path-constants";
import { useTRPCLoading } from "@/lib/use-loading-action";
import type { DeleteResumeResponse, DuplicateResumeResponse, RenameResumeResponse } from "@/types/trpc-responses";

interface ResumeCardActionsProps {
  resumeId: number;
}

export default function ResumeCardActions({ resumeId }: ResumeCardActionsProps) {
  const router = useRouter();
  const { resumeEditPath } = useRoutes();
  const [newTitle, setNewTitle] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isShareOpen, onOpen: onShareOpen, onClose: onShareClose } = useDisclosure();
  const deleteResumeMutation = trpc.resumes.deleteResume.useMutation();
  const duplicateResumeMutation = trpc.resumes.duplicateResume.useMutation();
  const renameResumeMutation = trpc.resumes.renameResume.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  // Wrap TRPC mutations with global loading
  const wrappedDuplicate = wrapTRPCMutation(
    duplicateResumeMutation,
    `duplicate-resume-${resumeId}`,
    "Duplicating resume...",
  );

  const wrappedRename = wrapTRPCMutation(renameResumeMutation, `rename-resume-${resumeId}`, "Renaming resume...");

  const wrappedDelete = wrapTRPCMutation(deleteResumeMutation, `delete-resume-${resumeId}`, "Deleting resume...");

  const handleDuplicate = async () => {
    try {
      const result = (await wrappedDuplicate.mutateAsync({
        id: resumeId,
      })) as DuplicateResumeResponse;
      if (result.success) {
        toast.success("Resume duplicated successfully");
      } else {
        toast.error("Failed to duplicate resume");
      }
    } catch (error) {
      console.error("Error duplicating resume:", error);
      toast.error("Failed to duplicate resume");
    }
  };

  const handleRename = async () => {
    if (!newTitle.trim()) return;

    // Close modal immediately and show global loader
    onClose();

    try {
      const result = (await wrappedRename.mutateAsync({
        id: resumeId,
        title: newTitle.trim(),
      })) as RenameResumeResponse;

      if (result.success) {
        setNewTitle("");
        toast.success("Resume renamed successfully");
      } else {
        toast.error("Failed to rename resume");
      }
    } catch (error) {
      console.error("Error renaming resume:", error);
      toast.error("Failed to rename resume");
    }
  };

  const handleDelete = async () => {
    try {
      const result = (await wrappedDelete.mutateAsync({
        id: resumeId,
      })) as DeleteResumeResponse;
      if (result.success) {
        toast.success("Resume deleted successfully");
      } else {
        toast.error("Failed to delete resume");
      }
    } catch (error) {
      console.error("Error deleting resume:", error);
      toast.error("Failed to delete resume");
    }
  };

  return (
    <>
      <Dropdown>
        <DropdownTrigger>
          <Button
            isIconOnly
            aria-label="More options"
            className="transition-all duration-200 hover:scale-105"
            size="sm"
            variant="light"
            color="default"
          >
            <Icon height="16" icon="lucide:more-horizontal" width="16" />
          </Button>
        </DropdownTrigger>
        <DropdownMenu aria-label="Resume actions">
          <DropdownItem
            key="edit"
            startContent={<Icon className="w-4 h-4" icon="lucide:edit-3" />}
            onPress={() => router.push(resumeEditPath(resumeId))}
          >
            Edit
          </DropdownItem>

          <DropdownItem key="rename" startContent={<Icon className="w-4 h-4" icon="lucide:edit" />} onPress={onOpen}>
            Rename
          </DropdownItem>

          <DropdownItem
            key="duplicate"
            startContent={<Icon className="w-4 h-4" icon="lucide:copy" />}
            onPress={handleDuplicate}
          >
            Duplicate
          </DropdownItem>

          <DropdownItem
            key="share"
            startContent={<Icon className="w-4 h-4" icon="tabler:share" />}
            onPress={onShareOpen}
          >
            Share
          </DropdownItem>

          <DropdownItem
            key="delete"
            className="text-danger"
            onPress={handleDelete}
            color="danger"
            startContent={<Icon className="w-4 h-4" icon="lucide:trash-2" />}
          >
            Delete
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      <Modal isOpen={isOpen} onClose={onClose} placement="top-center">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">Rename Resume</ModalHeader>
          <ModalBody>
            <Input
              autoFocus
              label="Resume Title"
              placeholder="Enter new title"
              value={newTitle}
              onValueChange={setNewTitle}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleRename();
                }
              }}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button color="primary" onPress={handleRename} isDisabled={!newTitle.trim()}>
              Save
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <ShareModal isOpen={isShareOpen} onClose={onShareClose} resumeId={resumeId} />
    </>
  );
}
