import React from "react";

import { getRTLFlexClasses, useRTL } from "../shared/utils";

// Navigation component for websites
export interface WebsiteNavProps {
  fullName: string;
  sections: { id: string; label: string }[];
  className?: string;
}

export const WebsiteNav: React.FC<WebsiteNavProps> = ({ fullName, sections, className = "" }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  return (
    <nav className={`website-nav bg-white shadow-sm border-b sticky top-0 z-50 ${className}`}>
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className={`flex ${rtlClasses.justifyStart} justify-between items-center`}>
          <div className={`font-bold text-xl text-gray-900 ${rtlClasses.textStart}`}>{fullName}</div>
          <div className={`hidden md:flex ${rtlClasses.spaceX}`}>
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                {section.label}
              </a>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

// Footer component for websites
export interface WebsiteFooterProps {
  fullName: string;
  className?: string;
}

export const WebsiteFooter: React.FC<WebsiteFooterProps> = ({ fullName, className = "" }) => {
  return (
    <footer className={`website-footer bg-gray-50 py-8 text-center ${className}`}>
      <div className="max-w-6xl mx-auto px-4">
        <p className="text-gray-600 mb-2">
          © {new Date().getFullYear()} {fullName}
        </p>
        <p className="text-gray-500 text-sm">
          Created with{" "}
          <a
            href="https://quickcv.com"
            className="text-blue-600 hover:text-blue-800"
            target="_blank"
            rel="noopener noreferrer"
          >
            QuickCV
          </a>
        </p>
      </div>
    </footer>
  );
};
