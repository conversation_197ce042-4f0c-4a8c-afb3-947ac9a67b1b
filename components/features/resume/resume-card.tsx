"use client";
import { useRouter } from "@bprogress/next/app";
import { <PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRoutes } from "@/config/path-constants";
import { Resume } from "@/db/schema";
import { ResumeCardActions } from "./";

interface ResumeCardProps {
  resume: Resume;
}

export default function ResumeCard({ resume }: ResumeCardProps) {
  const router = useRouter();
  const { resumeEditPath } = useRoutes();

  return (
    <Card className="w-[260px] hover:shadow-lg transition-all duration-300">
      <CardHeader className="p-0 cursor-pointer overflow-hidden" onClick={() => router.push(resumeEditPath(resume.id))}>
        {resume.thumbnail ? (
          <div className="w-full h-[320px] overflow-hidden hover:scale-[1.02] transition-transform duration-300">
            <img
              alt="Resume thumbnail"
              className="w-full h-full object-cover object-top"
              src={resume.thumbnail}
              onError={(e) => {
                console.error("Thumbnail failed to load:", resume.thumbnail);
                e.currentTarget.style.display = "none";
                const fallback = e.currentTarget.nextSibling as HTMLElement;
                if (fallback) fallback.style.display = "flex";
              }}
            />
            <div className="w-full h-full bg-default-100 flex items-center justify-center" style={{ display: "none" }}>
              <div className="text-center">
                <Icon icon="lucide:file-text" className="w-8 h-8 text-default-400 mx-auto mb-2" />
                <span className="text-default-500 text-sm">Thumbnail unavailable</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full h-[200px] bg-default-100 flex items-center justify-center">
            <div className="text-center">
              <Icon icon="lucide:file-plus" className="w-8 h-8 text-default-400 mx-auto mb-2" />
              <span className="text-default-500 text-sm">No preview available</span>
            </div>
          </div>
        )}
      </CardHeader>
      <CardFooter className="px-4 py-3 justify-between items-center">
        <div className="flex items-center min-w-0 flex-1">
          <h4 className="text-medium font-semibold text-default-700 truncate">{resume.title}</h4>
        </div>
        <ResumeCardActions resumeId={resume.id} />
      </CardFooter>
    </Card>
  );
}
