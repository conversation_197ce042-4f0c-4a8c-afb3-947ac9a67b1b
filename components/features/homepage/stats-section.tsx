"use client";
import { Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { subtitle, title } from "@/components/primitives";

const stats = [
  {
    key: "resumes_created",
    value: "50,000+",
    label: "Resumes Created",
    icon: "heroicons:document-text-20-solid",
  },
  {
    key: "job_success_rate",
    value: "94%",
    label: "Job Success Rate",
    icon: "heroicons:check-badge-20-solid",
  },
  {
    key: "satisfaction_rate",
    value: "4.9/5",
    label: "Satisfaction Rate",
    icon: "heroicons:star-20-solid",
  },
  {
    key: "avg_time_saved",
    value: "2 Hours",
    label: "Avg. Time Saved",
    icon: "heroicons:clock-20-solid",
  },
];

export default function StatsSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.15, delayChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
  };

  return (
    <section className="py-24 md:py-32 bg-default-50 dark:bg-default-950/20">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className={title({ size: "lg" })}>Join thousands of successful job seekers</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>
            Our platform has a proven track record of helping users land their dream jobs faster.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {stats.map((stat) => (
            <motion.div key={stat.key} variants={itemVariants}>
              <Card className="h-full text-center shadow-lg border-default-200 dark:border-default-800 bg-white/80 dark:bg-default-100/80 backdrop-blur-md">
                <CardBody className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl flex items-center justify-center text-primary-500 mx-auto mb-6">
                    <Icon icon={stat.icon} className="w-8 h-8" />
                  </div>
                  <h3 className="text-4xl font-bold text-default-900 mb-2">{stat.value}</h3>
                  <p className="text-default-600 font-medium">{stat.label}</p>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
