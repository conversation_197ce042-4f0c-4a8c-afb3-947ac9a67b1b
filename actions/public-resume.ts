"use server";

import { asc, eq } from "drizzle-orm";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  type FullResume,
  hobbies,
  languages,
  profiles,
  projects,
  resumes,
  skills,
  templates,
  volunteerings,
} from "@/db/schema";

/**
 * Get public resume by ID (no authentication required)
 * Used for public sharing via tokens
 */
export async function getPublicResumeById(resumeId: number) {
  try {
    // Get the resume with template relation
    const [resumeData] = await db
      .select()
      .from(resumes)
      .leftJoin(templates, eq(resumes.templateId, templates.id))
      .where(eq(resumes.id, resumeId))
      .limit(1);

    if (!resumeData?.resumes) {
      return {
        success: false,
        error: "Resume not found",
      };
    }

    // Get all nested resume resources in parallel (excluding references for privacy)
    const [
      resumeEducations,
      resumeExperiences,
      resumeProjects,
      resumeAwards,
      resumeCertifications,
      resumeSkills,
      resumeLanguages,
      resumeHobbies,
      resumeVolunteerings,
      resumeProfiles,
    ] = await Promise.all([
      db.select().from(educations).where(eq(educations.resumeId, resumeId)).orderBy(asc(educations.sort)),
      db.select().from(experiences).where(eq(experiences.resumeId, resumeId)).orderBy(asc(experiences.sort)),
      db.select().from(projects).where(eq(projects.resumeId, resumeId)).orderBy(asc(projects.sort)),
      db.select().from(awards).where(eq(awards.resumeId, resumeId)).orderBy(asc(awards.sort)),
      db.select().from(certifications).where(eq(certifications.resumeId, resumeId)).orderBy(asc(certifications.sort)),
      db.select().from(skills).where(eq(skills.resumeId, resumeId)).orderBy(asc(skills.sort)),
      db.select().from(languages).where(eq(languages.resumeId, resumeId)).orderBy(asc(languages.sort)),
      db.select().from(hobbies).where(eq(hobbies.resumeId, resumeId)).orderBy(asc(hobbies.sort)),
      db.select().from(volunteerings).where(eq(volunteerings.resumeId, resumeId)).orderBy(asc(volunteerings.sort)),
      db.select().from(profiles).where(eq(profiles.resumeId, resumeId)).orderBy(asc(profiles.sort)),
    ]);

    // Create privacy-safe resume object for public sharing
    const publicResume = { ...resumeData.resumes };

    // Remove sensitive personal information for public sharing
    // Keep only professional information and public-safe contact details
    const privacySafeResume = {
      ...publicResume,
      // Keep professional information
      firstName: publicResume.firstName,
      lastName: publicResume.lastName,
      jobTitle: publicResume.jobTitle,
      bio: publicResume.bio,
      website: publicResume.website,

      // Remove sensitive personal information
      phone: "", // Hide phone number
      email: "", // Hide email address
      address: "", // Hide full address
      street: "", // Hide street address
      birthDate: "", // Hide birth date
      city: publicResume.city, // Keep city for general location
      country: publicResume.country, // Keep country for general location

      // Keep visual and template settings
      showPhoto: publicResume.showPhoto,
      photo: publicResume.photo,
      thumbnail: publicResume.thumbnail,
      colorScheme: publicResume.colorScheme,
      fontFamily: publicResume.fontFamily,
      spacing: publicResume.spacing,
      margins: publicResume.margins,
      templateId: publicResume.templateId,

      // Keep metadata
      id: publicResume.id,
      title: publicResume.title,
      createdAt: publicResume.createdAt,
      updatedAt: publicResume.updatedAt,
      userId: publicResume.userId,
    };

    // Construct the full resume object with privacy controls
    const fullResume: FullResume = {
      ...privacySafeResume,
      template: resumeData.templates,
      educations: resumeEducations,
      experiences: resumeExperiences,
      projects: resumeProjects,
      awards: resumeAwards,
      certifications: resumeCertifications,
      skills: resumeSkills,
      languages: resumeLanguages,
      references: [], // Exclude references for privacy in public sharing
      hobbies: resumeHobbies,
      volunteerings: resumeVolunteerings,
      profiles: resumeProfiles,
    };

    return {
      success: true,
      data: fullResume,
    };
  } catch (error) {
    console.error("Error fetching public resume:", error);
    return {
      success: false,
      error: "Failed to fetch resume",
    };
  }
}
