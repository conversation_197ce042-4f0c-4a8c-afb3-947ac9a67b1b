"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, Chip, Progress } from "@heroui/react";
import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import { UpgradeModal } from "@/components/payment";

// Simplified feature approach - removed complex feature flag system

// Simple feature definitions
const FEATURES = {
  AI_GENERATION: { id: "ai_generation", name: "AI Content Generation" },
  PDF_EXPORT: { id: "pdf_export", name: "Export to PDF" },
  WEBSITE_PUBLISH: { id: "website_publish", name: "Publish Website" },
  RESUME_SHARE: { id: "resume_share", name: "Share Resume" },
} as const;

function getFeatureById(id: string) {
  return Object.values(FEATURES).find((feature) => feature.id === id);
}

// Stub hook to replace deleted feature flags
function useFeatureFlags() {
  return {
    trackUsage: (...args: any[]) => {
      console.log("Feature usage tracked:", args);
    },
  };
}

interface SmartUpgradePromptProps {
  featureId: string;
  trigger: "limit_reached" | "feature_blocked" | "contextual";
  context?: {
    action?: string;
    currentUsage?: number;
    limit?: number;
    userIntent?: string;
  };
  variant?: "compact" | "card" | "banner" | "modal";
  showBenefits?: boolean;
  showTestimonial?: boolean;
  autoShow?: boolean;
  onClose?: () => void;
  className?: string;
}

interface UpgradeMessage {
  title: string;
  subtitle: string;
  urgency: string;
  benefits: string[];
  cta: string;
  testimonial?: {
    text: string;
    author: string;
    avatar: string;
  };
}

const UPGRADE_MESSAGES: Record<string, Record<string, UpgradeMessage>> = {
  [FEATURES.AI_GENERATION.id]: {
    limit_reached: {
      title: "You've used your free AI generation!",
      subtitle: "Upgrade to continue creating compelling content with unlimited AI assistance.",
      urgency: "⚡ Unlock unlimited AI generations now",
      benefits: [
        "Unlimited AI content generation",
        "Industry-specific suggestions",
        "Multiple writing styles",
        "Advanced optimization tips",
      ],
      cta: "Upgrade for Unlimited AI",
      testimonial: {
        text: "AI helped me land 3 interviews in my first week. Best investment ever!",
        author: "Sarah K., Marketing Manager",
        avatar: "https://i.pravatar.cc/150?u=sarah",
      },
    },
    feature_blocked: {
      title: "Unlock AI-Powered Content",
      subtitle: "Generate professional resume content that stands out to recruiters.",
      urgency: "🚀 Write better content 10x faster",
      benefits: [
        "Professional content suggestions",
        "ATS-optimized keywords",
        "Industry best practices",
        "Instant results",
      ],
      cta: "Try AI Content Generation",
    },
    contextual: {
      title: "Struggling with what to write?",
      subtitle: "Let AI help you articulate your achievements professionally.",
      urgency: "💡 Get instant writing assistance",
      benefits: ["Overcome writer's block", "Professional language", "Tailored suggestions", "Save hours of writing"],
      cta: "Generate with AI",
    },
  },
  [FEATURES.PDF_EXPORT.id]: {
    feature_blocked: {
      title: "Ready to apply?",
      subtitle: "Export your resume as a professional PDF that recruiters love.",
      urgency: "📄 Get ATS-friendly PDFs instantly",
      benefits: ["ATS-optimized formatting", "Perfect print quality", "Instant download", "Multiple formats"],
      cta: "Export Professional PDF",
      testimonial: {
        text: "The PDF quality is amazing. I get calls for every application now!",
        author: "Mike R., Software Engineer",
        avatar: "https://i.pravatar.cc/150?u=mike",
      },
    },
  },
  [FEATURES.RESUME_SHARE.id]: {
    contextual: {
      title: "Want to share your resume?",
      subtitle: "Create professional sharing links and track who views your resume.",
      urgency: "🔗 Share professionally and track engagement",
      benefits: ["Professional sharing links", "View analytics", "Password protection", "Expiration controls"],
      cta: "Enable Smart Sharing",
    },
  },
};

export function SmartUpgradePrompt({
  featureId,
  trigger,
  context,
  variant = "card",
  showBenefits = true,
  showTestimonial = false,
  autoShow = false,
  onClose,
  className = "",
}: SmartUpgradePromptProps) {
  const { trackUsage } = useFeatureFlags();
  const [isVisible, setIsVisible] = useState(autoShow);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [showWiggle, setShowWiggle] = useState(false);
  const [showPulse, setShowPulse] = useState(false);

  const feature = getFeatureById(featureId);
  const message = UPGRADE_MESSAGES[featureId]?.[trigger];

  useEffect(() => {
    if (autoShow && message) {
      setIsVisible(true);
      trackUsage(featureId, {
        customProperties: {
          action: "smart_prompt_shown",
          trigger,
          context: context?.action,
        },
      });

      // Add playful entrance animation with delay
      setTimeout(() => setShowWiggle(true), 1000);
      setTimeout(() => setShowWiggle(false), 1500);

      // Periodic gentle pulse to draw attention (not annoying)
      const pulseInterval = setInterval(() => {
        setShowPulse(true);
        setTimeout(() => setShowPulse(false), 1000);
      }, 15000); // Every 15 seconds

      return () => clearInterval(pulseInterval);
    }
  }, [autoShow, message, featureId, trigger, context, trackUsage]);

  const handleUpgrade = () => {
    setIsAnimating(true);

    trackUsage(featureId, {
      customProperties: {
        action: "upgrade_clicked_from_smart_prompt",
        trigger,
        context: context?.action,
      },
    });

    // Add celebration before opening modal
    setTimeout(() => {
      setShowUpgradeModal(true);
      setIsAnimating(false);
    }, 800);
  };

  const handleClose = () => {
    trackUsage(featureId, {
      customProperties: {
        action: "smart_prompt_dismissed",
        trigger,
      },
    });
    setIsVisible(false);
    onClose?.();
  };

  if (!feature || !message || !isVisible) {
    return null;
  }

  // Compact variant (inline prompt)
  if (variant === "compact") {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`inline-flex items-center gap-3 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/10 dark:to-secondary-900/10 border border-primary-200 dark:border-primary-800 rounded-lg p-3 ${className}`}
      >
        <Icon icon="heroicons:star-20-solid" className="w-5 h-5 text-primary" />
        <span className="text-sm font-medium text-default-900">{message.title}</span>
        <Button size="sm" color="primary" onPress={handleUpgrade}>
          Upgrade
        </Button>
        <Button size="sm" variant="light" isIconOnly onPress={handleClose}>
          <Icon icon="heroicons:x-mark-20-solid" className="w-4 h-4" />
        </Button>
      </motion.div>
    );
  }

  // Banner variant (full width)
  if (variant === "banner") {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={`bg-gradient-to-r from-primary-500 to-secondary-500 text-white p-4 ${className}`}
      >
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center gap-4">
            <Icon icon="heroicons:star-20-solid" className="w-6 h-6" />
            <div>
              <h4 className="font-semibold">{message.title}</h4>
              <p className="text-sm opacity-90">{message.urgency}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              color="default"
              variant="solid"
              className="bg-white text-primary font-semibold"
              onPress={handleUpgrade}
            >
              {message.cta}
            </Button>
            <Button size="sm" variant="light" isIconOnly onPress={handleClose}>
              <Icon icon="heroicons:x-mark-20-solid" className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </motion.div>
    );
  }

  // Card variant (default)
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{
          opacity: 1,
          y: 0,
          scale: isAnimating ? 1.05 : 1,
        }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <motion.div
          animate={{
            scale: showPulse ? [1, 1.02, 1] : 1,
            rotate: showWiggle ? [0, 1, -1, 0] : 0,
          }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        >
          <Card className="shadow-lg border border-primary-200 dark:border-primary-800 bg-gradient-to-br from-white to-primary-50/30 dark:from-default-900 dark:to-primary-900/10 hover:shadow-xl transition-shadow duration-300">
            <CardBody className="p-6">
              {/* Header with Enhanced Whimsy */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center"
                    animate={{
                      rotate: isAnimating ? 360 : 0,
                      scale: isAnimating ? [1, 1.2, 1] : 1,
                    }}
                    transition={{ duration: 0.8 }}
                  >
                    <motion.div
                      animate={{
                        rotate: [0, 10, -10, 0],
                        scale: showPulse ? [1, 1.1, 1] : 1,
                      }}
                      transition={{
                        rotate: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                        scale: { duration: 0.6 },
                      }}
                    >
                      <Icon icon="heroicons:star-20-solid" className="w-5 h-5 text-white" />
                    </motion.div>
                  </motion.div>
                  <div>
                    <motion.h3
                      className="text-lg font-bold text-default-900"
                      animate={{ x: showWiggle ? [0, 2, -2, 0] : 0 }}
                      transition={{ duration: 0.4 }}
                    >
                      {message.title}
                    </motion.h3>
                    <p className="text-sm text-default-600">{message.subtitle}</p>
                  </div>
                </div>
                <motion.div whileHover={{ scale: 1.1, rotate: 90 }} whileTap={{ scale: 0.9 }}>
                  <Button size="sm" variant="light" isIconOnly onPress={handleClose}>
                    <Icon icon="heroicons:x-mark-20-solid" className="w-4 h-4" />
                  </Button>
                </motion.div>
              </div>

              {/* Usage progress for AI */}
              {trigger === "limit_reached" && context?.currentUsage && context?.limit && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-default-600">Usage Progress</span>
                    <span className="font-medium">
                      {context.currentUsage}/{context.limit}
                    </span>
                  </div>
                  <Progress value={(context.currentUsage / context.limit) * 100} color="warning" className="mb-2" />
                  <p className="text-xs text-warning-600">{message.urgency}</p>
                </div>
              )}

              {/* Benefits */}
              {showBenefits && message.benefits && (
                <div className="mb-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {message.benefits.map((benefit, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center gap-2"
                      >
                        <Icon icon="heroicons:check-circle-20-solid" className="w-4 h-4 text-success flex-shrink-0" />
                        <span className="text-sm text-default-700">{benefit}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Testimonial */}
              {showTestimonial && message.testimonial && (
                <div className="mb-4 p-3 bg-default-100 dark:bg-default-800 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Avatar src={message.testimonial.avatar} size="sm" className="flex-shrink-0" />
                    <div>
                      <p className="text-sm text-default-700 italic mb-1">"{message.testimonial.text}"</p>
                      <p className="text-xs text-default-500 font-medium">{message.testimonial.author}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="flex-1">
                  <Button
                    color="primary"
                    className={`w-full font-semibold bg-gradient-to-r from-primary to-secondary hover:shadow-2xl transition-all duration-300 ${
                      isAnimating ? "animate-pulse" : ""
                    }`}
                    startContent={
                      <motion.div
                        animate={
                          isAnimating
                            ? {
                                rotate: [0, 360],
                                scale: [1, 1.3, 1],
                              }
                            : showPulse
                              ? {
                                  scale: [1, 1.1, 1],
                                }
                              : {}
                        }
                        transition={{ duration: 0.8 }}
                      >
                        <Icon
                          icon={isAnimating ? "heroicons:sparkles-20-solid" : "heroicons:rocket-launch-20-solid"}
                          className="w-4 h-4"
                        />
                      </motion.div>
                    }
                    onPress={handleUpgrade}
                    isLoading={isAnimating}
                  >
                    {isAnimating ? "Creating magic..." : message.cta}
                  </Button>
                </motion.div>
              </div>

              {/* Trust indicators */}
              <div className="flex items-center justify-center gap-4 mt-4 text-xs text-default-500">
                <div className="flex items-center gap-1">
                  <Icon icon="heroicons:shield-check-20-solid" className="w-3 h-3" />
                  Secure Payment
                </div>
                <div className="flex items-center gap-1">
                  <Icon icon="heroicons:clock-20-solid" className="w-3 h-3" />
                  Instant Access
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>

      {/* Modals */}
      <UpgradeModal isOpen={showUpgradeModal} onClose={() => setShowUpgradeModal(false)} />
    </AnimatePresence>
  );
}

export default SmartUpgradePrompt;
