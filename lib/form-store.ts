import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { FullResume } from "@/db/schema";

interface FormStore {
  formData: FullResume | null;
  setFormData: (data: FullResume) => void;
  updateField: (fieldName: keyof FullResume, value: unknown) => void;
  updateNestedField: (path: string, value: unknown) => void;
  addItemToCollection: (collectionName: keyof FullResume, item: any) => void;
  removeItemFromCollection: (collectionName: keyof FullResume, index: number) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  resetForm: () => void;
}

export const useFormStore = create<FormStore>()(
  subscribeWithSelector((set, get) => ({
    formData: null,

    setFormData: (data) => set({ formData: data }),

    updateField: (fieldName, value) =>
      set((state) => {
        if (!state.formData) return state;
        return {
          formData: { ...state.formData, [fieldName]: value },
        };
      }),

    updateNestedField: (path, value) => {
      set((state) => {
        if (!state.formData) return state;

        // Simplified approach: handle both simple paths and nested array paths
        if (path.includes("[") && path.includes("]")) {
          // Parse array path: "educations[0][city]" -> collection="educations", index=0, field="city"
          const arrayMatch = path.match(/^([^[]+)\[(\d+)\]\[([^\]]+)\]$/);
          if (arrayMatch) {
            const [, collectionName, indexStr, fieldName] = arrayMatch;
            const index = parseInt(indexStr);

            return {
              formData: {
                ...state.formData,
                [collectionName]:
                  (state.formData[collectionName as keyof FullResume] as any[])?.map((item, i) =>
                    i === index ? { ...item, [fieldName]: value } : item,
                  ) || [],
              },
            };
          }
        }

        // Handle direct collection updates (for add/remove operations)
        return {
          formData: {
            ...state.formData,
            [path]: value,
          },
        };
      });
    },

    handleInputChange: (e) => {
      const { name, value, type } = e.target;
      const checked = (e.target as HTMLInputElement).checked;
      const newValue = type === "checkbox" ? checked : value;

      // Extract field name from complex nested name patterns
      const fieldName = name.split("][").pop()?.replace("]", "") || name;

      get().updateField(fieldName as keyof FullResume, newValue);
    },

    addItemToCollection: (collectionName, item) =>
      set((state) => {
        if (!state.formData) return state;

        const collection = (state.formData[collectionName] as any[]) || [];
        const updatedCollection = [...collection, { ...item, sort: collection.length }];

        return {
          formData: {
            ...state.formData,
            [collectionName]: updatedCollection,
          },
        };
      }),

    removeItemFromCollection: (collectionName, index) =>
      set((state) => {
        if (!state.formData) return state;

        const collection = (state.formData[collectionName] as any[]) || [];
        const updatedCollection = collection.filter((_, i) => i !== index);

        return {
          formData: {
            ...state.formData,
            [collectionName]: updatedCollection,
          },
        };
      }),

    resetForm: () => set({ formData: null }),
  })),
);
