import { FullResume } from "@/db/schema";

export interface FormSectionConfig {
  id: string;
  titleKey: string;
  descriptionKey?: string;
  icon: string;
  collectionName: keyof FullResume;
  keyField: string;
  entity: string;
  fields: FormFieldConfig[];
}

export interface FormFieldConfig {
  name: string;
  type: "string" | "number" | "date" | "boolean" | "textarea";
  labelKey?: string;
  placeholderKey?: string;
  descriptionKey?: string;
  className?: string;
  required?: boolean;
}

export const FORM_SECTIONS: FormSectionConfig[] = [
  {
    id: "profiles",
    titleKey: "forms.profile",
    descriptionKey: "forms.sections.profiles",
    icon: "lucide:link",
    collectionName: "profiles",
    keyField: "network",
    entity: "profile",
    fields: [
      { name: "network", type: "string", placeholderKey: "forms.placeholders.network" },
      { name: "username", type: "string", placeholderKey: "forms.placeholders.username" },
      { name: "url", type: "string", placeholderKey: "forms.placeholders.url_example" },
    ],
  },
  {
    id: "education",
    titleKey: "forms.education",
    descriptionKey: "forms.sections.education",
    icon: "lucide:graduation-cap",
    collectionName: "educations",
    keyField: "institution",
    entity: "education",
    fields: [
      { name: "institution", type: "string", placeholderKey: "forms.placeholders.institution" },
      { name: "degree", type: "string", placeholderKey: "forms.placeholders.degree" },
      { name: "field", type: "string", placeholderKey: "forms.placeholders.field_of_study" },
      { name: "startDate", type: "date", placeholderKey: "forms.placeholders.from" },
      { name: "endDate", type: "date", placeholderKey: "forms.placeholders.to" },
      { name: "gpa", type: "string", placeholderKey: "forms.placeholders.gpa" },
      { name: "city", type: "string", placeholderKey: "forms.placeholders.city" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "experience",
    titleKey: "forms.experience",
    descriptionKey: "forms.sections.experience",
    icon: "lucide:briefcase",
    collectionName: "experiences",
    keyField: "company",
    entity: "experience",
    fields: [
      { name: "company", type: "string", placeholderKey: "forms.placeholders.company" },
      { name: "position", type: "string", placeholderKey: "forms.placeholders.position" },
      { name: "startDate", type: "date", placeholderKey: "forms.placeholders.from" },
      { name: "endDate", type: "date", placeholderKey: "forms.placeholders.to" },
      { name: "city", type: "string", placeholderKey: "forms.placeholders.city" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "projects",
    titleKey: "forms.project",
    descriptionKey: "forms.sections.projects",
    icon: "lucide:folder",
    collectionName: "projects",
    keyField: "title",
    entity: "project",
    fields: [
      { name: "title", type: "string", placeholderKey: "forms.placeholders.project_title" },
      { name: "client", type: "string", placeholderKey: "forms.placeholders.client" },
      { name: "startDate", type: "date", placeholderKey: "forms.placeholders.from" },
      { name: "endDate", type: "date", placeholderKey: "forms.placeholders.to" },
      { name: "url", type: "string", placeholderKey: "forms.placeholders.url_example" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "awards",
    titleKey: "forms.award",
    descriptionKey: "forms.sections.awards",
    icon: "lucide:award",
    collectionName: "awards",
    keyField: "title",
    entity: "award",
    fields: [
      { name: "title", type: "string", placeholderKey: "forms.placeholders.award_title" },
      { name: "issuer", type: "string", placeholderKey: "forms.placeholders.issuer" },
      { name: "date", type: "date", placeholderKey: "forms.placeholders.date" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "certifications",
    titleKey: "forms.certification",
    descriptionKey: "forms.sections.certifications",
    icon: "lucide:certificate",
    collectionName: "certifications",
    keyField: "title",
    entity: "certification",
    fields: [
      { name: "title", type: "string", placeholderKey: "forms.placeholders.certification_title" },
      { name: "issuer", type: "string", placeholderKey: "forms.placeholders.issuer" },
      { name: "date", type: "date", placeholderKey: "forms.placeholders.date" },
      { name: "url", type: "string", placeholderKey: "forms.placeholders.url_example" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "skills",
    titleKey: "forms.skill",
    descriptionKey: "forms.sections.skills",
    icon: "lucide:zap",
    collectionName: "skills",
    keyField: "name",
    entity: "skill",
    fields: [
      { name: "name", type: "string", placeholderKey: "forms.placeholders.skill_name" },
      { name: "level", type: "number", placeholderKey: "forms.placeholders.skill_level" },
    ],
  },
  {
    id: "languages",
    titleKey: "forms.language",
    descriptionKey: "forms.sections.languages",
    icon: "lucide:globe",
    collectionName: "languages",
    keyField: "name",
    entity: "language",
    fields: [
      { name: "name", type: "string", placeholderKey: "forms.language_name" },
      { name: "proficiency", type: "number", placeholderKey: "forms.placeholders.proficiency_level" },
    ],
  },
  {
    id: "references",
    titleKey: "forms.reference",
    descriptionKey: "forms.sections.references",
    icon: "lucide:users",
    collectionName: "references",
    keyField: "name",
    entity: "reference",
    fields: [
      { name: "name", type: "string", placeholderKey: "forms.full_name" },
      { name: "company", type: "string", placeholderKey: "forms.company" },
      { name: "position", type: "string", placeholderKey: "forms.position" },
      { name: "email", type: "string", placeholderKey: "forms.placeholders.email_address" },
      { name: "phone", type: "string", placeholderKey: "forms.placeholders.phone_number" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "hobbies",
    titleKey: "forms.hobby",
    descriptionKey: "forms.sections.hobbies",
    icon: "lucide:heart",
    collectionName: "hobbies",
    keyField: "name",
    entity: "hobby",
    fields: [
      { name: "name", type: "string", placeholderKey: "forms.placeholders.hobby_name" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
  {
    id: "volunteerings",
    titleKey: "forms.volunteering",
    descriptionKey: "forms.sections.volunteering",
    icon: "lucide:hand-heart",
    collectionName: "volunteerings",
    keyField: "role",
    entity: "volunteering",
    fields: [
      { name: "organization", type: "string", placeholderKey: "forms.placeholders.organization" },
      { name: "role", type: "string", placeholderKey: "forms.placeholders.role" },
      { name: "startDate", type: "date", placeholderKey: "forms.placeholders.from" },
      { name: "endDate", type: "date", placeholderKey: "forms.placeholders.to" },
      { name: "city", type: "string", placeholderKey: "forms.placeholders.city" },
      { name: "description", type: "textarea", labelKey: "forms.description", className: "col-span-full" },
    ],
  },
];

// Helper function to get section by ID
export function getFormSection(id: string): FormSectionConfig | undefined {
  return FORM_SECTIONS.find((section) => section.id === id);
}

// Helper function to create empty item for a section
export function createEmptyItem(section: FormSectionConfig, resumeId: number): Record<string, any> {
  const emptyItem: Record<string, any> = {
    resumeId,
    sort: 0, // Will be updated when added to collection
  };

  section.fields.forEach((field) => {
    switch (field.type) {
      case "boolean":
        emptyItem[field.name] = false;
        break;
      case "number":
        emptyItem[field.name] = 0;
        break;
      default:
        emptyItem[field.name] = "";
    }
  });

  return emptyItem;
}
