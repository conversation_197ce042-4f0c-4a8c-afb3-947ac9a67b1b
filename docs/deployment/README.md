# Deployment Documentation

This directory contains deployment guides and configuration for different hosting platforms.

## Files

- **[DEPLOYMENT.md](DEPLOYMENT.md)** - General deployment instructions and best practices
- **[COOLIFY_DEPLOYMENT.md](COOLIFY_DEPLOYMENT.md)** - Coolify-specific deployment configuration

## Deployment Options

### 🐳 Coolify (Recommended)
- Self-hosted deployment platform
- Docker-based deployment
- Automatic SSL certificates
- Environment variable management
- See [Coolify Deployment Guide](COOLIFY_DEPLOYMENT.md)

### ☁️ Vercel
- Next.js optimized hosting
- Automatic deployments from Git
- Serverless functions
- Edge network distribution

### 🚀 Generic Docker
- Containerized deployment
- Portable across hosting providers
- Scalable infrastructure
- See [General Deployment Guide](DEPLOYMENT.md)

## Pre-Deployment Checklist

### 🔧 Environment Setup
- [ ] All environment variables configured
- [ ] Database connection established
- [ ] Authentication webhooks configured
- [ ] External services (Browserless, etc.) set up

### 🔒 Security
- [ ] Secret tokens generated and secure
- [ ] HTTPS enabled
- [ ] CORS policies configured
- [ ] Rate limiting implemented

### 📊 Performance
- [ ] Database optimizations applied
- [ ] Asset optimization enabled
- [ ] Caching strategies implemented
- [ ] CDN configured (if applicable)

### 🧪 Testing
- [ ] All features tested in production environment
- [ ] Payment system verified
- [ ] PDF generation working
- [ ] Email notifications functional

## Environment Variables

Key environment variables for deployment:

```bash
# Database
TURSO_CONNECTION_URL=
TURSO_AUTH_TOKEN=

# Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_WEBHOOK_SECRET=

# Payment
PADDLE_API_KEY=
PADDLE_ENVIRONMENT=

# Features
PDF_TOKEN_SECRET=
NEXT_PUBLIC_APP_URL=
```

See [Environment Setup](../setup/ENV_SETUP.md) for complete list.

## Post-Deployment

### 🔍 Monitoring
- Application performance monitoring
- Error tracking and logging
- Database performance metrics
- User analytics

### 🔄 Updates
- Automated deployment pipelines
- Database migration strategies
- Zero-downtime deployment
- Rollback procedures

### 📈 Scaling
- Database scaling strategies
- CDN implementation
- Caching optimization
- Load balancing

See individual deployment guides for platform-specific instructions.