"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface DelightfulErrorProps {
  isVisible: boolean;
  onClose?: () => void;
  onRetry?: () => void;
  type?: "network" | "upload" | "generation" | "generic" | "permission";
  title?: string;
  message?: string;
  showRetry?: boolean;
  showClose?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

const ERROR_CONFIGS = {
  network: {
    icon: "heroicons:wifi-20-solid",
    color: "text-orange-500",
    bgColor: "from-orange-50 to-yellow-100",
    title: "Oops! Connection hiccup",
    message: "Looks like the internet took a coffee break. Let's try that again!",
    encouragement: "Don't worry, even the best WiFi needs a moment sometimes",
    actionText: "Retry Connection",
    funnyMessages: [
      "The internet is probably just tying its shoes...",
      "Our servers are doing yoga, be right back!",
      "The WiFi is having an existential crisis",
      "Connection went to find itself. It'll be back!",
    ],
  },
  upload: {
    icon: "heroicons:cloud-arrow-up-20-solid",
    color: "text-blue-500",
    bgColor: "from-blue-50 to-sky-100",
    title: "Upload got shy!",
    message: "Your file is playing hard to get. Let's give it another gentle nudge!",
    encouragement: "Sometimes even files need a second chance to shine",
    actionText: "Try Upload Again",
    funnyMessages: [
      "Your file is being a bit dramatic today...",
      "Upload queue is backed up like rush hour traffic",
      "File decided to take the scenic route",
      "Your photo is just making sure it looks perfect!",
    ],
  },
  generation: {
    icon: "heroicons:sparkles-20-solid",
    color: "text-purple-500",
    bgColor: "from-purple-50 to-pink-100",
    title: "AI got creative block!",
    message: "Our AI is having one of those days. Let's inspire it to try again!",
    encouragement: "Even artificial intelligence needs a creative break sometimes",
    actionText: "Inspire AI Again",
    funnyMessages: [
      "AI is staring at a blank page, just like writers do...",
      "Our AI went to find its muse. Back in a jiffy!",
      "Artificial Intelligence is temporarily being very human",
      "AI is reading Shakespeare for inspiration",
    ],
  },
  permission: {
    icon: "heroicons:lock-closed-20-solid",
    color: "text-red-500",
    bgColor: "from-red-50 to-pink-100",
    title: "Access says 'Not today!'",
    message: "Looks like you need special permissions for this adventure!",
    encouragement: "Every superhero needs the right access level",
    actionText: "Request Access",
    funnyMessages: [
      "Access control is being very exclusive today",
      "You need the secret handshake for this feature",
      "Permission bouncer is being extra strict",
      "Access is playing hard to get",
    ],
  },
  generic: {
    icon: "heroicons:exclamation-triangle-20-solid",
    color: "text-amber-500",
    bgColor: "from-amber-50 to-orange-100",
    title: "Something went sideways!",
    message: "Our digital hamsters took an unexpected break. Let's wake them up!",
    encouragement: "Every great app has its quirky moments",
    actionText: "Try Again",
    funnyMessages: [
      "The hamsters powering our servers fell asleep",
      "A wild bug appeared! But we'll catch it",
      "Our code is having a philosophical debate",
      "Error 404: Perfection not found (but we're close!)",
    ],
  },
};

export function DelightfulError({
  isVisible,
  onClose,
  onRetry,
  type = "generic",
  title,
  message,
  showRetry = true,
  showClose = true,
  autoHide = false,
  autoHideDelay = 5000,
  className = "",
}: DelightfulErrorProps) {
  const [currentFunnyMessage, setCurrentFunnyMessage] = useState(0);
  const [showWiggle, setShowWiggle] = useState(false);

  const config = ERROR_CONFIGS[type];
  const finalTitle = title || config.title;
  const finalMessage = message || config.message;

  useEffect(() => {
    if (!isVisible) return;

    // Wiggle animation on appear
    setShowWiggle(true);
    setTimeout(() => setShowWiggle(false), 1000);

    // Cycle through funny messages
    const messageTimer = setInterval(() => {
      setCurrentFunnyMessage((prev) => (prev + 1) % config.funnyMessages.length);
    }, 3000);

    // Auto hide
    let hideTimer: NodeJS.Timeout;
    if (autoHide) {
      hideTimer = setTimeout(() => {
        onClose?.();
      }, autoHideDelay);
    }

    return () => {
      clearInterval(messageTimer);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [isVisible, config.funnyMessages.length, autoHide, autoHideDelay, onClose]);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{
          opacity: 1,
          scale: 1,
          y: 0,
          rotate: showWiggle ? [0, 2, -2, 0] : 0,
        }}
        exit={{ opacity: 0, scale: 0.8, y: -20 }}
        transition={{
          duration: 0.4,
          rotate: { duration: 0.5 },
        }}
        className={`w-full max-w-md mx-auto ${className}`}
      >
        <Card
          className={`border-none shadow-lg bg-gradient-to-br ${config.bgColor} dark:from-default-900 dark:to-default-800`}
        >
          <CardBody className="p-6 text-center">
            {/* Animated Error Icon */}
            <motion.div
              className="mx-auto mb-4 w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, -5, 5, 0],
              }}
              transition={{
                scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                rotate: { duration: 3, repeat: Infinity, ease: "easeInOut" },
              }}
            >
              <motion.div
                animate={{
                  y: [0, -2, 0],
                  scale: [1, 1.05, 1],
                }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              >
                <Icon icon={config.icon} className={`w-8 h-8 ${config.color}`} />
              </motion.div>
            </motion.div>

            {/* Title */}
            <motion.h3
              className="text-xl font-bold text-default-900 mb-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              {finalTitle}
            </motion.h3>

            {/* Main Message */}
            <motion.p
              className="text-default-700 mb-3"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {finalMessage}
            </motion.p>

            {/* Rotating Funny Messages */}
            <div className="h-6 mb-4">
              <AnimatePresence mode="wait">
                <motion.p
                  key={currentFunnyMessage}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.4 }}
                  className="text-sm text-default-600 italic"
                >
                  {config.funnyMessages[currentFunnyMessage]}
                </motion.p>
              </AnimatePresence>
            </div>

            {/* Encouragement */}
            <motion.p
              className={`text-sm font-medium mb-6 ${config.color}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {config.encouragement}
            </motion.p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {showRetry && onRetry && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    color="primary"
                    size="lg"
                    className="font-semibold bg-gradient-to-r from-primary to-secondary hover:shadow-xl transition-all duration-300"
                    startContent={
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Icon icon="heroicons:arrow-path-20-solid" className="w-4 h-4" />
                      </motion.div>
                    }
                    onPress={onRetry}
                  >
                    {config.actionText}
                  </Button>
                </motion.div>
              )}

              {showClose && onClose && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="bordered"
                    size="lg"
                    className="border-default-300 hover:bg-default-100 transition-all duration-300"
                    onPress={onClose}
                  >
                    Dismiss
                  </Button>
                </motion.div>
              )}
            </div>

            {/* Floating comfort elements */}
            <div className="absolute top-4 right-4">
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1,
                }}
              >
                <Icon icon="heroicons:heart-20-solid" className="w-4 h-4 text-red-400/60" />
              </motion.div>
            </div>

            <div className="absolute bottom-4 left-4">
              <motion.div
                animate={{
                  y: [0, -5, 0],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2,
                }}
              >
                <Icon icon="heroicons:face-smile-20-solid" className="w-4 h-4 text-yellow-400/60" />
              </motion.div>
            </div>

            {/* Progress bar for auto-hide */}
            {autoHide && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-1 bg-white/20 overflow-hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <motion.div
                  className="h-full bg-primary/60"
                  initial={{ width: "100%" }}
                  animate={{ width: "0%" }}
                  transition={{
                    duration: autoHideDelay / 1000,
                    ease: "linear",
                  }}
                />
              </motion.div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}

export default DelightfulError;
