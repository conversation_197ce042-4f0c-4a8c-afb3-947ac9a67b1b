import { Badge } from "@heroui/badge";
import { But<PERSON> } from "@heroui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, Mo<PERSON>Header, useDisclosure } from "@heroui/modal";
import { Icon } from "@iconify/react";
import React, { useEffect, useState } from "react";
import { submitContactForm } from "@/actions/contact";
import ContactChannels from "@/app/support/components/ContactChannels";
import ContactForm from "@/app/support/components/ContactForm";
import ContactHero from "@/app/support/components/ContactHero";
import styles from "@/app/support/styles/contact.module.css";
import { checkBusinessHours, contactConfig, getContactChannels } from "@/config/contact";
import { getContactSupportFormCategories, getContactSupportFormPriorities } from "@/config/contact-support";

const SubmitTicket = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: "", content: "" });
  const [chatOnline, setChatOnline] = useState(true);
  const { isOpen: isChatOpen, onOpen: onChatOpen, onOpenChange: onChatOpenChange } = useDisclosure();

  // Get configuration data
  const categories = getContactSupportFormCategories();
  const priorities = getContactSupportFormPriorities();
  const channels = getContactChannels(chatOnline, onChatOpen);

  // Check chat availability
  useEffect(() => {
    const checkChatAvailability = () => {
      setChatOnline(checkBusinessHours());
    };

    checkChatAvailability();
    const interval = setInterval(checkChatAvailability, contactConfig.uploadSettings.checkInterval);

    return () => clearInterval(interval);
  }, []);

  // Clear message handler
  const clearMessage = () => {
    setMessage({ type: "", content: "" });
  };

  const handleSubmit = async (formData: FormData) => {
    setIsLoading(true);
    clearMessage();

    try {
      const result = await submitContactForm(formData);

      if (result.success) {
        setMessage({
          type: "success",
          content: "Your support ticket has been submitted successfully. We'll get back to you soon!",
        });
      } else {
        setMessage({
          type: "error",
          content:
            "error" in result
              ? result.error || "Failed to submit support ticket. Please try again."
              : "Failed to submit support ticket. Please try again.",
        });
      }
    } catch (error) {
      setMessage({ type: "error", content: "Failed to submit support ticket. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className={styles.gridLayout}>
        <div className={styles.channelsColumn}>
          <ContactChannels channels={channels} />
        </div>

        <div className={styles.formColumn}>
          <ContactForm
            categories={categories}
            priorities={priorities}
            onSubmit={handleSubmit}
            isLoading={isLoading}
            message={message}
            onClearMessage={clearMessage}
          />
        </div>
      </div>

      {/* Live Chat Modal */}
      <Modal isOpen={isChatOpen} onOpenChange={onChatOpenChange} size="2xl" scrollBehavior="inside">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <Icon icon="tabler:message-circle" className="w-5 h-5 text-blue-500" />
                  Live Chat
                  <Badge content="" color="success" size="sm" placement="top-right">
                    <div className="w-2 h-2 rounded-full bg-green-500" />
                  </Badge>
                </div>
                <p className="text-sm text-gray-500 font-normal">Chat with our support team</p>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  {/* Chat Interface Placeholder */}
                  <div className="h-64 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Icon icon="tabler:message-dots" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400">Live chat is coming soon</p>
                      <p className="text-sm text-gray-500 mt-2">In the meantime, please email us or submit a ticket</p>
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button
                  color="primary"
                  onPress={() => {
                    onClose();
                    window.open("mailto:<EMAIL>", "_blank");
                  }}
                  startContent={<Icon icon="tabler:mail" className="w-4 h-4" />}
                >
                  Email Support
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default SubmitTicket;
