"use client";
import { But<PERSON>, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { ShareButton } from "@/components/features/sharing";
import { FeatureButton } from "@/components/premium";

interface ResumePreviewControlsProps {
  resumeId: number;
  handleFullScreen: () => void;
  exportPdf: () => void;
  exportError?: string | null;
  isGeneratingPDF?: boolean;
}

export const ResumePreviewControls: React.FC<ResumePreviewControlsProps> = ({
  resumeId,
  exportPdf,
  handleFullScreen,
  exportError = null,
  isGeneratingPDF = false,
}) => {
  return (
    <div className="preview-controls relative flex items-center justify-end p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <div className="flex items-center gap-3">
        {/* Fullscreen Control */}
        <Tooltip content="Fullscreen" delay={500} placement="top">
          <Button
            isIconOnly
            size="sm"
            variant="flat"
            onPress={handleFullScreen}
            className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600"
          >
            <Icon height={16} icon="lucide:fullscreen" width={16} />
          </Button>
        </Tooltip>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <ShareButton resumeId={resumeId} variant="flat" size="sm" />
          <FeatureButton
            featureId="pdf_export_v2"
            onClick={exportPdf}
            icon={<Icon icon="lucide:download" />}
            color={exportError ? "danger" : "primary"}
            size="sm"
            isLoading={isGeneratingPDF}
            disabled={isGeneratingPDF}
          >
            {isGeneratingPDF ? "Generating PDF..." : "Download PDF"}
          </FeatureButton>
        </div>
      </div>

      {/* Error Message - positioned below the controls */}
      {exportError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 text-sm text-danger bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-md flex items-center gap-2 z-10">
          <Icon height={16} icon="lucide:alert-circle" width={16} />
          <span>{exportError}</span>
        </div>
      )}
    </div>
  );
};
