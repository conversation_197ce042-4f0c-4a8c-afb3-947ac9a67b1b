import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import { users } from "@/db/schema";

/**
 * Utility functions for syncing Clerk users with database
 */

export interface ClerkUserData {
  id: string;
  email_addresses: Array<{
    id: string;
    email_address: string;
  }>;
  primary_email_address_id?: string;
  first_name?: string | null;
  last_name?: string | null;
  image_url?: string;
}

/**
 * Sync a user from Clerk to our database
 */
export async function syncUserFromClerk(clerkUserData: ClerkUserData) {
  const { id, email_addresses, first_name, last_name, primary_email_address_id } = clerkUserData;

  const primaryEmail = email_addresses.find((email) => email.id === primary_email_address_id);

  try {
    // Check if user exists
    const existingUser = await db.select().from(users).where(eq(users.clerkId, id)).limit(1);

    if (existingUser.length > 0) {
      // Update existing user
      const [updatedUser] = await db
        .update(users)
        .set({
          emailAddress: primaryEmail?.email_address || "",
          firstName: first_name || "",
          lastName: last_name || "",
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, id))
        .returning();

      return updatedUser;
    } else {
      // Create new user
      const [newUser] = await db
        .insert(users)
        .values({
          id: createId(),
          clerkId: id,
          emailAddress: primaryEmail?.email_address || "",
          firstName: first_name || "",
          lastName: last_name || "",
          isPremium: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      return newUser;
    }
  } catch (error) {
    console.error("Error syncing user from Clerk:", error);
    throw error;
  }
}

/**
 * Delete user from database when deleted from Clerk
 */
export async function deleteUserFromDatabase(clerkUserId: string) {
  try {
    const deletedUser = await db.delete(users).where(eq(users.clerkId, clerkUserId)).returning();

    return deletedUser[0] || null;
  } catch (error) {
    console.error("Error deleting user from database:", error);
    throw error;
  }
}

/**
 * Get user from database by Clerk ID
 */
export async function getUserByClerkId(clerkUserId: string) {
  try {
    const user = await db.select().from(users).where(eq(users.clerkId, clerkUserId)).limit(1);

    return user[0] || null;
  } catch (error) {
    console.error("Error getting user by Clerk ID:", error);
    throw error;
  }
}

/**
 * Update user premium status
 */
export async function updateUserPremiumStatus(
  clerkUserId: string,
  planId: "free" | "pro_monthly" | "pro_yearly",
  paymentData?: {
    paymentId?: string;
    paymentGateway?: string;
    subscriptionId?: string;
    currentPeriodEnd?: string;
  },
) {
  try {
    const [updatedUser] = await db
      .update(users)
      .set({
        planId,
        paymentId: paymentData?.paymentId,
        paymentGateway: paymentData?.paymentGateway,
        subscriptionId: paymentData?.subscriptionId,
        currentPeriodEnd: paymentData?.currentPeriodEnd,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(users.clerkId, clerkUserId))
      .returning();

    return updatedUser;
  } catch (error) {
    console.error("Error updating user premium status:", error);
    throw error;
  }
}
