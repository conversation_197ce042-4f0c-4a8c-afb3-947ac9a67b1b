{"version": "6", "dialect": "sqlite", "id": "87ff6921-473b-41f8-87a0-8cc324adfb26", "prevId": "ac8391a0-4135-4a57-87c6-a2a99b28d276", "tables": {"ab_tests": {"name": "ab_tests", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "feature_id": {"name": "feature_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "traffic_allocation": {"name": "traffic_allocation", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 100}, "variants": {"name": "variants", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "success_metrics": {"name": "success_metrics", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "hypothesis": {"name": "hypothesis", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "results": {"name": "results", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "awards": {"name": "awards", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "issuer": {"name": "issuer", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "date_received": {"name": "date_received", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "certifications": {"name": "certifications", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "issuer": {"name": "issuer", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "date_received": {"name": "date_received", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "contact_submissions": {"name": "contact_submissions", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'new'"}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "response_message": {"name": "response_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "responded_at": {"name": "responded_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "educations": {"name": "educations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "institution": {"name": "institution", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_current": {"name": "is_current", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "startDate": {"name": "startDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "endDate": {"name": "endDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "field_of_study": {"name": "field_of_study", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "experiences": {"name": "experiences", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "startDate": {"name": "startDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "endDate": {"name": "endDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_current": {"name": "is_current", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "feature_analytics": {"name": "feature_analytics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "feature_id": {"name": "feature_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "unique_users": {"name": "unique_users", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "total_usage": {"name": "total_usage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "successful_usage": {"name": "successful_usage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "blocked_usage": {"name": "blocked_usage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "premium_users": {"name": "premium_users", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "free_users": {"name": "free_users", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "average_duration": {"name": "average_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "conversion_rate": {"name": "conversion_rate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"feature_analytics_feature_date_idx": {"name": "feature_analytics_feature_date_idx", "columns": ["feature_id", "date"], "isUnique": false}, "feature_analytics_date_idx": {"name": "feature_analytics_date_idx", "columns": ["date"], "isUnique": false}, "feature_analytics_feature_id_idx": {"name": "feature_analytics_feature_id_idx", "columns": ["feature_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "feature_flags": {"name": "feature_flags", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_enabled": {"name": "is_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "requires_premium": {"name": "requires_premium", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'exploration'"}, "lifecycle": {"name": "lifecycle", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'stable'"}, "rollout_percentage": {"name": "rollout_percentage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 100}, "target_audience": {"name": "target_audience", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "block_message": {"name": "block_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "global_limit": {"name": "global_limit", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "deprecated_at": {"name": "deprecated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"feature_flags_key_unique": {"name": "feature_flags_key_unique", "columns": ["key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "feature_usage": {"name": "feature_usage", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "feature_id": {"name": "feature_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ab_test_variant": {"name": "ab_test_variant", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"feature_usage_user_id_idx": {"name": "feature_usage_user_id_idx", "columns": ["user_id"], "isUnique": false}, "feature_usage_feature_id_idx": {"name": "feature_usage_feature_id_idx", "columns": ["feature_id"], "isUnique": false}, "feature_usage_created_at_idx": {"name": "feature_usage_created_at_idx", "columns": ["createdAt"], "isUnique": false}, "feature_usage_user_feature_idx": {"name": "feature_usage_user_feature_idx", "columns": ["user_id", "feature_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "hobbies": {"name": "hobbies", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "languages": {"name": "languages", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "proficiency": {"name": "proficiency", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 10}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "profiles": {"name": "profiles", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "network": {"name": "network", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "projects": {"name": "projects", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "client": {"name": "client", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "startDate": {"name": "startDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "endDate": {"name": "endDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "references": {"name": "references", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "resumes": {"name": "resumes", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "birthDate": {"name": "birthDate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "street": {"name": "street", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "showPhoto": {"name": "showPhoto", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "color_scheme": {"name": "color_scheme", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'blue'"}, "font_family": {"name": "font_family", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'inter'"}, "spacing": {"name": "spacing", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "margins": {"name": "margins", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "thumbnail": {"name": "thumbnail", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "template_id": {"name": "template_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "share_tokens": {"name": "share_tokens", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"share_tokens_token_unique": {"name": "share_tokens_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "skills": {"name": "skills", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "proficiency": {"name": "proficiency", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "support_tickets": {"name": "support_tickets", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "ticket_number": {"name": "ticket_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'open'"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "resolution_message": {"name": "resolution_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resolved_at": {"name": "resolved_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"support_tickets_ticket_number_unique": {"name": "support_tickets_ticket_number_unique", "columns": ["ticket_number"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "templates": {"name": "templates", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ats_score": {"name": "ats_score", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "features": {"name": "features", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"templates_slug_unique": {"name": "templates_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_feature_assignments": {"name": "user_feature_assignments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "feature_id": {"name": "feature_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_enabled": {"name": "is_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ab_test_id": {"name": "ab_test_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "variant": {"name": "variant", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_at": {"name": "assigned_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_feature_assignments_user_feature_idx": {"name": "user_feature_assignments_user_feature_idx", "columns": ["user_id", "feature_id"], "isUnique": false}, "user_feature_assignments_user_id_idx": {"name": "user_feature_assignments_user_id_idx", "columns": ["user_id"], "isUnique": false}, "user_feature_assignments_feature_id_idx": {"name": "user_feature_assignments_feature_id_idx", "columns": ["feature_id"], "isUnique": false}, "user_feature_assignments_ab_test_id_idx": {"name": "user_feature_assignments_ab_test_id_idx", "columns": ["ab_test_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_address": {"name": "email_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_premium": {"name": "is_premium", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "purchased_at": {"name": "purchased_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_gateway": {"name": "payment_gateway", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paddle_customer_id": {"name": "paddle_customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "has_used_free_ai": {"name": "has_used_free_ai", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "street": {"name": "street", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_clerk_id_unique": {"name": "users_clerk_id_unique", "columns": ["clerk_id"], "isUnique": true}, "users_email_address_unique": {"name": "users_email_address_unique", "columns": ["email_address"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "volunteerings": {"name": "volunteerings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "organization": {"name": "organization", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "startDate": {"name": "startDate", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "endDate": {"name": "endDate", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort": {"name": "sort", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "website_templates": {"name": "website_templates", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"website_templates_slug_unique": {"name": "website_templates_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "websites": {"name": "websites", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ispublic": {"name": "ispublic", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "analytics": {"name": "analytics", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "background_pattern": {"name": "background_pattern", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'none'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "website_template_id": {"name": "website_template_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"websites_slug_unique": {"name": "websites_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}