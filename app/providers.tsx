"use client";

import { App<PERSON>rogress<PERSON><PERSON>ider as Progress<PERSON>rovider } from "@bprogress/next";
import { useRouter } from "@bprogress/next/app";
import { enUS } from "@clerk/localizations";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { HeroUIProvider } from "@heroui/react";
import type { ThemeProviderProps } from "next-themes";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { PostHogProvider } from "posthog-js/react";
import * as React from "react";
import { Toaster } from "react-hot-toast";
import TRPCProvider from "@/app/_trpc/Provider";
import { GlobalLoadingOverlay } from "@/components/shared";
import { UpgradeModalProvider } from "@/contexts/upgrade-modal-context";

export interface ProvidersProps {
  children: React.ReactNode;
  themeProps?: ThemeProviderProps;
}

declare module "@react-types/shared" {
  interface RouterConfig {
    routerOptions: NonNullable<Parameters<ReturnType<typeof useRouter>["push"]>[1]>;
  }
}

function InnerProviders({ children, themeProps }: ProvidersProps) {
  const router = useRouter();

  return (
    <PostHogProvider
      apiKey={process.env.NEXT_PUBLIC_POSTHOG_KEY || ""}
      options={{
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || "https://us.i.posthog.com",
      }}
    >
      <ClerkProvider
        localization={enUS}
        appearance={{
          // Use your app's colors for consistency
          variables: {
            colorPrimary: "oklch(58.5% 0.233 277.117)", // Your app's primary color
          },
        }}
      >
        <TRPCProvider>
          <HeroUIProvider navigate={router.push}>
            <NextThemesProvider {...themeProps}>
              <UpgradeModalProvider>
                <div>
                  <Toaster position="top-right" reverseOrder={false} />
                </div>
                <GlobalLoadingOverlay />
                {children}
              </UpgradeModalProvider>
            </NextThemesProvider>
          </HeroUIProvider>
        </TRPCProvider>
      </ClerkProvider>
    </PostHogProvider>
  );
}

export function Providers({ children, themeProps }: ProvidersProps) {
  return (
    <ProgressProvider height="4px" color="oklch(58.5% 0.233 277.117)" options={{ showSpinner: false }} shallowRouting>
      <InnerProviders themeProps={themeProps}>{children}</InnerProviders>
    </ProgressProvider>
  );
}
