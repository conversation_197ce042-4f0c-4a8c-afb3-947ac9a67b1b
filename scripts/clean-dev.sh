#!/bin/bash

# Clean development script for Next.js build manifest issues

echo "🧹 Cleaning Next.js development environment..."

# Stop any running Next.js processes
echo "Stopping Next.js processes..."
pkill -f "next dev" || true

# Remove Next.js cache
echo "Removing .next directory..."
rm -rf .next

# Remove node_modules cache (optional - uncomment if needed)
# echo "Removing node_modules..."
# rm -rf node_modules

# Clear npm cache (optional)
# echo "Clearing npm cache..."
# npm cache clean --force

# Reinstall dependencies (optional - uncomment if needed)
# echo "Reinstalling dependencies..."
# npm install

echo "✅ Cleanup complete! You can now run 'npm run dev' to start fresh."