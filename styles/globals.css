@import "tailwindcss";
@import "./animations.css";
@plugin '../hero.ts';
/* Note: You may need to change the path to fit your project structure */
@source '../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));
@config "../tailwind.config.js";

.wysiwyg {
  @apply max-w-none prose-headings:mb-2 prose-headings:mt-0 prose-p:mb-2 prose-p:mt-0 prose-p:leading-normal prose-a:break-all prose-ol:mb-2 prose-ol:mt-0 prose-ul:mb-2 prose-ul:mt-0 prose-li:mb-2 prose-li:mt-0 prose-li:leading-normal prose-img:mb-2 prose-img:mt-0 prose-hr:mb-2 prose-hr:mt-0;
}

/* Sidebar responsive layout styles */
.sidebar-expanded {
  margin-left: 18rem; /* 288px - expanded sidebar width */
}

.sidebar-collapsed {
  margin-left: 5rem; /* 80px - collapsed sidebar width */
}

@media (max-width: 1024px) {
  .sidebar-expanded,
  .sidebar-collapsed {
    margin-left: 0;
  }
}
