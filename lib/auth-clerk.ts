import { auth, currentUser } from "@clerk/nextjs/server";
import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import { db } from "@/db";
import { users } from "@/db/schema";

export async function getAuthenticatedUser() {
  const { userId } = await auth();

  if (!userId) {
    return null;
  }

  const clerkUser = await currentUser();

  if (!clerkUser) {
    return null;
  }

  // Find or create user in our database
  let user = (await db.select().from(users).where(eq(users.clerkId, userId)).limit(1))[0];

  if (!user) {
    // Create new user with complete profile data
    const primaryEmail = clerkUser.emailAddresses.find((email) => email.id === clerkUser.primaryEmailAddress?.id);

    const [newUser] = await db
      .insert(users)
      .values({
        id: createId(),
        clerkId: userId,
        emailAddress: primaryEmail?.emailAddress || "",
        firstName: clerkUser.firstName || "",
        lastName: clerkUser.lastName || "",
        isPremium: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();
    user = newUser;
  } else {
    // Update user data if it has changed (sync from Clerk)
    const primaryEmail = clerkUser.emailAddresses.find((email) => email.id === clerkUser.primaryEmailAddress?.id);

    const shouldUpdate =
      user.emailAddress !== (primaryEmail?.emailAddress || "") ||
      user.firstName !== (clerkUser.firstName || "") ||
      user.lastName !== (clerkUser.lastName || "");

    if (shouldUpdate) {
      const [updatedUser] = await db
        .update(users)
        .set({
          emailAddress: primaryEmail?.emailAddress || "",
          firstName: clerkUser.firstName || "",
          lastName: clerkUser.lastName || "",
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, userId))
        .returning();
      user = updatedUser;
    }
  }

  return user;
}

export async function requireAuth() {
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect("/sign-in");
  }

  return user;
}
