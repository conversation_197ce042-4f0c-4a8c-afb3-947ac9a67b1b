"use client";

import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import CreateResumeButton from "./createResumeButton";

const ENCOURAGING_MESSAGES = [
  "Every great career starts with a single step",
  "Your dream job is just one resume away",
  "Today's the perfect day to start your journey",
  "You've got this! Let's build something amazing",
  "Your future self will thank you for starting today",
];

const FLOATING_ICONS = [
  "heroicons:star-20-solid",
  "heroicons:heart-20-solid",
  "heroicons:sparkles-20-solid",
  "heroicons:trophy-20-solid",
  "heroicons:rocket-launch-20-solid",
];

export default function ResumeEmptyState() {
  const [currentMessage, setCurrentMessage] = useState(0);
  const [showFloatingIcons, setShowFloatingIcons] = useState(false);

  useEffect(() => {
    // Cycle through encouraging messages
    const messageTimer = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % ENCOURAGING_MESSAGES.length);
    }, 4000);

    // Show floating icons after initial load
    const iconTimer = setTimeout(() => {
      setShowFloatingIcons(true);
    }, 1000);

    return () => {
      clearInterval(messageTimer);
      clearTimeout(iconTimer);
    };
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center px-4 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-secondary rounded-full blur-2xl"></div>
      </div>

      {/* Floating motivational icons */}
      <AnimatePresence>
        {showFloatingIcons &&
          FLOATING_ICONS.map((icon, index) => (
            <motion.div
              key={icon}
              initial={{ opacity: 0, scale: 0, x: 0, y: 0 }}
              animate={{
                opacity: [0, 0.3, 0],
                scale: [0, 1, 0],
                x: Math.cos((index * 72 * Math.PI) / 180) * 150,
                y: Math.sin((index * 72 * Math.PI) / 180) * 150,
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                delay: index * 0.5,
                ease: "easeInOut",
              }}
              className="absolute z-0"
            >
              <Icon icon={icon} className="w-6 h-6 text-primary" />
            </motion.div>
          ))}
      </AnimatePresence>

      {/* Main content */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 max-w-md"
      >
        {/* Animated main icon */}
        <motion.div
          className="mx-auto mb-6 w-20 h-20 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-full flex items-center justify-center relative"
          animate={{
            scale: [1, 1.05, 1],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
            rotate: { duration: 4, repeat: Infinity, ease: "easeInOut" },
          }}
        >
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <Icon icon="heroicons:document-text-20-solid" className="w-10 h-10 text-primary" />
          </motion.div>

          {/* Plus badge with pulse */}
          <motion.div
            className="absolute -bottom-1 -right-1 w-6 h-6 bg-primary rounded-full flex items-center justify-center shadow-lg"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
          >
            <Icon icon="heroicons:plus-20-solid" className="w-3 h-3 text-white" />
          </motion.div>
        </motion.div>

        {/* Dynamic title */}
        <motion.h3
          className="text-2xl font-bold text-default-900 mb-3"
          key={`title-${currentMessage}`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.5 }}
        >
          Create Your First Resume
        </motion.h3>

        {/* Cycling encouraging messages */}
        <AnimatePresence mode="wait">
          <motion.p
            key={currentMessage}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -15 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="text-lg text-primary font-medium mb-2 italic"
          >
            "{ENCOURAGING_MESSAGES[currentMessage]}"
          </motion.p>
        </AnimatePresence>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.8 }}
          className="text-default-600 mb-8 leading-relaxed"
        >
          Build a professional, ATS-optimized resume in minutes with our easy-to-use templates and AI-powered features.
        </motion.p>

        {/* Enhanced action button */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <CreateResumeButton
            className="px-8 bg-gradient-to-r from-primary to-secondary hover:shadow-2xl transition-all duration-300"
            size="lg"
          />
        </motion.div>

        {/* Success statistics to inspire confidence */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="flex items-center justify-center gap-6 mt-8 text-sm text-default-500"
        >
          <div className="flex items-center gap-1">
            <Icon icon="heroicons:users-20-solid" className="w-4 h-4 text-success" />
            <span>10k+ users hired</span>
          </div>
          <div className="flex items-center gap-1">
            <Icon icon="heroicons:clock-20-solid" className="w-4 h-4 text-primary" />
            <span>5 min to create</span>
          </div>
        </motion.div>
      </motion.div>

      {/* Subtle breathing background */}
      <motion.div
        className="absolute inset-0 bg-gradient-radial from-primary/5 to-transparent opacity-50"
        animate={{ scale: [1, 1.02, 1] }}
        transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        style={{ zIndex: -1 }}
      />
    </div>
  );
}
