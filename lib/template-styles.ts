// Website template visual characteristics and metadata
export interface TemplateStyle {
  name: string;
  slug: string;
  description: string;
  category: string;
  bestFor: string;
  features: string[];
  colors: string[];
  icon: string;
  typography: {
    heading: string;
    body: string;
    accent: string;
  };
  styleKeywords: string[];
}

export const websiteTemplateStyles: Record<string, TemplateStyle> = {
  elegant: {
    name: "Elegant",
    slug: "elegant",
    description: "Modern glassmorphism design with animated backgrounds and sophisticated visual effects",
    category: "Modern",
    bestFor: "Creative Professionals, Designers, Freelancers",
    features: ["Glassmorphism Effects", "Animated Backgrounds", "Modern Cards", "Gradient Accents"],
    colors: ["#3B82F6", "#6366F1", "#8B5CF6", "#EC4899"],
    icon: "✨",
    typography: {
      heading: "text-5xl font-bold tracking-tight",
      body: "text-lg leading-relaxed",
      accent: "text-blue-700 font-semibold",
    },
    styleKeywords: ["Modern", "Creative", "Animated", "Glassmorphism"],
  },
  professional: {
    name: "Professional",
    slug: "professional",
    description: "Professional corporate design with clean layout and business-focused structure",
    category: "Professional",
    bestFor: "Business Professionals, Corporate Executives, Consultants",
    features: ["Two-Column Layout", "Professional Header", "Sidebar Design", "Corporate Colors"],
    colors: ["#1E293B", "#475569", "#3B82F6", "#10B981"],
    icon: "🚀",
    typography: {
      heading: "text-5xl font-bold mb-4 tracking-tight",
      body: "text-lg leading-relaxed",
      accent: "text-slate-700 font-semibold",
    },
    styleKeywords: ["Professional", "Corporate", "Clean", "Business"],
  },
  modern: {
    name: "Modern",
    slug: "modern",
    description: "Executive-level design with premium styling and sophisticated visual hierarchy",
    category: "Executive",
    bestFor: "C-Level Executives, Senior Leadership, Board Members",
    features: ["Premium Typography", "Executive Styling", "Large Photos", "Achievement Focus"],
    colors: ["#0F172A", "#1E293B", "#3B82F6", "#F59E0B"],
    icon: "👔",
    typography: {
      heading: "text-6xl font-bold tracking-tight",
      body: "text-xl leading-relaxed",
      accent: "text-blue-800 font-bold",
    },
    styleKeywords: ["Executive", "Premium", "Leadership", "Sophisticated"],
  },
  creative: {
    name: "Creative",
    slug: "creative",
    description: "Vibrant and energetic design with bold colors and dynamic visual elements",
    category: "Creative",
    bestFor: "Artists, Creative Directors, Marketing Professionals, Content Creators",
    features: ["Vibrant Colors", "Dynamic Layouts", "Creative Typography", "Visual Impact"],
    colors: ["#EC4899", "#8B5CF6", "#3B82F6", "#F59E0B"],
    icon: "🎨",
    typography: {
      heading: "text-5xl font-bold tracking-wide",
      body: "text-lg leading-relaxed",
      accent: "text-purple-700 font-bold",
    },
    styleKeywords: ["Creative", "Vibrant", "Dynamic", "Artistic"],
  },
  minimal: {
    name: "Minimal",
    slug: "minimal",
    description: "Clean and focused design emphasizing content over decoration with elegant simplicity",
    category: "Minimalist",
    bestFor: "Researchers, Academics, Writers, Consultants",
    features: ["Clean Typography", "Content Focus", "Subtle Styling", "Readable Layout"],
    colors: ["#374151", "#6B7280", "#3B82F6", "#10B981"],
    icon: "⚪",
    typography: {
      heading: "text-4xl font-light tracking-wide",
      body: "text-base leading-relaxed",
      accent: "text-gray-700 font-medium",
    },
    styleKeywords: ["Minimal", "Clean", "Simple", "Content-focused"],
  },
  tech: {
    name: "Tech",
    slug: "tech",
    description: "Dark theme with terminal aesthetics and code-inspired design elements",
    category: "Technical",
    bestFor: "Software Engineers, DevOps Engineers, Tech Leads, System Architects",
    features: ["Dark Theme", "Code Aesthetics", "Terminal Style", "Tech Colors"],
    colors: ["#0F172A", "#1E293B", "#06B6D4", "#10B981"],
    icon: "💻",
    typography: {
      heading: "text-4xl font-mono font-bold",
      body: "text-base font-mono leading-relaxed",
      accent: "text-cyan-400 font-semibold",
    },
    styleKeywords: ["Technical", "Dark", "Code", "Developer"],
  },
  academic: {
    name: "Academic",
    slug: "academic",
    description: "Professional serif-based design optimized for academic and research professionals",
    category: "Academic",
    bestFor: "Professors, Researchers, PhD Candidates, Academic Professionals",
    features: ["Serif Typography", "Publication Focus", "Academic Structure", "Research Emphasis"],
    colors: ["#1F2937", "#374151", "#3B82F6", "#059669"],
    icon: "🎓",
    typography: {
      heading: "text-4xl font-serif font-bold",
      body: "text-base font-serif leading-relaxed",
      accent: "text-blue-700 font-semibold",
    },
    styleKeywords: ["Academic", "Research", "Serif", "Professional"],
  },
};

export const getTemplateStyle = (slug: string): TemplateStyle | null => {
  return websiteTemplateStyles[slug] || null;
};

export const getTemplatesByCategory = (category: string): TemplateStyle[] => {
  return Object.values(websiteTemplateStyles).filter((template) => template.category === category);
};

export const getAllCategories = (): string[] => {
  const categories = Object.values(websiteTemplateStyles).map((t) => t.category);
  return Array.from(new Set(categories)).sort();
};
