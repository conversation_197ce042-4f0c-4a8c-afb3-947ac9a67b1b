PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_educations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`city` text,
	`country` text,
	`institution` text,
	`is_current` integer DEFAULT 0,
	`start_date` text DEFAULT '' NOT NULL,
	`end_date` text DEFAULT '' NOT NULL,
	`description` text,
	`degree` text,
	`field_of_study` text,
	`website` text,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_educations`("id", "city", "country", "institution", "is_current", "start_date", "end_date", "description", "degree", "field_of_study", "website", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "city", "country", "institution", "is_current", "start_date", "end_date", "description", "degree", "field_of_study", "website", "sort", "created_at", "updated_at", "resume_id" FROM `educations`;--> statement-breakpoint
DROP TABLE `educations`;--> statement-breakpoint
ALTER TABLE `__new_educations` RENAME TO `educations`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE TABLE `__new_experiences` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`company` text,
	`start_date` text DEFAULT '' NOT NULL,
	`end_date` text DEFAULT '' NOT NULL,
	`description` text,
	`city` text,
	`country` text,
	`is_current` integer DEFAULT 0 NOT NULL,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_experiences`("id", "title", "company", "start_date", "end_date", "description", "city", "country", "is_current", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "title", "company", "start_date", "end_date", "description", "city", "country", "is_current", "sort", "created_at", "updated_at", "resume_id" FROM `experiences`;--> statement-breakpoint
DROP TABLE `experiences`;--> statement-breakpoint
ALTER TABLE `__new_experiences` RENAME TO `experiences`;--> statement-breakpoint
CREATE TABLE `__new_resumes` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`first_name` text DEFAULT '' NOT NULL,
	`last_name` text DEFAULT '' NOT NULL,
	`job_title` text DEFAULT '' NOT NULL,
	`address` text DEFAULT '' NOT NULL,
	`phone` text DEFAULT '' NOT NULL,
	`email` text DEFAULT '' NOT NULL,
	`website` text DEFAULT '' NOT NULL,
	`bio` text DEFAULT '' NOT NULL,
	`birth_date` text DEFAULT '' NOT NULL,
	`city` text DEFAULT '' NOT NULL,
	`street` text DEFAULT '' NOT NULL,
	`country` text DEFAULT '' NOT NULL,
	`showPhoto` integer DEFAULT 0 NOT NULL,
	`color_scheme` text DEFAULT 'blue' NOT NULL,
	`font_family` text DEFAULT 'inter' NOT NULL,
	`spacing` text DEFAULT 'normal' NOT NULL,
	`margins` text DEFAULT 'normal' NOT NULL,
	`photo` text DEFAULT '' NOT NULL,
	`thumbnail` text DEFAULT '' NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`user_id` text NOT NULL,
	`template_id` integer DEFAULT 1 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_resumes`("id", "title", "first_name", "last_name", "job_title", "address", "phone", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id") SELECT "id", "title", "first_name", "last_name", "job_title", "address", "phone", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id" FROM `resumes`;--> statement-breakpoint
DROP TABLE `resumes`;--> statement-breakpoint
ALTER TABLE `__new_resumes` RENAME TO `resumes`;--> statement-breakpoint
CREATE TABLE `__new_websites` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`slug` text NOT NULL,
	`is_public` integer DEFAULT 0 NOT NULL,
	`analytics` integer DEFAULT 0 NOT NULL,
	`background_pattern` text DEFAULT 'none' NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`user_id` text NOT NULL,
	`resume_id` integer NOT NULL,
	`website_template_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_websites`("id", "slug", "is_public", "analytics", "background_pattern", "created_at", "updated_at", "user_id", "resume_id", "website_template_id") SELECT "id", "slug", "is_public", "analytics", "background_pattern", "created_at", "updated_at", "user_id", "resume_id", "website_template_id" FROM `websites`;--> statement-breakpoint
DROP TABLE `websites`;--> statement-breakpoint
ALTER TABLE `__new_websites` RENAME TO `websites`;--> statement-breakpoint
CREATE UNIQUE INDEX `websites_slug_unique` ON `websites` (`slug`);