"use client";

import { useUser } from "@clerk/nextjs";
import { useCallback, useEffect, useState } from "react";
import {
  getAllFeatureFlags,
  getFeatureFlagPayload,
  getFeatureFlagVariant,
  isFeatureFlagEnabled,
  onFeatureFlags,
  reloadFeatureFlags,
  trackFeatureFlagExposure,
  trackFeatureFlagUsage,
} from "@/lib/posthog-analytics";

// Define feature flag keys as a type for better type safety
export type FeatureFlagKey =
  | "pdf_export_v2"
  | "website_builder"
  | "ai_generation_enhanced"
  | "premium_templates"
  | "analytics_dashboard"
  | "social_sharing"
  | "real_time_collaboration"
  | "custom_branding"
  | "advanced_export"
  | "template_customization"
  | "resume_scoring"
  | "job_matching"
  | "interview_prep"
  | "portfolio_builder"
  | "referral_program";

export interface FeatureFlagState {
  isLoading: boolean;
  flags: Record<string, boolean | string>;
  error?: string;
}

/**
 * Hook to check if a feature flag is enabled
 * Uses PostHog's native isFeatureEnabled with React state management
 */
export function useFeatureFlagEnabled(
  flagKey: FeatureFlagKey,
  defaultValue: boolean = false,
): {
  isEnabled: boolean;
  isLoading: boolean;
  variant: string | boolean;
  trackUsage: () => void;
} {
  const { user, isLoaded } = useUser();
  const [isEnabled, setIsEnabled] = useState<boolean>(defaultValue);
  const [variant, setVariant] = useState<string | boolean>(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check feature flag status
  useEffect(() => {
    if (!isLoaded) return;

    const checkFlag = () => {
      const enabled = isFeatureFlagEnabled(flagKey, defaultValue);
      const flagVariant = getFeatureFlagVariant(flagKey);

      setIsEnabled(enabled);
      setVariant(flagVariant);
      setIsLoading(false);

      // Track exposure automatically when flag is evaluated
      if (enabled !== undefined) {
        trackFeatureFlagExposure(flagKey, flagVariant, {
          userId: user?.id,
          userEmail: user?.emailAddresses[0]?.emailAddress,
          isPremium: user?.publicMetadata?.isPremium === true,
          exposureType: "automatic",
        });
      }
    };

    // Initial check
    checkFlag();

    // Set up callback for when flags are updated
    onFeatureFlags(() => {
      checkFlag();
    });
  }, [flagKey, defaultValue, isLoaded, user]);

  // Reload flags when user changes (authentication state)
  useEffect(() => {
    if (user) {
      reloadFeatureFlags();
    }
  }, [user]);

  const trackUsage = useCallback(() => {
    if (isEnabled) {
      trackFeatureFlagUsage(flagKey, variant, {
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
      });
    }
  }, [flagKey, variant, isEnabled, user]);

  return {
    isEnabled,
    isLoading,
    variant,
    trackUsage,
  };
}

/**
 * Hook to get feature flag payload/configuration
 * Useful for feature flags that carry configuration data
 */
export function useFeatureFlagPayload<T = any>(
  flagKey: FeatureFlagKey,
  defaultPayload?: T,
): {
  payload: T | undefined;
  isLoading: boolean;
  isEnabled: boolean;
  trackUsage: () => void;
} {
  const { user, isLoaded } = useUser();
  const [payload, setPayload] = useState<T | undefined>(defaultPayload);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isLoaded) return;

    const checkPayload = () => {
      const flagPayload = getFeatureFlagPayload(flagKey, defaultPayload);
      const enabled = isFeatureFlagEnabled(flagKey, false);

      setPayload(flagPayload);
      setIsEnabled(enabled);
      setIsLoading(false);
    };

    // Initial check
    checkPayload();

    // Set up callback for when flags are updated
    onFeatureFlags(() => {
      checkPayload();
    });
  }, [flagKey, defaultPayload, isLoaded]);

  // Reload flags when user changes
  useEffect(() => {
    if (user) {
      reloadFeatureFlags();
    }
  }, [user]);

  const trackUsage = useCallback(() => {
    if (isEnabled) {
      trackFeatureFlagUsage(flagKey, (payload as string | boolean) || false, {
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
        payloadType: typeof payload,
      });
    }
  }, [flagKey, payload, isEnabled, user]);

  return {
    payload,
    isLoading,
    isEnabled,
    trackUsage,
  };
}

/**
 * Hook to get feature flag variant key
 * Useful for A/B testing scenarios
 */
export function useFeatureFlagVariantKey(
  flagKey: FeatureFlagKey,
  defaultVariant?: string,
): {
  variant: string | boolean;
  isLoading: boolean;
  isEnabled: boolean;
  trackUsage: () => void;
} {
  const { user, isLoaded } = useUser();
  const [variant, setVariant] = useState<string | boolean>(defaultVariant || false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isLoaded) return;

    const checkVariant = () => {
      const flagVariant = getFeatureFlagVariant(flagKey, defaultVariant);
      const enabled = isFeatureFlagEnabled(flagKey, false);

      setVariant(flagVariant);
      setIsEnabled(enabled);
      setIsLoading(false);
    };

    // Initial check
    checkVariant();

    // Set up callback for when flags are updated
    onFeatureFlags(() => {
      checkVariant();
    });
  }, [flagKey, defaultVariant, isLoaded]);

  // Reload flags when user changes
  useEffect(() => {
    if (user) {
      reloadFeatureFlags();
    }
  }, [user]);

  const trackUsage = useCallback(() => {
    if (isEnabled) {
      trackFeatureFlagUsage(flagKey, variant, {
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
        variantType: typeof variant,
      });
    }
  }, [flagKey, variant, isEnabled, user]);

  return {
    variant,
    isLoading,
    isEnabled,
    trackUsage,
  };
}

/**
 * Hook to get all feature flags state
 * Useful for dashboard/admin interfaces
 */
export function useAllFeatureFlags(): FeatureFlagState {
  const { user, isLoaded } = useUser();
  const [state, setState] = useState<FeatureFlagState>({
    isLoading: true,
    flags: {},
  });

  useEffect(() => {
    if (!isLoaded) return;

    const updateFlags = (flags: Record<string, boolean | string>) => {
      setState({
        isLoading: false,
        flags,
      });
    };

    // Set up callback for when flags are loaded/updated
    onFeatureFlags(updateFlags);

    // Get initial flags
    const initialFlags = getAllFeatureFlags();
    setState({
      isLoading: false,
      flags: initialFlags,
    });
  }, [isLoaded]);

  // Reload flags when user changes
  useEffect(() => {
    if (user) {
      reloadFeatureFlags();
    }
  }, [user]);

  return state;
}

/**
 * Hook for feature flag experiments
 * Combines flag checking with automatic tracking
 */
export function useFeatureExperiment(
  flagKey: FeatureFlagKey,
  experimentName: string,
  defaultVariant: string = "control",
) {
  const { variant, isLoading, isEnabled, trackUsage } = useFeatureFlagVariantKey(flagKey, defaultVariant);
  const { user } = useUser();

  // Track experiment exposure automatically
  useEffect(() => {
    if (!isLoading && isEnabled) {
      trackFeatureFlagUsage(flagKey, variant, {
        experimentName,
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
        exposureType: "automatic",
      });
    }
  }, [flagKey, variant, isLoading, isEnabled, experimentName, user]);

  const trackConversion = useCallback(
    (conversionEvent: string, value?: number) => {
      trackFeatureFlagUsage(flagKey, variant, {
        experimentName,
        conversionEvent,
        conversionValue: value,
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
        eventType: "conversion",
      });
    },
    [flagKey, variant, experimentName, user],
  );

  return {
    variant: variant as string,
    isLoading,
    isEnabled,
    isControl: variant === defaultVariant,
    isVariant: variant !== defaultVariant && variant !== false,
    trackConversion,
    trackUsage,
  };
}

/**
 * Hook for gradual rollout scenarios
 * Provides additional rollout-specific metadata
 */
export function useFeatureRollout(flagKey: FeatureFlagKey, rolloutName: string) {
  const { isEnabled, isLoading, variant, trackUsage } = useFeatureFlagEnabled(flagKey, false);
  const { payload } = useFeatureFlagPayload(flagKey);
  const { user } = useUser();

  // Track rollout participation
  useEffect(() => {
    if (!isLoading && isEnabled) {
      trackFeatureFlagUsage(flagKey, variant, {
        rolloutName,
        rolloutStage: payload?.stage || "unknown",
        userId: user?.id,
        userEmail: user?.emailAddresses[0]?.emailAddress,
        isPremium: user?.publicMetadata?.isPremium === true,
        participationType: "rollout",
      });
    }
  }, [flagKey, variant, isLoading, isEnabled, rolloutName, payload, user]);

  return {
    isEnabled,
    isLoading,
    variant,
    rolloutConfig: payload,
    trackUsage,
    trackFeedback: useCallback(
      (feedbackType: "positive" | "negative" | "neutral", comment?: string) => {
        trackFeatureFlagUsage(flagKey, variant, {
          rolloutName,
          feedbackType,
          feedbackComment: comment,
          userId: user?.id,
          eventType: "feedback",
        });
      },
      [flagKey, variant, rolloutName, user],
    ),
  };
}
