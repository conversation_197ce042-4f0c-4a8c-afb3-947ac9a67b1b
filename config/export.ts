/**
 * Export configuration settings
 */
export const EXPORT_CONFIG = {
  /**
   * Maximum number of resumes that can be exported at once
   */
  MAX_EXPORT_LIMIT: 20,

  /**
   * Maximum number of websites that can be exported at once
   */
  MAX_WEBSITE_EXPORT_LIMIT: 15,

  /**
   * Export file naming configuration
   */
  FILE_NAMING: {
    RESUME_PREFIX: "resumes_export",
    WEBSITE_PREFIX: "websites_export",
    DATE_FORMAT: "YYYY-MM-DD", // ISO date format
  },

  /**
   * Export API endpoints
   */
  ENDPOINTS: {
    RESUME_EXPORT_ALL: "/api/pdf/export-all",
    WEBSITE_EXPORT_ALL: "/api/website/export-all",
  },

  /**
   * Export timeouts (in milliseconds)
   */
  TIMEOUTS: {
    DEFAULT: 5 * 60 * 1000, // 5 minutes
    LARGE_EXPORT: 10 * 60 * 1000, // 10 minutes for large exports
  },

  /**
   * Messages for export operations
   */
  MESSAGES: {
    PREPARING: "Preparing to export items...",
    GENERATING: (count: number, type: string) => `Generating PDFs for ${count} ${type}${count > 1 ? "s" : ""}...`,
    SUCCESS: (count: number, type: string) => `Successfully exported ${count} ${type}${count > 1 ? "s" : ""}!`,
    NO_ITEMS: "No items to export",
    LIMIT_EXCEEDED: (limit: number) => `Cannot export more than ${limit} items at once. Please filter your selection.`,
    PREMIUM_REQUIRED: "Premium subscription required for bulk export",
    GENERIC_ERROR: "Failed to export items",
  },
} as const;

export type ExportType = "resumes" | "websites";

/**
 * Get the maximum export limit for a specific type
 */
export function getMaxExportLimit(type: ExportType): number {
  return type === "resumes" ? EXPORT_CONFIG.MAX_EXPORT_LIMIT : EXPORT_CONFIG.MAX_WEBSITE_EXPORT_LIMIT;
}

/**
 * Get the export endpoint for a specific type
 */
export function getExportEndpoint(type: ExportType): string {
  return type === "resumes" ? EXPORT_CONFIG.ENDPOINTS.RESUME_EXPORT_ALL : EXPORT_CONFIG.ENDPOINTS.WEBSITE_EXPORT_ALL;
}

/**
 * Generate filename for export
 */
export function generateExportFilename(type: ExportType): string {
  const prefix =
    type === "resumes" ? EXPORT_CONFIG.FILE_NAMING.RESUME_PREFIX : EXPORT_CONFIG.FILE_NAMING.WEBSITE_PREFIX;

  const date = new Date().toISOString().slice(0, 10);
  return `${prefix}_${date}.zip`;
}
