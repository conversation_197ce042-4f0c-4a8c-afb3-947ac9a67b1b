import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { capitalizeFirstLetter, formatHyphenatedString } from "@/lib/utils/string";

interface FilterIndicatorProps {
  currentFilter: string;
  currentSort: string;
  defaultFilter?: string;
  defaultSort?: string;
  filteredCount: number;
  totalCount: number;
  itemType: string;
  onClear: () => void;
}

export default function FilterIndicator({
  currentFilter,
  currentSort,
  defaultFilter = "all",
  defaultSort = "newest",
  filteredCount,
  totalCount,
  itemType,
  onClear,
}: FilterIndicatorProps) {
  // Don't show indicator if using default values
  if (currentFilter === defaultFilter && currentSort === defaultSort) {
    return null;
  }

  return (
    <div className="mb-4 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
      <Icon icon="tabler:filter" className="w-4 h-4" />
      <span>
        Showing {filteredCount} of {totalCount} {itemType}
        {currentFilter !== defaultFilter && ` • Filtered by: ${capitalizeFirstLetter(currentFilter)}`}
        {currentSort !== defaultSort && ` • Sorted by: ${formatHyphenatedString(currentSort)}`}
      </span>
      <Button
        size="sm"
        variant="flat"
        onPress={onClear}
        className="text-xs bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30"
        startContent={<Icon icon="tabler:x" className="w-3 h-3" />}
      >
        Clear
      </Button>
    </div>
  );
}
