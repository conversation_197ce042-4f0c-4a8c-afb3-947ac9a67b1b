import DOMPurify from "dompurify";

export const getInitials = (firstName?: string, lastName?: string) => {
  const first = firstName?.charAt(0) || "";
  const last = lastName?.charAt(0) || "";
  return (first + last).toUpperCase();
};

export const upperCaseFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const createMarkup = (htmlContent: string): { __html: string } => {
  return {
    __html: typeof DOMPurify.sanitize === "function" ? DOMPurify.sanitize(htmlContent) : htmlContent,
  };
};

export const isUrl = (string: string | null | undefined) => {
  if (!string) return false;
  const urlRegex = /https?:\/\/[^\n ]+/i;
  return urlRegex.test(string);
};

export const isEmptyString = (string: string) => {
  if (string === "<p></p>") return true;
  return string.trim().length === 0;
};

export const getFullName = (firstName: string, lastName: string, locale: string = "en-US"): string => {
  // In Arabic, sometimes family name comes first
  if (locale === "ar" && lastName && firstName) {
    return `${firstName} ${lastName}`.trim();
  }
  return `${firstName} ${lastName}`.trim();
};

export const formatLocation = (city: string, country: string): string => {
  const parts = [city, country].filter(Boolean);
  return parts.join(", ");
};
