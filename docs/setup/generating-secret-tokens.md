# Generating Secret Tokens

This guide provides various methods to generate secure random tokens for use in applications, API keys, secrets, and other cryptographic purposes.

## Table of Contents
- [OpenSSL Method](#openssl-method)
- [Using /dev/urandom](#using-devurandom)
- [Node.js Methods](#nodejs-methods)
- [Python Methods](#python-methods)
- [UUID Generation](#uuid-generation)
- [Best Practices](#best-practices)
- [Common Use Cases](#common-use-cases)

## OpenSSL Method

OpenSSL is widely available and provides cryptographically secure random generation.

### Generate Hex Tokens
```bash
# 32-byte (256-bit) token
openssl rand -hex 32

# 64-byte (512-bit) token
openssl rand -hex 64

# 16-byte (128-bit) token
openssl rand -hex 16
```

### Generate Base64 Tokens
```bash
# Base64 encoded (includes special characters)
openssl rand -base64 32

# URL-safe base64 (replace special chars)
openssl rand -base64 32 | tr '+/' '-_' | tr -d '='
```

## Using /dev/urandom

Direct access to the system's random number generator.

### Hex Format
```bash
# Generate 32 bytes in hex
head -c 32 /dev/urandom | xxd -p -c 32

# Alternative using od
head -c 32 /dev/urandom | od -An -tx1 | tr -d ' \n'
```

### Base64 Format
```bash
# Generate and encode to base64
head -c 32 /dev/urandom | base64

# Single line output
head -c 32 /dev/urandom | base64 | tr -d '\n'

# URL-safe base64
head -c 32 /dev/urandom | base64 | tr '+/' '-_' | tr -d '=\n'
```

## Node.js Methods

Using Node.js crypto module from the command line.

### Random Bytes
```bash
# Hex format (64 characters for 32 bytes)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Base64 format
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# URL-safe base64
node -e "console.log(require('crypto').randomBytes(32).toString('base64url'))"
```

### UUID Generation
```bash
# Generate UUID v4
node -e "console.log(require('crypto').randomUUID())"
```

### Custom Length Tokens
```bash
# Generate token of specific character length
node -e "console.log(require('crypto').randomBytes(Math.ceil(48 * 3/4)).toString('base64').slice(0, 48))"
```

## Python Methods

Python's secrets module is designed for cryptographic use.

### Using secrets Module
```bash
# Hex token (recommended for secrets)
python3 -c "import secrets; print(secrets.token_hex(32))"

# URL-safe token (good for URLs and file names)
python3 -c "import secrets; print(secrets.token_urlsafe(32))"

# Numeric token (for PINs or verification codes)
python3 -c "import secrets; print(secrets.randbelow(1000000))"
```

### UUID Generation
```bash
# Generate UUID4
python3 -c "import uuid; print(uuid.uuid4())"

# UUID without dashes
python3 -c "import uuid; print(str(uuid.uuid4()).replace('-', ''))"
```

## UUID Generation

UUIDs are standardized 128-bit identifiers.

### Using uuidgen
```bash
# Generate UUID (uppercase)
uuidgen

# Generate lowercase UUID
uuidgen | tr '[:upper:]' '[:lower:]'

# Remove dashes
uuidgen | tr -d '-'
```

### Using dbus-uuidgen
```bash
# Generate UUID using D-Bus
dbus-uuidgen
```

## Best Practices

### 1. Token Length
- **API Keys**: 32-64 bytes (256-512 bits)
- **Session Tokens**: 32 bytes (256 bits) minimum
- **CSRF Tokens**: 16-32 bytes (128-256 bits)
- **Password Reset**: 32 bytes (256 bits) minimum

### 2. Character Sets
- **Hex**: Uses 0-9, a-f (URL-safe, case-insensitive)
- **Base64**: Uses A-Z, a-z, 0-9, +, / (more compact)
- **Base64url**: Uses A-Z, a-z, 0-9, -, _ (URL-safe variant)

### 3. Security Considerations
- Always use cryptographically secure random generators
- Never use `Math.random()` or similar for security tokens
- Store tokens securely (environment variables, secret managers)
- Rotate tokens regularly
- Use sufficient entropy (minimum 128 bits)

## Common Use Cases

### Next.js Applications
```bash
# Generate NEXTAUTH_SECRET
openssl rand -base64 32

# Generate API secret key
openssl rand -hex 32
```

### Database Secrets
```bash
# PostgreSQL password
openssl rand -base64 24 | tr -d "=+/" | cut -c1-16

# MongoDB connection string secret
openssl rand -hex 32
```

### JWT Secrets
```bash
# Strong JWT secret (512 bits)
openssl rand -base64 64
```

### Webhook Signatures
```bash
# Webhook signing secret
openssl rand -hex 32
```

### Clerk Webhook Secrets
```bash
# Generate Clerk webhook signing secret
openssl rand -base64 32

# Add to .env.local
echo "CLERK_WEBHOOK_SECRET=$(openssl rand -base64 32)" >> .env.local
```

**Note**: Clerk uses base64 encoded secrets for webhook signature verification. The secret should be at least 32 bytes for security.

### PDF Generation/Protection Tokens
```bash
# Generate PDF secret token (hex format for compatibility)
openssl rand -hex 32

# Add to .env.local
echo "PDF_SECRET_TOKEN=$(openssl rand -hex 32)" >> .env.local
```

**Note**: Hex format is recommended for PDF operations as it avoids special characters that might cause issues in URLs or headers.

### Environment Variables
```bash
# Generate and save to .env file
echo "SECRET_KEY=$(openssl rand -hex 32)" >> .env.local
echo "API_TOKEN=$(openssl rand -base64 32)" >> .env.local
```

## Quick Reference

```bash
# Most common commands
openssl rand -hex 32          # Hex token
openssl rand -base64 32       # Base64 token
uuidgen                       # UUID
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
python3 -c "import secrets; print(secrets.token_urlsafe(32))"

# Specific use cases
openssl rand -base64 32       # Clerk webhook secret
openssl rand -hex 32          # PDF secret token
```

## Shell Functions

Add these to your `.bashrc` or `.zshrc` for quick access:

```bash
# Generate hex token
gentoken() {
    openssl rand -hex "${1:-32}"
}

# Generate base64 token
gentoken64() {
    openssl rand -base64 "${1:-32}"
}

# Generate UUID
genuuid() {
    uuidgen | tr '[:upper:]' '[:lower:]'
}

# Usage
gentoken      # 32-byte hex token
gentoken 64   # 64-byte hex token
gentoken64    # 32-byte base64 token
genuuid       # lowercase UUID
```