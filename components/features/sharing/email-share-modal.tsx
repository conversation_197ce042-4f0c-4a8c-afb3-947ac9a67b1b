"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Input,
  Modal,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  Select,
  SelectItem,
  Textarea,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { getFieldError, isFieldRequired, validateTemplateVariables } from "@/lib/email-template-validation";
import type { EmailTemplate } from "@/lib/email-templates";

interface EmailShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  resumeId: number;
}

export function EmailShareModal({ isOpen, onClose, resumeId }: EmailShareModalProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
  const [customVariables, setCustomVariables] = useState({
    recipientName: "",
    position: "",
    companyName: "",
    personalPitch: "",
    mutualConnection: "",
    industry: "",
    meetingContext: "",
    followUpNote: "",
    personalNote: "",
    yearsOfExperience: "",
  });
  const [generatedEmail, setGeneratedEmail] = useState<{
    subject: string;
    body: string;
    resumeUrl: string;
  } | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Fetch email templates
  const { data: templates, isLoading: templatesLoading } = trpc.sharing.getEmailTemplates.useQuery();

  // Generate email content mutation
  const generateEmailMutation = trpc.sharing.generateEmailContent.useMutation({
    onSuccess: (data) => {
      setGeneratedEmail(data);
    },
    onError: (error) => {
      toast.error("Failed to generate email. Please try again.");
      console.error("Email generation error:", error);
    },
  });

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplateId(templateId);
    setGeneratedEmail(null);
    setValidationErrors({});
  };

  const handleVariableChange = (key: string, value: string) => {
    setCustomVariables((prev) => ({ ...prev, [key]: value }));

    // Validate field on change
    if (selectedTemplateId) {
      const error = getFieldError(selectedTemplateId, key, value);
      setValidationErrors((prev) => ({
        ...prev,
        [key]: error || "",
      }));
    }
  };

  // Validate all required fields
  const validateAllFields = (): boolean => {
    if (!selectedTemplateId) return false;

    const validation = validateTemplateVariables(selectedTemplateId, customVariables);
    if (!validation.success) {
      const errors: Record<string, string> = {};
      validation.error.issues.forEach((issue) => {
        if (issue.path[0]) {
          errors[issue.path[0] as string] = issue.message;
        }
      });
      setValidationErrors(errors);
      return false;
    }

    setValidationErrors({});
    return true;
  };

  const handleGenerateEmail = () => {
    if (!selectedTemplateId) {
      toast.error("Please select an email template first.");
      return;
    }

    if (!validateAllFields()) {
      return;
    }

    generateEmailMutation.mutate({
      resumeId,
      templateId: selectedTemplateId,
      variables: customVariables,
    });
  };

  const handleSendEmail = () => {
    if (!generatedEmail) return;

    const mailtoLink = `mailto:?subject=${encodeURIComponent(generatedEmail.subject)}&body=${encodeURIComponent(generatedEmail.body)}`;

    // Try to detect if mailto will work
    const isEmailClientAvailable = /(iPad|iPhone|iPod|Mac|Windows|Linux)/i.test(navigator.userAgent);

    if (isEmailClientAvailable) {
      // Use location.href for better compatibility
      window.location.href = mailtoLink;
      toast.success("Opening your email client...");
    } else {
      // Fallback: copy the email and show instructions
      handleCopyEmail();
      toast.success("Email copied to clipboard!");
    }
  };

  const handleCopyEmail = async () => {
    if (!generatedEmail) return;

    const emailContent = `Subject: ${generatedEmail.subject}\n\n${generatedEmail.body}`;

    try {
      await navigator.clipboard.writeText(emailContent);
      toast.success("Email copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy email. Please try again.");
    }
  };

  const selectedTemplate = templates?.find((t) => t.id === selectedTemplateId);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="tabler:mail" className="w-6 h-6" />
            Share via Email
          </div>
          <p className="text-sm text-default-500 font-normal">
            Generate professional email templates to share your resume with employers.
          </p>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            {/* Template Selection */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Select Email Template</h3>
              <Select
                placeholder="Choose a template"
                selectedKeys={selectedTemplateId ? [selectedTemplateId] : []}
                onSelectionChange={(keys) => {
                  const selected = Array.from(keys)[0];
                  if (selected) {
                    handleTemplateChange(selected.toString());
                  }
                }}
                isLoading={templatesLoading}
                startContent={<Icon icon="tabler:template" className="w-4 h-4" />}
              >
                {templates?.map((template) => (
                  <SelectItem key={template.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{template.name}</span>
                      <span className="text-xs text-default-400 capitalize">{template.category}</span>
                    </div>
                  </SelectItem>
                )) || []}
              </Select>
            </div>

            {/* Template Variables */}
            {selectedTemplate && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Customize Email</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedTemplate.category === "professional" && (
                    <>
                      <Input
                        label={"Recipient Name"}
                        placeholder={"Enter recipient's name"}
                        value={customVariables.recipientName}
                        onValueChange={(value) => handleVariableChange("recipientName", value)}
                        startContent={<Icon icon="tabler:user" className="w-4 h-4" />}
                      />
                      {selectedTemplate.id === "job-application" && (
                        <>
                          <Input
                            label={"Position"}
                            placeholder={"Enter job position"}
                            value={customVariables.position}
                            onValueChange={(value) => handleVariableChange("position", value)}
                            startContent={<Icon icon="tabler:briefcase" className="w-4 h-4" />}
                          />
                          <Input
                            label={"Company Name"}
                            placeholder={"Enter company name"}
                            value={customVariables.companyName}
                            onValueChange={(value) => handleVariableChange("companyName", value)}
                            startContent={<Icon icon="tabler:building" className="w-4 h-4" />}
                            className="md:col-span-2"
                          />
                          <Textarea
                            label={"Personal Pitch"}
                            placeholder={"Brief description of your value proposition"}
                            value={customVariables.personalPitch}
                            onValueChange={(value) => handleVariableChange("personalPitch", value)}
                            minRows={3}
                            className="md:col-span-2"
                          />
                        </>
                      )}
                      {selectedTemplate.id === "follow-up" && (
                        <>
                          <Input
                            label={"Meeting Context"}
                            placeholder={"Where/how you met"}
                            value={customVariables.meetingContext}
                            onValueChange={(value) => handleVariableChange("meetingContext", value)}
                            startContent={<Icon icon="tabler:calendar" className="w-4 h-4" />}
                            className="md:col-span-2"
                          />
                          <Textarea
                            label={"Follow-up Note"}
                            placeholder={"Additional message or context"}
                            value={customVariables.followUpNote}
                            onValueChange={(value) => handleVariableChange("followUpNote", value)}
                            minRows={2}
                            className="md:col-span-2"
                          />
                        </>
                      )}
                      <Input
                        label={"Years of Experience"}
                        placeholder={"e.g., 5 years"}
                        value={customVariables.yearsOfExperience}
                        onValueChange={(value) => handleVariableChange("yearsOfExperience", value)}
                        startContent={<Icon icon="tabler:clock" className="w-4 h-4" />}
                      />
                    </>
                  )}

                  {selectedTemplate.category === "networking" && (
                    <>
                      <Input
                        label={"Recipient Name"}
                        placeholder={"Enter recipient's name"}
                        value={customVariables.recipientName}
                        onValueChange={(value) => handleVariableChange("recipientName", value)}
                        startContent={<Icon icon="tabler:user" className="w-4 h-4" />}
                      />
                      <Input
                        label={"Mutual Connection"}
                        placeholder={"Person who connected you"}
                        value={customVariables.mutualConnection}
                        onValueChange={(value) => handleVariableChange("mutualConnection", value)}
                        startContent={<Icon icon="tabler:users" className="w-4 h-4" />}
                      />
                      <Input
                        label={"Industry"}
                        placeholder={"e.g., Technology, Finance"}
                        value={customVariables.industry}
                        onValueChange={(value) => handleVariableChange("industry", value)}
                        startContent={<Icon icon="tabler:building-store" className="w-4 h-4" />}
                        className="md:col-span-2"
                      />
                    </>
                  )}

                  {selectedTemplate.category === "casual" && (
                    <>
                      <Input
                        label={"Recipient Name"}
                        placeholder={"Enter recipient's name"}
                        value={customVariables.recipientName}
                        onValueChange={(value) => handleVariableChange("recipientName", value)}
                        startContent={<Icon icon="tabler:user" className="w-4 h-4" />}
                      />
                      <Textarea
                        label={"Personal Note"}
                        placeholder={"Add a personal message"}
                        value={customVariables.personalNote}
                        onValueChange={(value) => handleVariableChange("personalNote", value)}
                        minRows={2}
                        className="md:col-span-2"
                      />
                    </>
                  )}
                </div>

                <Button
                  onPress={handleGenerateEmail}
                  isLoading={generateEmailMutation.isPending}
                  color="primary"
                  startContent={<Icon icon="tabler:wand" className="w-4 h-4" />}
                  className="w-full"
                >
                  Generate Email
                </Button>
              </div>
            )}

            {/* Generated Email Preview */}
            {generatedEmail && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Email Preview</h3>
                <Card>
                  <CardBody className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-default-600 mb-1">Subject:</p>
                      <p className="text-sm bg-default-100 p-2 rounded">{generatedEmail.subject}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-default-600 mb-1">Body:</p>
                      <div className="text-sm bg-default-100 p-4 rounded whitespace-pre-wrap max-h-60 overflow-y-auto">
                        {generatedEmail.body}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-default-600 mb-1">Resume Link:</p>
                      <p className="text-xs text-primary break-all">{generatedEmail.resumeUrl}</p>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button variant="light" onPress={onClose}>
            Cancel
          </Button>
          {generatedEmail && (
            <>
              <Button
                variant="flat"
                onPress={handleCopyEmail}
                startContent={<Icon icon="tabler:copy" className="w-4 h-4" />}
              >
                Copy Email
              </Button>
              <Button
                color="primary"
                onPress={handleSendEmail}
                startContent={<Icon icon="tabler:mail" className="w-4 h-4" />}
              >
                Send Email
              </Button>
            </>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
