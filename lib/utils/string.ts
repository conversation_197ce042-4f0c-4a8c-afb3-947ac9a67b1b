/**
 * Capitalizes the first letter of a string
 * @param str - The string to capitalize
 * @returns The string with the first letter capitalized
 */
export function capitalizeFirstLetter(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Formats a hyphenated string by replacing hyphens with spaces and capitalizing each word
 * @param str - The string to format (e.g., "name-asc")
 * @returns The formatted string (e.g., "Name Asc")
 */
export function formatHyphenatedString(str: string): string {
  if (!str) return str;
  return str.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase());
}
