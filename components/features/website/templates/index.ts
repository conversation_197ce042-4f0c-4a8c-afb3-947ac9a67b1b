// Website Template Components - Website template implementations
export { AcademicTemplate } from "./academic-template";
// Base components and types (exported from organized modules)
export * from "./content";
export { CreativeTemplate } from "./creative-template";
export * from "./data";
export { ElegantTemplate } from "./elegant-template";
export { ExecutiveTemplate } from "./executive-template";
export * from "./layout";
export { MinimalTemplate } from "./minimal-template";
export * from "./primitives";
export { ProfessionalTemplate } from "./professional-template";
export * from "./shared/utils";
export { TechTemplate } from "./tech-template";

// Template registry and utilities
export {
  getAvailableWebsiteTemplates,
  getWebsiteTemplate,
  WebsiteTemplateSelector,
  type WebsiteTemplateSelectorProps,
  websiteTemplateRegistry,
} from "./template-registry";
