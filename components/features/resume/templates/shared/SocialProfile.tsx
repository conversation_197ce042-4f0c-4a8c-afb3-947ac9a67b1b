import React from "react";
import { Profile } from "@/db/schema";

export interface SocialProfileProps {
  profiles: Profile[];
  layout?: "grid" | "horizontal" | "vertical";
  showNetworkLabel?: boolean;
  className?: string;
}

export const SocialProfile: React.FC<SocialProfileProps> = ({
  profiles,
  layout = "vertical",
  showNetworkLabel = true,
  className = "",
}) => {
  if (!profiles || profiles.length === 0) return null;

  const getProfileIcon = (network: string) => {
    const icons: Record<string, string> = {
      linkedin: "💼",
      github: "🐙",
      stackoverflow: "🔧",
      twitter: "🐦",
      behance: "🎨",
      dribbble: "🏀",
      instagram: "📷",
      facebook: "📘",
      youtube: "📺",
      tiktok: "🎵",
      default: "🔗",
    };
    return icons[network?.toLowerCase()] || icons.default;
  };

  const containerClass =
    layout === "horizontal" ? "flex flex-wrap gap-3" : layout === "grid" ? "grid grid-cols-2 gap-2" : "space-y-2";

  return (
    <div className={`profiles-grid ${containerClass} ${className}`}>
      {profiles.map((profile) => (
        <div key={profile.id} className="flex items-center text-sm">
          <span className="me-2">{getProfileIcon(profile.network)}</span>
          <div>
            <span className="font-medium text-gray-800">{profile.username}</span>
            {showNetworkLabel && <span className="ms-1 text-gray-600">{profile.network ?? ""}</span>}
          </div>
        </div>
      ))}
    </div>
  );
};
