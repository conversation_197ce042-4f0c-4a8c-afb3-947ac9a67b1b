"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody, Link } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import ResumeTemplateRenderer from "@/components/features/resume/templates/template-registry";
import { type FullResume } from "@/db/schema";
import { SocialMediaSharing } from "@/lib/social-sharing";

interface PublicResumeViewProps {
  resume: FullResume;
  token: string;
}

export function PublicResumeView({ resume, token }: PublicResumeViewProps) {
  const [isSharing, setIsSharing] = useState(false);

  // Use the ResumeTemplateRenderer component directly

  // Generate current share URL
  const shareUrl = `${window.location.origin}/share/${token}`;
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();

  const handleSocialShare = async (platform: string) => {
    setIsSharing(true);

    try {
      const shareData = {
        fullName,
        jobTitle: resume.jobTitle || "Professional",
        resumeUrl: shareUrl,
      };

      let platformShareUrl: string;

      switch (platform) {
        case "linkedin":
          platformShareUrl = SocialMediaSharing.generateLinkedInShareUrl(shareData);
          break;
        case "twitter":
          platformShareUrl = SocialMediaSharing.generateTwitterShareUrl(shareData);
          break;
        case "facebook":
          platformShareUrl = SocialMediaSharing.generateFacebookShareUrl(shareData);
          break;
        case "whatsapp":
          platformShareUrl = SocialMediaSharing.generateWhatsAppShareUrl(shareData);
          break;
        default:
          throw new Error("Unsupported platform");
      }

      window.open(platformShareUrl, "_blank", "width=600,height=400");
      toast.success(`Shared successfully on ${platform}`);
    } catch (error) {
      console.error("Share error:", error);
      toast.error("Failed to share. Please try again.");
    } finally {
      setIsSharing(false);
    }
  };

  const socialPlatforms = [
    {
      name: "LinkedIn",
      icon: "tabler:brand-linkedin",
      color: "hover:bg-[#0077B5]",
      key: "linkedin",
    },
    {
      name: "Twitter",
      icon: "tabler:brand-twitter",
      color: "hover:bg-[#1DA1F2]",
      key: "twitter",
    },
    {
      name: "Facebook",
      icon: "tabler:brand-facebook",
      color: "hover:bg-[#1877F2]",
      key: "facebook",
    },
    {
      name: "WhatsApp",
      icon: "tabler:brand-whatsapp",
      color: "hover:bg-[#25D366]",
      key: "whatsapp",
    },
  ];

  // No need for template check - ResumeTemplateRenderer handles it internally

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* QuickCV Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Icon icon="tabler:file-cv" className="w-8 h-8 text-primary" />
                <span className="text-xl font-bold text-gray-900">QuickCV</span>
              </Link>
            </div>

            {/* CTA Button */}
            <Button
              as={Link}
              href="/"
              color="primary"
              size="sm"
              startContent={<Icon icon="tabler:rocket" className="w-4 h-4" />}
            >
              Try QuickCV
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Resume Content */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
          <div className="p-8" id="resume-content">
            <ResumeTemplateRenderer resume={resume} />
          </div>
        </div>

        {/* Action Bar */}
        <Card className="mb-8">
          <CardBody>
            <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
              {/* Social Sharing */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 hidden sm:block">Share on:</span>
                <div className="flex gap-2">
                  {socialPlatforms.map((platform) => (
                    <Button
                      key={platform.key}
                      isIconOnly
                      variant="ghost"
                      size="sm"
                      className={`text-gray-600 ${platform.color} hover:text-white transition-colors`}
                      onPress={() => handleSocialShare(platform.key)}
                      isLoading={isSharing}
                    >
                      <Icon icon={platform.icon} className="w-5 h-5" />
                    </Button>
                  ))}
                </div>
              </div>

              {/* Try QuickCV CTA */}
              <Button
                as={Link}
                href="/"
                color="primary"
                variant="solid"
                startContent={<Icon icon="tabler:rocket" className="w-4 h-4" />}
              >
                Try QuickCV
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Made with QuickCV Badge */}
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm text-gray-500 hover:text-primary transition-colors"
          >
            <Icon icon="tabler:heart" className="w-4 h-4" />
            Made with QuickCV
          </Link>
        </div>
      </main>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #resume-content,
          #resume-content * {
            visibility: visible;
          }
          #resume-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          @page {
            margin: 0.5in;
            size: A4;
          }
        }
      `}</style>
    </div>
  );
}
