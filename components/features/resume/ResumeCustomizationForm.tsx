"use client";

import React from "react";
import { TemplateSelector } from "@/components/shared";
import ColorSchemeSelector from "@/components/shared/color-scheme-selector";
import { ColorSchemeId } from "@/config/color-schemes";
import { Template } from "@/db/schema";
import { useFormStore } from "@/lib/form-store";

interface ResumeCustomizationFormProps {
  templates: Template[];
}

export const ResumeCustomizationForm: React.FC<ResumeCustomizationFormProps> = ({ templates }) => {
  const { formData, updateField } = useFormStore();

  // React Compiler will optimize these handlers automatically
  const handleTemplateChange = (templateId: number) => {
    updateField("templateId", templateId);
  };

  const handleColorSchemeChange = (colorScheme: ColorSchemeId) => {
    updateField("colorScheme", colorScheme);
  };

  return (
    <div className="space-y-6 py-4">
      {/* Colors Section */}
      <ColorSchemeSelector
        selectedColorScheme={formData?.colorScheme as ColorSchemeId}
        onColorSchemeChange={handleColorSchemeChange}
      />
      {/* Templates Section */}
      <TemplateSelector
        selectedTemplateId={formData?.templateId}
        showCategory={false}
        templates={templates}
        onTemplateSelect={handleTemplateChange}
      />
    </div>
  );
};
