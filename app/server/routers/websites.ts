import { TRPCError } from "@trpc/server";
import { and, desc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { type FullWebsite, resumes, websites, websiteTemplates } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";

export const websitesRouter = router({
  // Get all user websites
  getUserWebsites: protectedProcedure.query(async ({ ctx }) => {
    const userWebsites = await db
      .select()
      .from(websites)
      .leftJoin(resumes, eq(websites.resumeId, resumes.id))
      .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
      .where(eq(websites.userId, ctx.user.clerkId))
      .orderBy(desc(websites.updatedAt));

    // Transform to FullWebsite structure
    const transformedWebsites: FullWebsite[] = userWebsites.map((row) => ({
      ...row.websites,
      resume: row.resumes,
      websiteTemplate: row.website_templates,
    }));

    return transformedWebsites;
  }),

  // Create a new website
  createWebsite: protectedProcedure
    .input(
      z.object({
        resumeId: z.number(),
        websiteTemplateId: z.number(),
        slug: z.string().min(1, "Slug is required"),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Verify the resume belongs to the user
      const [resume] = await db
        .select()
        .from(resumes)
        .where(and(eq(resumes.id, input.resumeId), eq(resumes.userId, ctx.user.clerkId)))
        .limit(1);

      if (!resume) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Resume not found or unauthorized",
        });
      }

      // Check if slug is already taken
      const [existingWebsite] = await db.select().from(websites).where(eq(websites.slug, input.slug)).limit(1);

      if (existingWebsite) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "This URL slug is already taken. Please choose a different one.",
        });
      }

      // Verify website template exists
      const [websiteTemplate] = await db
        .select()
        .from(websiteTemplates)
        .where(eq(websiteTemplates.id, input.websiteTemplateId))
        .limit(1);

      if (!websiteTemplate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Website template not found",
        });
      }

      // Create the website
      const [newWebsite] = await db
        .insert(websites)
        .values({
          slug: input.slug,
          userId: ctx.user.clerkId,
          resumeId: input.resumeId,
          websiteTemplateId: input.websiteTemplateId,
        })
        .returning();

      // Return the created website with relations
      const [websiteWithRelations] = await db
        .select()
        .from(websites)
        .leftJoin(resumes, eq(websites.resumeId, resumes.id))
        .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
        .where(eq(websites.id, newWebsite.id))
        .limit(1);

      const fullWebsite: FullWebsite = {
        ...websiteWithRelations.websites,
        resume: websiteWithRelations.resumes,
        websiteTemplate: websiteWithRelations.website_templates,
      };

      return {
        success: true,
        data: fullWebsite,
      };
    }),

  // Update website settings
  updateWebsite: protectedProcedure
    .input(
      z.object({
        websiteId: z.number(),
        slug: z.string().optional(),
        websiteTemplateId: z.number().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Verify the website belongs to the user
      const [existingWebsite] = await db
        .select()
        .from(websites)
        .where(and(eq(websites.id, input.websiteId), eq(websites.userId, ctx.user.clerkId)))
        .limit(1);

      if (!existingWebsite) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Website not found or unauthorized",
        });
      }

      // If slug is being updated, check if it's available
      if (input.slug && input.slug !== existingWebsite.slug) {
        const [slugExists] = await db.select().from(websites).where(eq(websites.slug, input.slug)).limit(1);

        if (slugExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "This URL slug is already taken. Please choose a different one.",
          });
        }
      }

      // If website template is being updated, verify it exists
      if (input.websiteTemplateId) {
        const [websiteTemplate] = await db
          .select()
          .from(websiteTemplates)
          .where(eq(websiteTemplates.id, input.websiteTemplateId))
          .limit(1);

        if (!websiteTemplate) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Website template not found",
          });
        }
      }

      // Prepare update data
      const updateData: any = {};
      if (input.slug) updateData.slug = input.slug;
      if (input.websiteTemplateId) updateData.websiteTemplateId = input.websiteTemplateId;

      // Update the website
      await db.update(websites).set(updateData).where(eq(websites.id, input.websiteId));

      // Get the updated website with relations
      const [websiteWithRelations] = await db
        .select()
        .from(websites)
        .leftJoin(resumes, eq(websites.resumeId, resumes.id))
        .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
        .where(eq(websites.id, input.websiteId))
        .limit(1);

      const fullWebsite: FullWebsite = {
        ...websiteWithRelations.websites,
        resume: websiteWithRelations.resumes,
        websiteTemplate: websiteWithRelations.website_templates,
      };

      return {
        success: true,
        data: fullWebsite,
      };
    }),

  // Toggle website public status
  toggleWebsitePublic: protectedProcedure
    .input(z.object({ websiteId: z.number() }))
    .mutation(async ({ input, ctx }) => {
      // Verify the website belongs to the user
      const [existingWebsite] = await db
        .select()
        .from(websites)
        .where(and(eq(websites.id, input.websiteId), eq(websites.userId, ctx.user.clerkId)))
        .limit(1);

      if (!existingWebsite) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Website not found or unauthorized",
        });
      }

      // Toggle the public status
      const newPublicStatus = existingWebsite.isPublic === 1 ? 0 : 1;

      // Check if user is trying to publish (make public) and doesn't have premium
      if (newPublicStatus === 1 && !ctx.user.isPremium) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Premium subscription required to publish websites",
        });
      }

      await db
        .update(websites)
        .set({
          isPublic: newPublicStatus,
        })
        .where(eq(websites.id, input.websiteId));

      // Get the updated website with relations
      const [websiteWithRelations] = await db
        .select()
        .from(websites)
        .leftJoin(resumes, eq(websites.resumeId, resumes.id))
        .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
        .where(eq(websites.id, input.websiteId))
        .limit(1);

      const fullWebsite: FullWebsite = {
        ...websiteWithRelations.websites,
        resume: websiteWithRelations.resumes,
        websiteTemplate: websiteWithRelations.website_templates,
      };

      return {
        success: true,
        data: fullWebsite,
      };
    }),

  // Delete a website
  deleteWebsite: protectedProcedure.input(z.object({ websiteId: z.number() })).mutation(async ({ input, ctx }) => {
    // Verify the website belongs to the user
    const [existingWebsite] = await db
      .select()
      .from(websites)
      .where(and(eq(websites.id, input.websiteId), eq(websites.userId, ctx.user.clerkId)))
      .limit(1);

    if (!existingWebsite) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Website not found or unauthorized",
      });
    }

    await db.delete(websites).where(eq(websites.id, input.websiteId));

    return {
      success: true,
    };
  }),

  // Get available website templates
  getWebsiteTemplates: protectedProcedure.query(async () => {
    const templates = await db.select().from(websiteTemplates).orderBy(websiteTemplates.name);

    return templates;
  }),

  // Check if slug is available
  checkSlugAvailability: protectedProcedure
    .input(
      z.object({
        slug: z.string(),
        excludeWebsiteId: z.number().optional(),
      }),
    )
    .query(async ({ input }) => {
      const [existingWebsite] = await db.select().from(websites).where(eq(websites.slug, input.slug)).limit(1);

      // If excluding a specific website (for updates), check if it's a different website
      const isAvailable = !existingWebsite || (input.excludeWebsiteId && existingWebsite.id === input.excludeWebsiteId);

      return {
        available: isAvailable,
      };
    }),

  // Generate a suggested slug from name
  generateSlug: protectedProcedure
    .input(
      z.object({
        firstName: z.string(),
        lastName: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const fullName = `${input.firstName} ${input.lastName}`.trim();
      const baseSlug = fullName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-") // Replace multiple hyphens with single
        .trim();

      return {
        slug: baseSlug,
      };
    }),

  // Get website for a specific resume
  getResumeWebsite: protectedProcedure.input(z.object({ resumeId: z.number() })).query(async ({ input, ctx }) => {
    // Verify the resume belongs to the user
    const [resume] = await db
      .select()
      .from(resumes)
      .where(and(eq(resumes.id, input.resumeId), eq(resumes.userId, ctx.user.clerkId)))
      .limit(1);

    if (!resume) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }

    // Get the website for this resume
    const [websiteData] = await db
      .select()
      .from(websites)
      .leftJoin(resumes, eq(websites.resumeId, resumes.id))
      .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
      .where(and(eq(websites.resumeId, input.resumeId), eq(websites.userId, ctx.user.clerkId)))
      .limit(1);

    if (!websiteData?.websites) {
      return null; // No website exists for this resume
    }

    const fullWebsite: FullWebsite = {
      ...websiteData.websites,
      resume: websiteData.resumes,
      websiteTemplate: websiteData.website_templates,
    };

    return fullWebsite;
  }),
});

export type WebsitesRouter = typeof websitesRouter;
