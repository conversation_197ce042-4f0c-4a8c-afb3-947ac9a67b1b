export interface SiteConfigType {
  name: string;
  description: string;
  links: {
    github: string;
    twitter: string;
    docs: string;
    discord: string;
    sponsor: string;
  };
}

export const siteConfig: SiteConfigType = {
  name: "QuickCV",
  description: "Create professional resumes and websites with ease. ATS-optimized templates and modern design tools.",
  links: {
    github: "https://github.com/heroui-inc/heroui",
    twitter: "https://twitter.com/hero_ui",
    docs: "https://heroui.com",
    discord: "https://discord.gg/9b6yyZKmH4",
    sponsor: "https://patreon.com/jrgarciadev",
  },
};

// This makes `SiteConfig` refer to the type of the `siteConfig` object
export type SiteConfig = typeof siteConfig;
