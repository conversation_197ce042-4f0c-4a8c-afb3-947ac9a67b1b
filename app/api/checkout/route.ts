import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/auth-clerk";
import { PADDLE_PRODUCTS } from "@/lib/paddle";

export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const user = await requireAuth();

    // Check if user is already premium
    if (user.planId !== "free") {
      return NextResponse.json({ error: "User already has premium access" }, { status: 400 });
    }

    // Get the plan from request body
    const { planId } = await request.json();

    // Validate plan
    if (!planId || !["pro_monthly", "pro_yearly"].includes(planId)) {
      return NextResponse.json({ error: "Invalid plan selected" }, { status: 400 });
    }

    // Get the appropriate price ID
    const priceId = PADDLE_PRODUCTS[planId as keyof typeof PADDLE_PRODUCTS]?.priceId;

    if (!priceId) {
      return NextResponse.json({ error: "Price ID not configured for selected plan" }, { status: 500 });
    }

    // Return the checkout data for the client
    return NextResponse.json({
      success: true,
      data: {
        priceId,
        planId,
        customData: {
          userId: user.clerkId,
          planId,
        },
        customer: {
          email: user.emailAddress,
        },
      },
    });
  } catch (error) {
    console.error("Checkout initialization error:", error);
    return NextResponse.json({ error: "Failed to initialize checkout" }, { status: 500 });
  }
}
