import type { NavbarMenuItem } from "@/config/navbar-menu";

/**
 * Color variant type for menu items
 */
export type ColorVariant = "primary" | "secondary" | "success" | "warning" | "danger";

/**
 * Color mappings for different variants
 */
const COLOR_MAPPINGS = {
  primary: {
    background: "bg-blue-100 dark:bg-blue-900/30",
    text: "text-blue-600 dark:text-blue-400",
    border: "border-blue-200 dark:border-blue-800",
  },
  secondary: {
    background: "bg-purple-100 dark:bg-purple-900/30",
    text: "text-purple-600 dark:text-purple-400",
    border: "border-purple-200 dark:border-purple-800",
  },
  success: {
    background: "bg-green-100 dark:bg-green-900/30",
    text: "text-green-600 dark:text-green-400",
    border: "border-green-200 dark:border-green-800",
  },
  warning: {
    background: "bg-yellow-100 dark:bg-yellow-900/30",
    text: "text-yellow-600 dark:text-yellow-400",
    border: "border-yellow-200 dark:border-yellow-800",
  },
  danger: {
    background: "bg-red-100 dark:bg-red-900/30",
    text: "text-red-600 dark:text-red-400",
    border: "border-red-200 dark:border-red-800",
  },
} as const;

/**
 * Default color mapping for fallback
 */
const DEFAULT_COLOR = {
  background: "bg-gray-100 dark:bg-gray-900/30",
  text: "text-gray-600 dark:text-gray-400",
  border: "border-gray-200 dark:border-gray-800",
} as const;

/**
 * Get background color classes for a menu item
 */
export function getMenuItemBackgroundColor(color?: ColorVariant): string {
  if (!color || !COLOR_MAPPINGS[color]) {
    return DEFAULT_COLOR.background;
  }
  return COLOR_MAPPINGS[color].background;
}

/**
 * Get text color classes for a menu item
 */
export function getMenuItemTextColor(color?: ColorVariant): string {
  if (!color || !COLOR_MAPPINGS[color]) {
    return DEFAULT_COLOR.text;
  }
  return COLOR_MAPPINGS[color].text;
}

/**
 * Get border color classes for a menu item
 */
export function getMenuItemBorderColor(color?: ColorVariant): string {
  if (!color || !COLOR_MAPPINGS[color]) {
    return DEFAULT_COLOR.border;
  }
  return COLOR_MAPPINGS[color].border;
}

/**
 * Get all color classes for a menu item
 */
export function getMenuItemColors(color?: ColorVariant) {
  const mapping = color && COLOR_MAPPINGS[color] ? COLOR_MAPPINGS[color] : DEFAULT_COLOR;

  return {
    background: mapping.background,
    text: mapping.text,
    border: mapping.border,
  };
}

/**
 * Convenience function to get colors from a NavbarMenuItem
 */
export function getMenuItemColorsFromItem(item: Pick<NavbarMenuItem, "color">) {
  return getMenuItemColors(item.color);
}
