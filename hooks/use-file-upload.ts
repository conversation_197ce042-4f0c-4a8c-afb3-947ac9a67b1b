"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";
import { useUploadThing } from "@/lib/uploadthing";

interface UseFileUploadProps {
  endpoint: "imageUploader" | "resumePhotoUploader" | "contactAttachmentsUploader";
  onSuccess?: (res: { url: string }[]) => void;
  onError?: (error: Error) => void;
}

export function useFileUpload({ endpoint, onSuccess, onError }: UseFileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileProgress, setFileProgress] = useState<Record<string, number>>({});

  const { startUpload } = useUploadThing(endpoint, {
    onClientUploadComplete: (res) => {
      setIsUploading(false);
      setUploadProgress(0);
      toast.success("Upload successful!");
      onSuccess?.(res);
    },
    onUploadError: (error: Error) => {
      setIsUploading(false);
      setUploadProgress(0);
      toast.error(error.message);
      onError?.(error);
    },
    onUploadBegin: () => {
      setIsUploading(true);
      setUploadProgress(0);
    },
    onUploadProgress: (progress) => {
      setUploadProgress(progress);
    },
  });

  const uploadFiles = async (files: File[]) => {
    if (files.length === 0) return;

    try {
      await startUpload(files);
    } catch (error) {
      console.error("Upload error:", error);
      setIsUploading(false);
      setUploadProgress(0);
      toast.error("Upload failed. Please try again.");
      onError?.(error as Error);
    }
  };

  const uploadSingleFile = async (file: File) => {
    await uploadFiles([file]);
  };

  const uploadFilesWithProgress = async (files: File[]) => {
    if (files.length === 0) return;

    // Initialize progress for each file
    const initialProgress: Record<string, number> = {};
    files.forEach((file) => {
      initialProgress[file.name] = 0;
    });
    setFileProgress(initialProgress);

    try {
      await startUpload(files);
    } catch (error) {
      console.error("Upload error:", error);
      setIsUploading(false);
      setUploadProgress(0);
      setFileProgress({});
      toast.error("Upload failed. Please try again.");
      onError?.(error as Error);
    }
  };

  return {
    isUploading,
    uploadProgress,
    fileProgress,
    uploadFiles,
    uploadSingleFile,
    uploadFilesWithProgress,
  };
}
