"use client";

import { useRouter } from "@bprogress/next/app";
import { <PERSON><PERSON>, Card, CardBody, Chip, Progress } from "@heroui/react";
import { Icon } from "@iconify/react";
import { AnimatePresence, motion, useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { UpgradeModal } from "@/components/payment";
// Modern PostHog feature flag integration
import { type FeatureFlagKey, useFeatureFlagEnabled } from "@/hooks/use-feature-flags";

// Simple feature definitions
const FEATURES = {
  AI_GENERATION: { id: "ai_generation", name: "AI Content Generation" },
  PDF_EXPORT: { id: "pdf_export", name: "Export to PDF" },
  WEBSITE_PUBLISH: { id: "website_publish", name: "Publish Website" },
  UNLIMITED_AI: { id: "unlimited_ai", name: "Unlimited AI Generation" },
  RESUME_SHARE: { id: "resume_share", name: "Share Resume" },
} as const;

function getPremiumFeatures() {
  return Object.values(FEATURES).filter((feature) => feature.id !== "ai_generation");
}

// Simplified analytics functions for showcase
function trackFeatureEvent(...args: any[]) {
  console.log("Feature event:", ...args);
}
function trackFeatureUsage(...args: any[]) {
  console.log("Feature usage:", ...args);
}
function trackUpgradePrompt(...args: any[]) {
  console.log("Upgrade prompt:", ...args);
}

interface FeatureShowcaseProps {
  autoPlay?: boolean;
  showProgress?: boolean;
  className?: string;
}

interface FeatureShowcase {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  benefits: string[];
  icon: string;
  gradient: string;
  demoAnimation: string;
  isPremium: boolean;
  badge?: string;
  stats?: {
    label: string;
    value: string;
  }[];
}

const getFeatureShowcases = (): FeatureShowcase[] => [
  {
    id: FEATURES.AI_GENERATION.id,
    title: "AI-Powered Resume Generation",
    subtitle: "Create professional resumes instantly",
    description:
      "Leverage advanced AI to generate compelling resume content tailored to your experience and target roles.",
    benefits: [
      "Instant content generation",
      "Industry-specific optimization",
      "ATS-friendly formatting",
      "Professional language enhancement",
    ],
    icon: "heroicons:sparkles-20-solid",
    gradient: "from-purple-500 to-pink-500",
    demoAnimation: "sparkle",
    isPremium: false,
    badge: "Limited Free",
    stats: [
      { label: "Success Rate", value: "95%" },
      { label: "Time Saved", value: "2hrs" },
    ],
  },
  {
    id: FEATURES.PDF_EXPORT.id,
    title: "Professional PDF Export",
    subtitle: "High-quality PDF downloads",
    description: "Export your resume as a professional PDF with perfect formatting and print-ready quality.",
    benefits: ["High-resolution output", "Perfect formatting", "Multiple size options", "Print-ready quality"],
    icon: "heroicons:document-arrow-down-20-solid",
    gradient: "from-blue-500 to-cyan-500",
    demoAnimation: "download",
    isPremium: true,
    stats: [
      { label: "Export Quality", value: "HD" },
      { label: "Download Speed", value: "<3s" },
    ],
  },
  {
    id: FEATURES.WEBSITE_PUBLISH.id,
    title: "Personal Website Publishing",
    subtitle: "Your resume as a beautiful website",
    description: "Transform your resume into a stunning personal website with custom domain and professional design.",
    benefits: ["Custom domain support", "Mobile-responsive design", "SEO optimization", "Social media integration"],
    icon: "heroicons:globe-alt-20-solid",
    gradient: "from-green-500 to-emerald-500",
    demoAnimation: "globe",
    isPremium: true,
    stats: [
      { label: "Load Time", value: "<1s" },
      { label: "Mobile Score", value: "100%" },
    ],
  },
  {
    id: FEATURES.RESUME_SHARE.id,
    title: "Smart Resume Sharing",
    subtitle: "Share with a simple link",
    description: "Share your resume instantly with employers using secure, trackable links with view analytics.",
    benefits: ["Secure sharing links", "View tracking", "Password protection", "Expiration controls"],
    icon: "heroicons:share-20-solid",
    gradient: "from-orange-500 to-red-500",
    demoAnimation: "share",
    isPremium: true,
    stats: [
      { label: "Click Rate", value: "89%" },
      { label: "Response Time", value: "2x" },
    ],
  },
];

export function FeatureShowcaseCarousel({
  autoPlay = true,
  showProgress = true,
  className = "",
}: FeatureShowcaseProps) {
  const router = useRouter();
  const FEATURE_SHOWCASES = getFeatureShowcases();

  // Simplified showcase analytics
  const trackShowcaseViewed = (...args: any[]) => console.log("Showcase viewed:", ...args);
  const trackShowcaseNavigation = (...args: any[]) => console.log("Showcase navigation:", ...args);
  const trackFeatureClick = (...args: any[]) => console.log("Feature click:", ...args);
  const trackCTAClick = (...args: any[]) => console.log("CTA click:", ...args);
  const [currentFeature, setCurrentFeature] = useState(0);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const [progress, setProgress] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [celebrateFeature, setCelebrateFeature] = useState("");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const currentShowcase = FEATURE_SHOWCASES[currentFeature];

  // Track initial showcase view when component mounts and comes into view
  useEffect(() => {
    if (isInView && currentShowcase) {
      trackShowcaseViewed(currentShowcase.id, {
        showcasePosition: currentFeature,
        autoAdvanced: false,
        category: "feature_showcase",
        initialView: true,
        viewSource: "component_mount",
      });

      trackFeatureEvent("showcase_initial_view", {
        featureId: currentShowcase.id,
        action: "accessed",
        showcasePosition: currentFeature,
        category: "feature_showcase",
      });
    }
  }, [isInView, currentShowcase?.id, trackShowcaseViewed]);

  // Auto-advance carousel and track showcase views
  useEffect(() => {
    if (!autoPlay || isHovered) return;

    const timer = setInterval(() => {
      const nextIndex = (currentFeature + 1) % FEATURE_SHOWCASES.length;
      const nextShowcase = FEATURE_SHOWCASES[nextIndex];

      trackShowcaseViewed(nextShowcase.id, {
        showcasePosition: nextIndex,
        autoAdvanced: true,
        category: "feature_showcase",
        previousShowcase: FEATURE_SHOWCASES[currentFeature]?.id,
      });

      trackFeatureEvent("showcase_auto_advanced", {
        featureId: nextShowcase.id,
        action: "accessed",
        previousFeature: FEATURE_SHOWCASES[currentFeature]?.id,
        category: "showcase_navigation",
      });

      setCurrentFeature(nextIndex);
      setProgress(0);
    }, 5000);

    return () => clearInterval(timer);
  }, [autoPlay, isHovered, currentFeature, trackShowcaseViewed]);

  // Progress animation
  useEffect(() => {
    if (!showProgress || isHovered) return;

    const progressTimer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          return 0;
        }
        return prev + 2;
      });
    }, 100);

    return () => clearInterval(progressTimer);
  }, [currentFeature, showProgress, isHovered]);

  const navigateToFeature = (featureId: string) => {
    switch (featureId) {
      case FEATURES.AI_GENERATION.id:
        // Navigate to resumes page where users can create/edit resumes and use AI
        router.push("/resumes");
        break;
      case FEATURES.PDF_EXPORT.id:
        // Navigate to resumes page where users can export PDFs
        router.push("/resumes");
        break;
      case FEATURES.WEBSITE_PUBLISH.id:
        // Navigate to websites page
        router.push("/websites");
        break;
      case FEATURES.RESUME_SHARE.id:
        // Navigate to resumes page where sharing is available
        router.push("/resumes");
        break;
      default:
        // Fallback to resumes page for any other features
        router.push("/resumes");
        break;
    }
  };

  const handleDirectUpgrade = () => {
    // Track upgrade interaction
    trackCTAClick("homepage_showcase", "upgrade_now", {
      showcasePosition: currentFeature,
      featureId: currentShowcase.id,
      isPremium: currentShowcase.isPremium,
    });

    trackUpgradePrompt(currentShowcase.id, "clicked", "showcase_cta", {
      source: "homepage_showcase",
      showcasePosition: currentFeature,
      featureName: currentShowcase.title,
      conversionStep: "showcase_to_upgrade",
    });

    trackFeatureEvent("direct_upgrade_clicked", {
      featureId: currentShowcase.id,
      action: "clicked",
      promptType: "showcase_cta",
      showcasePosition: currentFeature,
      category: "conversion",
    });

    setShowUpgradeModal(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={`py-20 bg-gradient-to-br from-background via-default-50/50 to-primary-50/30 relative overflow-hidden ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background decorations */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-1/4 -left-32 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 -right-32 w-64 h-64 bg-secondary/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Icon icon="heroicons:star-20-solid" className="w-4 h-4" />
            Powerful Features
          </div>
          <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Build Your Perfect Resume
          </h2>
          <p className="text-lg text-default-600 max-w-2xl mx-auto">
            Create professional resumes with our advanced features and beautiful templates
          </p>
        </motion.div>

        {/* Feature Showcase */}
        <div className="relative">
          {/* Progress indicators */}
          {showProgress && (
            <motion.div variants={itemVariants} className="flex justify-center mb-8">
              <div className="flex items-center gap-2">
                {FEATURE_SHOWCASES.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      const showcase = FEATURE_SHOWCASES[index];

                      trackShowcaseNavigation(showcase.id, index > currentFeature ? "next" : "prev", {
                        targetPosition: index,
                        currentPosition: currentFeature,
                        navigationMethod: "progress_indicator",
                      });

                      trackFeatureEvent("showcase_navigation", {
                        featureId: showcase.id,
                        action: "accessed",
                        navigationMethod: "progress_indicator",
                        category: "showcase_navigation",
                      });

                      setCurrentFeature(index);
                      setProgress(0);
                    }}
                    className={`h-2 rounded-full transition-all duration-300 ${
                      index === currentFeature ? "w-8 bg-primary" : "w-2 bg-default-300 hover:bg-default-400"
                    }`}
                  >
                    {index === currentFeature && (
                      <div
                        className="h-full bg-primary-600 rounded-full transition-all duration-100"
                        style={{ width: `${progress}%` }}
                      />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Main Feature Card */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentFeature}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
              className="max-w-5xl mx-auto"
            >
              <Card className="shadow-2xl border-none overflow-hidden bg-gradient-to-br from-white to-default-50 dark:from-default-900 dark:to-default-800">
                <CardBody className="p-0">
                  <div className="grid lg:grid-cols-2 gap-0">
                    {/* Content Side */}
                    <div className="p-8 lg:p-12 flex flex-col justify-center">
                      <div className="space-y-6">
                        {/* Feature Header */}
                        <div>
                          <div className="flex items-center gap-3 mb-4">
                            <div
                              className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${currentShowcase.gradient} flex items-center justify-center text-white shadow-lg`}
                            >
                              <Icon icon={currentShowcase.icon} className="w-6 h-6" />
                            </div>
                            <div className="flex items-center gap-2">
                              {currentShowcase.isPremium && (
                                <Chip color="primary" variant="flat" size="sm">
                                  Premium
                                </Chip>
                              )}
                              {currentShowcase.badge && (
                                <Chip color="warning" variant="flat" size="sm">
                                  {currentShowcase.badge}
                                </Chip>
                              )}
                            </div>
                          </div>

                          <h3 className="text-start text-2xl lg:text-3xl font-bold text-default-900 dark:text-default-100 mb-2">
                            {currentShowcase.title}
                          </h3>
                          <p className="text-start text-lg text-primary font-medium mb-4">{currentShowcase.subtitle}</p>
                          <p className="text-start text-default-600 dark:text-default-300 leading-relaxed">
                            {currentShowcase.description}
                          </p>
                        </div>

                        {/* Benefits */}
                        <div className="space-y-3">
                          {currentShowcase.benefits.map((benefit, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-center gap-3"
                            >
                              <div className="w-5 h-5 bg-success/10 rounded-full flex items-center justify-center">
                                <Icon icon="heroicons:check-20-solid" className="w-3 h-3 text-success" />
                              </div>
                              <span className="text-default-700 dark:text-default-200">{benefit}</span>
                            </motion.div>
                          ))}
                        </div>

                        {/* Stats */}
                        {currentShowcase.stats && (
                          <div className="flex items-center gap-6 pt-4 border-t border-default-200 dark:border-default-700">
                            {currentShowcase.stats.map((stat, index) => (
                              <div key={index} className="text-center">
                                <div className="text-2xl font-bold text-primary">{stat.value}</div>
                                <div className="text-sm text-default-600 dark:text-default-400">{stat.label}</div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* CTA Buttons */}
                        <div className="flex flex-col sm:flex-row gap-3 pt-6">
                          {currentShowcase.isPremium && (
                            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                              <Button
                                variant="bordered"
                                size="lg"
                                className="font-medium border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300 hover:shadow-lg"
                                startContent={
                                  <motion.div
                                    animate={{ rotate: [0, 5, -5, 0] }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                  >
                                    <Icon icon="heroicons:arrow-up-20-solid" className="w-4 h-4" />
                                  </motion.div>
                                }
                                onPress={handleDirectUpgrade}
                              >
                                Upgrade Now
                              </Button>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Visual Side */}
                    <div
                      className={`relative bg-gradient-to-br ${currentShowcase.gradient} p-8 lg:p-12 flex items-center justify-center min-h-[400px]`}
                    >
                      {/* Animated Visual */}
                      <motion.div
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="relative"
                      >
                        {/* Feature Icon */}
                        <div className="w-32 h-32 bg-white/20 backdrop-blur-sm rounded-3xl flex items-center justify-center shadow-2xl">
                          <Icon icon={currentShowcase.icon} className="w-16 h-16 text-white" />
                        </div>

                        {/* Floating Elements with Enhanced Whimsy */}
                        <motion.div
                          animate={{
                            y: [0, -10, 0],
                            rotate: [0, 15, -15, 0],
                            scale: celebrateFeature === currentShowcase.id ? [1, 1.3, 1] : 1,
                          }}
                          transition={{
                            y: { duration: 3, repeat: Infinity, ease: "easeInOut" },
                            rotate: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                            scale: { duration: 0.5 },
                          }}
                          className="absolute -top-4 -right-4 w-8 h-8 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg"
                        >
                          <Icon icon="heroicons:star-20-solid" className="w-4 h-4 text-white" />
                        </motion.div>

                        <motion.div
                          animate={{
                            y: [0, 10, 0],
                            rotate: [0, -10, 10, 0],
                            x: [0, 5, -5, 0],
                          }}
                          transition={{
                            duration: 2.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.5,
                          }}
                          className="absolute -bottom-4 -left-4 w-6 h-6 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg"
                        >
                          <Icon icon="heroicons:sparkles-20-solid" className="w-3 h-3 text-white" />
                        </motion.div>

                        {/* Additional playful floating elements */}
                        <motion.div
                          animate={{
                            rotate: 360,
                            scale: [1, 1.1, 1],
                          }}
                          transition={{
                            rotate: { duration: 8, repeat: Infinity, ease: "linear" },
                            scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                          }}
                          className="absolute top-1/2 -right-2 w-4 h-4 bg-white/20 rounded-full flex items-center justify-center"
                        >
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </motion.div>
                      </motion.div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Arrows */}
          <div className="flex justify-center gap-4 mt-8">
            <Button
              isIconOnly
              variant="flat"
              size="lg"
              onPress={() => {
                const newIndex = currentFeature === 0 ? FEATURE_SHOWCASES.length - 1 : currentFeature - 1;
                const showcase = FEATURE_SHOWCASES[newIndex];

                trackShowcaseNavigation(showcase.id, "prev", {
                  targetPosition: newIndex,
                  currentPosition: currentFeature,
                  navigationMethod: "arrow_button",
                });

                setCurrentFeature(newIndex);
                setProgress(0);
              }}
            >
              <Icon icon="heroicons:chevron-left-20-solid" className="w-5 h-5" />
            </Button>
            <Button
              isIconOnly
              variant="flat"
              size="lg"
              onPress={() => {
                const newIndex = (currentFeature + 1) % FEATURE_SHOWCASES.length;
                const showcase = FEATURE_SHOWCASES[newIndex];

                trackShowcaseNavigation(showcase.id, "next", {
                  targetPosition: newIndex,
                  currentPosition: currentFeature,
                  navigationMethod: "arrow_button",
                });

                setCurrentFeature(newIndex);
                setProgress(0);
              }}
            >
              <Icon icon="heroicons:chevron-right-20-solid" className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Modals */}
      <UpgradeModal isOpen={showUpgradeModal} onClose={() => setShowUpgradeModal(false)} />
    </motion.section>
  );
}

export default FeatureShowcaseCarousel;
