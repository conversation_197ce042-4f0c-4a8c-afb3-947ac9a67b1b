"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody, Input, Select, SelectItem, Textarea } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import {
  getFieldError,
  getRequiredFields,
  isFieldRequired,
  validateTemplateVariables,
} from "@/lib/email-template-validation";

interface EmailShareContentProps {
  resumeId: number;
}

export function EmailShareContent({ resumeId }: EmailShareContentProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
  const [customVariables, setCustomVariables] = useState({
    recipientName: "",
    position: "",
    companyName: "",
    personalPitch: "",
    mutualConnection: "",
    industry: "",
    meetingContext: "",
    followUpNote: "",
    personalNote: "",
    yearsOfExperience: "",
  });
  const [generatedEmail, setGeneratedEmail] = useState<{
    subject: string;
    body: string;
    resumeUrl: string;
  } | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [editableEmail, setEditableEmail] = useState<{
    subject: string;
    body: string;
  } | null>(null);

  // Fetch email templates (retry on auth error)
  const {
    data: templates,
    isLoading: templatesLoading,
    error: templatesError,
  } = trpc.sharing.getEmailTemplates.useQuery(undefined, {
    retry: (failureCount, error) => {
      // Retry up to 3 times for auth errors (user might still be logging in)
      if (error?.message?.includes("UNAUTHORIZED") && failureCount < 3) {
        return true;
      }
      return false;
    },
    retryDelay: 1000, // Wait 1 second between retries
  });

  // Generate email content mutation
  const generateEmailMutation = trpc.sharing.generateEmailContent.useMutation({
    onSuccess: (data) => {
      setGeneratedEmail(data);
      setEditableEmail({
        subject: data.subject,
        body: data.body,
      });
      setIsEditing(false);
    },
    onError: (error) => {
      toast.error("Failed to generate email content");
      console.error("Email generation error:", error);
    },
  });

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplateId(templateId);
    setGeneratedEmail(null);
    setEditableEmail(null);
    setValidationErrors({});
    setIsEditing(false);
  };

  const handleVariableChange = (key: string, value: string) => {
    setCustomVariables((prev) => ({ ...prev, [key]: value }));

    // Validate field on change
    if (selectedTemplateId) {
      const error = getFieldError(selectedTemplateId, key, value);
      setValidationErrors((prev) => ({
        ...prev,
        [key]: error || "",
      }));
    }
  };

  // Validate all required fields
  const validateAllFields = (): boolean => {
    if (!selectedTemplateId) return false;

    const validation = validateTemplateVariables(selectedTemplateId, customVariables);
    if (!validation.success) {
      const errors: Record<string, string> = {};
      validation.error.issues.forEach((issue) => {
        if (issue.path[0]) {
          errors[issue.path[0] as string] = issue.message;
        }
      });
      setValidationErrors(errors);
      return false;
    }

    setValidationErrors({});
    return true;
  };

  const handleGenerateEmail = () => {
    if (!selectedTemplateId) {
      toast.error("Please select a template first");
      return;
    }

    if (!validateAllFields()) {
      return;
    }

    generateEmailMutation.mutate({
      resumeId,
      templateId: selectedTemplateId,
      variables: customVariables,
    });
  };

  const handleSendEmail = () => {
    const emailToSend = editableEmail || generatedEmail;
    if (!emailToSend) return;

    const mailtoLink = `mailto:?subject=${encodeURIComponent(emailToSend.subject)}&body=${encodeURIComponent(emailToSend.body)}`;

    // Try to detect if mailto will work
    const isEmailClientAvailable = /(iPad|iPhone|iPod|Mac|Windows|Linux)/i.test(navigator.userAgent);

    if (isEmailClientAvailable) {
      // Use location.href for better compatibility
      window.location.href = mailtoLink;
      toast.success("Opening email client...");
    } else {
      // Fallback: copy the email and show instructions
      handleCopyEmail();
      toast.success("Email copied to clipboard");
    }
  };

  const handleCopyEmail = async () => {
    const emailToSend = editableEmail || generatedEmail;
    if (!emailToSend) return;

    const emailContent = `Subject: ${emailToSend.subject}\n\n${emailToSend.body}`;

    try {
      await navigator.clipboard.writeText(emailContent);
      toast.success("Email copied to clipboard");
    } catch (error) {
      toast.error("Failed to copy email");
    }
  };

  const handleEditToggle = () => {
    if (!isEditing && generatedEmail) {
      setEditableEmail({
        subject: generatedEmail.subject,
        body: generatedEmail.body,
      });
    }
    setIsEditing(!isEditing);
  };

  const handleEditableChange = (field: "subject" | "body", value: string) => {
    setEditableEmail((prev) => (prev ? { ...prev, [field]: value } : null));
  };

  const selectedTemplate = templates?.find((t) => t.id === selectedTemplateId);

  // Helper function to render input with validation
  const renderInput = (
    key: string,
    label: string,
    placeholder: string,
    icon: string,
    type: "input" | "textarea" = "input",
    minRows?: number,
    className?: string,
  ) => {
    const isRequired = selectedTemplateId ? isFieldRequired(selectedTemplateId, key) : false;
    const error = validationErrors[key];
    const hasError = !!error;

    const commonProps = {
      label: (
        <span>
          {label}
          {isRequired && <span className="text-danger ml-1">*</span>}
        </span>
      ),
      placeholder,
      value: customVariables[key as keyof typeof customVariables] || "",
      onValueChange: (value: string) => handleVariableChange(key, value),
      startContent: <Icon icon={icon} className="w-4 h-4" />,
      isInvalid: hasError,
      errorMessage: error,
      className: className || "",
    };

    if (type === "textarea") {
      return <Textarea {...commonProps} minRows={minRows || 2} />;
    }

    return <Input {...commonProps} />;
  };

  return (
    <div className="space-y-6">
      {/* Template Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Select Email Template</h3>
        {templatesError && (
          <div className="p-3 bg-danger-50 border border-danger-200 rounded-lg">
            <p className="text-sm text-danger-600">Error loading templates: {templatesError.message}</p>
          </div>
        )}
        <Select
          placeholder="Choose a template"
          selectedKeys={selectedTemplateId ? [selectedTemplateId] : []}
          onSelectionChange={(keys) => {
            const selected = Array.from(keys)[0];
            if (selected) {
              handleTemplateChange(selected.toString());
            }
          }}
          isLoading={templatesLoading}
          startContent={<Icon icon="tabler:template" className="w-4 h-4" />}
        >
          {templates?.map((template) => (
            <SelectItem key={template.id}>
              <div className="flex flex-col">
                <span className="font-medium">{template.name}</span>
                <span className="text-xs text-default-400 capitalize">{template.category}</span>
              </div>
            </SelectItem>
          )) || []}
        </Select>
        {!templatesLoading && (!templates || templates.length === 0) && (
          <div className="p-3 bg-warning-50 border border-warning-200 rounded-lg">
            <p className="text-sm text-warning-600">No email templates found. Debug info in console.</p>
          </div>
        )}
      </div>

      {/* Template Variables */}
      {selectedTemplate && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Customize Email</h3>
            <div className="flex items-center gap-1 text-xs text-default-500">
              <Icon icon="tabler:asterisk" className="w-3 h-3 text-danger" />
              <span>Required fields</span>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectedTemplate.category === "professional" && (
              <>
                {renderInput("recipientName", "Recipient Name", "Enter the recipient's name", "tabler:user")}

                {selectedTemplate.id === "job-application" && (
                  <>
                    {renderInput("position", "Position", "Enter the job position", "tabler:briefcase")}
                    {renderInput(
                      "companyName",
                      "Company Name",
                      "Enter the company name",
                      "tabler:building",
                      "input",
                      undefined,
                      "md:col-span-2",
                    )}
                    {renderInput(
                      "personalPitch",
                      "Personal Pitch",
                      "Write a brief personal pitch",
                      "tabler:message",
                      "textarea",
                      3,
                      "md:col-span-2",
                    )}
                  </>
                )}

                {selectedTemplate.id === "follow-up" && (
                  <>
                    {renderInput(
                      "meetingContext",
                      "Meeting Context",
                      "Describe where you met or the context",
                      "tabler:calendar",
                      "input",
                      undefined,
                      "md:col-span-2",
                    )}
                    {renderInput(
                      "followUpNote",
                      "Follow-up Note",
                      "Add a follow-up message",
                      "tabler:note",
                      "textarea",
                      2,
                      "md:col-span-2",
                    )}
                  </>
                )}

                {selectedTemplate.id === "professional-introduction" && (
                  <>
                    {renderInput(
                      "yearsOfExperience",
                      "Years of Experience",
                      "Number of years of experience",
                      "tabler:clock",
                    )}
                  </>
                )}
              </>
            )}

            {selectedTemplate.category === "networking" && (
              <>
                {renderInput("recipientName", "Recipient Name", "Enter the recipient's name", "tabler:user")}
                {renderInput("mutualConnection", "Mutual Connection", "Name of your mutual connection", "tabler:users")}
                {renderInput(
                  "industry",
                  "Industry",
                  "Enter the industry",
                  "tabler:building-store",
                  "input",
                  undefined,
                  "md:col-span-2",
                )}
              </>
            )}

            {selectedTemplate.category === "casual" && (
              <>
                {renderInput("recipientName", "Recipient Name", "Enter the recipient's name", "tabler:user")}
                {renderInput(
                  "personalNote",
                  "Personal Note",
                  "Add a personal message",
                  "tabler:message",
                  "textarea",
                  2,
                  "md:col-span-2",
                )}
              </>
            )}
          </div>

          <Button
            onPress={handleGenerateEmail}
            isLoading={generateEmailMutation.isPending}
            color="primary"
            startContent={<Icon icon="tabler:wand" className="w-4 h-4" />}
            className="w-full"
          >
            Generate Email
          </Button>
        </div>
      )}

      {/* Generated Email Preview */}
      {generatedEmail && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Email Preview</h3>
            <Button
              size="sm"
              variant="flat"
              onPress={handleEditToggle}
              startContent={<Icon icon={isEditing ? "tabler:check" : "tabler:edit"} className="w-4 h-4" />}
            >
              {isEditing ? "Save Changes" : "Edit Email"}
            </Button>
          </div>

          <Card>
            <CardBody className="space-y-4">
              <div>
                <p className="text-sm font-medium text-default-600 mb-1">Subject:</p>
                {isEditing && editableEmail ? (
                  <Input
                    value={editableEmail.subject}
                    onValueChange={(value) => handleEditableChange("subject", value)}
                    className="text-sm"
                  />
                ) : (
                  <p className="text-sm bg-default-100 p-2 rounded">
                    {editableEmail?.subject || generatedEmail.subject}
                  </p>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-default-600 mb-1">Body:</p>
                {isEditing && editableEmail ? (
                  <Textarea
                    value={editableEmail.body}
                    onValueChange={(value) => handleEditableChange("body", value)}
                    minRows={8}
                    className="text-sm"
                  />
                ) : (
                  <div className="text-sm bg-default-100 p-4 rounded whitespace-pre-wrap max-h-60 overflow-y-auto">
                    {editableEmail?.body || generatedEmail.body}
                  </div>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-default-600 mb-1">Resume Link:</p>
                <p className="text-xs text-primary break-all">{generatedEmail.resumeUrl}</p>
              </div>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="flat"
              onPress={handleCopyEmail}
              startContent={<Icon icon="tabler:copy" className="w-4 h-4" />}
              className="flex-1"
            >
              Copy Email
            </Button>
            <Button
              color="primary"
              onPress={handleSendEmail}
              startContent={<Icon icon="tabler:mail" className="w-4 h-4" />}
              className="flex-1"
            >
              Send Email
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
