import React from "react";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";
import { WebsiteTemplateProps } from "@/components/shared/template-system/types";

export const TechTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");

  // Navigation sections
  const sections = [
    { id: "about", label: "About" },
    { id: "experience", label: "Experience" },
    { id: "projects", label: "Projects" },
    { id: "skills", label: "Skills" },
    { id: "education", label: "Education" },
    { id: "contact", label: "Contact" },
  ];

  return (
    <div className={`tech-template min-h-screen bg-gray-900 text-white ${className}`}>
      {/* Navigation */}
      <nav className="bg-gray-800/90 backdrop-blur-sm border-b border-gray-700 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="font-mono text-xl text-green-400">{`<${fullName.replace(/\s+/g, "")}/>`}</div>
            <div className="hidden md:flex space-x-6 font-mono text-sm">
              {sections.map((section) => (
                <a
                  key={section.id}
                  href={`#${section.id}`}
                  className="text-gray-300 hover:text-green-400 transition-colors duration-200"
                >
                  {section.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 text-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden">
        {/* Terminal-style background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="font-mono text-xs leading-4 p-4">
            {Array.from({ length: 50 }, (_, i) => (
              <div key={i} className="mb-1">
                {`> console.log("Hello World"); // Line ${i + 1}`}
              </div>
            ))}
          </div>
        </div>

        <div className="relative z-10">
          {resume.showPhoto === 1 && resume.photo && (
            <div className="mb-8">
              <div className="w-32 h-32 mx-auto rounded-full border-4 border-green-400 p-1 bg-gradient-to-r from-green-400 to-blue-500">
                <img alt={fullName} className="w-full h-full rounded-full object-cover" src={resume.photo} />
              </div>
            </div>
          )}

          <div className="font-mono mb-4">
            <span className="text-green-400">const</span> <span className="text-blue-400">developer</span>{" "}
            <span className="text-white">=</span> <span className="text-yellow-400">"{fullName}"</span>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
            {fullName}
          </h1>

          <div className="font-mono text-lg mb-8">
            <span className="text-gray-400">//</span> <span className="text-cyan-400">{resume.jobTitle}</span>
          </div>

          {resume.bio && (
            <div
              dangerouslySetInnerHTML={{ __html: resume.bio }}
              className="text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8"
            />
          )}

          {/* Animated code snippet */}
          <div className="bg-gray-800 rounded-lg p-4 font-mono text-sm text-start max-w-md mx-auto border border-gray-700">
            <div className="flex items-center mb-2">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <span className="ms-4 text-gray-400">terminal</span>
            </div>
            <div className="text-green-400">
              <span className="text-blue-400">$</span> whoami
            </div>
            <div className="text-white">{resume.jobTitle}</div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-16">
        {/* About Section */}
        <section className="mb-20" id="about">
          <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
            <span className="text-gray-400">//</span> About
          </h2>
          <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
            <div className="text-gray-300 leading-relaxed">
              {resume.bio ? (
                <div dangerouslySetInnerHTML={{ __html: resume.bio }} />
              ) : (
                <p>
                  <span className="text-blue-400">console.log</span>
                  <span className="text-white">(</span>
                  <span className="text-yellow-400">
                    "Passionate {resume.jobTitle} with expertise in modern technologies."
                  </span>
                  <span className="text-white">);</span>
                </p>
              )}
            </div>
            {location && (
              <div className="mt-6 font-mono text-sm">
                <span className="text-green-400">location</span>
                <span className="text-white">:</span> <span className="text-yellow-400">"{location}"</span>
              </div>
            )}
          </div>
        </section>

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-20" id="experience">
            <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
              <span className="text-gray-400">//</span> Experience
            </h2>
            <div className="space-y-8">
              {resume.experiences.map((experience, index) => (
                <div
                  key={experience.id}
                  className="bg-gray-800 rounded-lg p-8 border border-gray-700 relative overflow-hidden"
                >
                  {/* Terminal prompt indicator */}
                  <div className="absolute top-4 start-4 font-mono text-xs text-green-400">{`[${index + 1}]`}</div>

                  <div className="ms-8">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4">
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-2 font-mono">{experience.title}</h3>
                        <p className="text-xl text-cyan-400 font-semibold mb-2">{experience.company}</p>
                        {formatLocation(experience.city || "", experience.country || "") && (
                          <p className="text-gray-400 font-mono text-sm">
                            {formatLocation(experience.city || "", experience.country || "")}
                          </p>
                        )}
                      </div>
                      <div className="text-gray-400 mt-4 lg:mt-0 font-mono text-sm">
                        <div className="bg-gray-700 px-3 py-1 rounded border">
                          {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                        </div>
                      </div>
                    </div>
                    {experience.description && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: experience.description,
                        }}
                        className="text-gray-300 leading-relaxed"
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-20" id="projects">
            <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
              <span className="text-gray-400">//</span> Projects
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              {resume.projects.map((project, index) => (
                <div
                  key={project.id}
                  className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-green-400 transition-all duration-300 group"
                >
                  {/* Project icon with terminal styling */}
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded flex items-center justify-center font-mono text-black font-bold text-sm">
                      {project.title?.charAt(0) || "P"}
                    </div>
                    <div className="ms-3 font-mono text-sm text-gray-400">project_{index + 1}/</div>
                  </div>

                  <h3 className="text-xl font-bold text-white mb-3 font-mono">{project.title}</h3>

                  {project.client && (
                    <p className="text-cyan-400 font-semibold mb-3 font-mono text-sm">client: "{project.client}"</p>
                  )}

                  {project.description && (
                    <div
                      dangerouslySetInnerHTML={{ __html: project.description }}
                      className="text-gray-300 mb-6 leading-relaxed"
                    />
                  )}

                  {project.url && (
                    <a
                      href={project.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-black rounded font-mono text-sm font-bold hover:from-green-400 hover:to-blue-400 transition-all duration-300"
                    >
                      <span className="me-2">{">"}</span>
                      view_project()
                    </a>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-20" id="skills">
            <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
              <span className="text-gray-400">//</span> Skills
            </h2>
            <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
              <div className="font-mono text-sm mb-4">
                <span className="text-blue-400">const</span> <span className="text-white">skills</span>{" "}
                <span className="text-white">=</span> <span className="text-white">{"{"}</span>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 ms-4">
                {/* Group skills by category */}
                {Object.entries(
                  resume.skills.reduce(
                    (acc, skill) => {
                      const category = skill.category || "other";
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(skill);
                      return acc;
                    },
                    {} as Record<string, typeof resume.skills>,
                  ),
                ).map(([category, categorySkills]) => (
                  <div key={category} className="mb-4">
                    <div className="font-mono text-cyan-400 mb-3">{category}: [</div>
                    <div className="ms-4 space-y-1">
                      {categorySkills.map((skill, index) => (
                        <div key={skill.id} className="font-mono text-sm">
                          <span className="text-yellow-400">"{skill.name}"</span>
                          {index < categorySkills.length - 1 && <span className="text-white">,</span>}
                        </div>
                      ))}
                    </div>
                    <div className="font-mono text-cyan-400 mt-2">],</div>
                  </div>
                ))}
              </div>

              <div className="font-mono text-sm">
                <span className="text-white">{"}"}</span>
              </div>
            </div>
          </section>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-20" id="education">
            <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
              <span className="text-gray-400">//</span> Education
            </h2>
            <div className="space-y-6">
              {resume.educations.map((education) => (
                <div key={education.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-2 font-mono">{education.institution}</h3>
                      <p className="text-cyan-400 font-semibold mb-2 font-mono">
                        {education.degree}
                        {education.fieldOfStudy && ` // ${education.fieldOfStudy}`}
                      </p>
                      {formatLocation(education.city || "", education.country || "") && (
                        <p className="text-gray-400 font-mono text-sm">
                          {formatLocation(education.city || "", education.country || "")}
                        </p>
                      )}
                    </div>
                    <div className="text-gray-400 mt-4 lg:mt-0 font-mono text-sm">
                      <div className="bg-gray-700 px-3 py-1 rounded border">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </div>
                    </div>
                  </div>
                  {education.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: education.description,
                      }}
                      className="text-gray-300 mt-4 leading-relaxed"
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Contact Section */}
        <section id="contact">
          <h2 className="text-3xl font-bold mb-8 text-green-400 font-mono">
            <span className="text-gray-400">//</span> Contact
          </h2>
          <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
            <div className="font-mono">
              <div className="mb-4">
                <span className="text-blue-400">const</span> <span className="text-white">contact</span>{" "}
                <span className="text-white">=</span> <span className="text-white">{"{"}</span>
              </div>

              <div className="ms-4 space-y-2">
                {resume.email && (
                  <div>
                    <span className="text-cyan-400">email</span>
                    <span className="text-white">: </span>
                    <a
                      href={`mailto:${resume.email}`}
                      className="text-yellow-400 hover:text-yellow-300 transition-colors"
                    >
                      "{resume.email}"
                    </a>
                    <span className="text-white">,</span>
                  </div>
                )}

                {resume.website && (
                  <div>
                    <span className="text-cyan-400">website</span>
                    <span className="text-white">: </span>
                    <a
                      href={resume.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-yellow-400 hover:text-yellow-300 transition-colors"
                    >
                      "{resume.website}"
                    </a>
                    <span className="text-white">,</span>
                  </div>
                )}

                {location && (
                  <div>
                    <span className="text-cyan-400">location</span>
                    <span className="text-white">: </span>
                    <span className="text-yellow-400">"{location}"</span>
                  </div>
                )}
              </div>

              <div className="mt-4">
                <span className="text-white">{"}"}</span>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 border-t border-gray-700 py-8 text-center">
        <div className="max-w-6xl mx-auto px-4">
          <div className="font-mono text-gray-400 mb-2">
            <span className="text-gray-500">//</span> © {new Date().getFullYear()} {fullName}
          </div>
          <div className="font-mono text-sm text-gray-500">
            <span className="text-blue-400">console.log</span>
            <span className="text-white">(</span>
            <span className="text-yellow-400">"Made with "</span>
            <span className="text-white"> + </span>
            <a
              href="https://quickcv.com"
              className="text-green-400 hover:text-green-300 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              "QuickCV"
            </a>
            <span className="text-white">);</span>
          </div>
        </div>
      </footer>
    </div>
  );
};
