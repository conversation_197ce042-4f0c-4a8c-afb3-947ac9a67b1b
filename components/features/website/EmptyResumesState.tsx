import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";
import { GradientEmptyState } from "@/components/shared/empty-state";

const EmptyResumesState = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-2xl mx-auto px-4">
        <div className="text-center py-16">
          <GradientEmptyState
            icon="heroicons:document-text"
            title="No resumes found"
            description="You need to create a resume first before building your website"
            actionButton={
              <Button
                as={Link}
                href="/resumes"
                color="primary"
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg hover:shadow-xl transition-shadow"
                startContent={<Icon icon="heroicons:plus" />}
              >
                Create Your First Resume
              </Button>
            }
          />
        </div>
      </div>
    </div>
  );
};

export default EmptyResumesState;
