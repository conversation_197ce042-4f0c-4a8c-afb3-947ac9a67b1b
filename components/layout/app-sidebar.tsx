"use client";

import { SignedIn, SignedOut } from "@clerk/nextjs";
import { <PERSON>, Divider, Too<PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import CreateResumeButton from "@/components/features/resume/createResumeButton";
import { ThemeSwitch } from "@/components/shared";
import { Logo } from "@/components/shared/common/icons";
import { CustomUserButton } from "@/components/shared/custom-user-button";
import { getMainNavigationItems, getUserMenuItems } from "@/config/navbar-menu";
import { useSidebarStore } from "@/stores/sidebar-store";
import { SidebarItem } from "./sidebar-item";
import { SidebarToggle } from "./sidebar-toggle";
import { UserDropdown } from "./user-dropdown";

// Helper function to get English labels for translation keys
const getEnglishLabel = (translationKey: string): string => {
  const labels: Record<string, string> = {
    about: "About",
    templates: "Templates",
    resumes: "Resumes",
    websites: "Websites",
    contact: "Contact",
    profile: "Profile",
    billing: "Billing",
    support: "Support",
    signin: "Sign In",
  };
  return labels[translationKey] || translationKey;
};

interface AppSidebarProps {
  className?: string;
}

export const AppSidebar = ({ className = "" }: AppSidebarProps) => {
  const pathname = usePathname();
  const { isCollapsed, isMobile, setMobile } = useSidebarStore();
  const [mounted, setMounted] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    setMounted(true);

    const checkMobile = () => {
      const mobile = window.innerWidth < 1024; // lg breakpoint
      setMobile(mobile);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [setMobile]);

  // Check if link is active
  const isActive = (path: string) => {
    const cleanPathname = pathname.replace(/^\/(en|ar)/, "");
    const cleanPath = path.replace(/^\/(en|ar)/, "");
    return cleanPathname === cleanPath || cleanPathname.startsWith(cleanPath + "/");
  };

  const menuItems = getMainNavigationItems();
  const userMenuItems = getUserMenuItems();

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  // Mobile sidebar is handled by MobileSidebarDrawer
  if (isMobile) {
    return null;
  }

  const sidebarWidth = isCollapsed ? "w-20" : "w-72";

  return (
    <Card
      className={`
        fixed left-0 top-0 h-screen ${sidebarWidth} 
        transition-all duration-300 ease-in-out
        bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg
        border-r border-gray-200/20 dark:border-gray-700/20
        shadow-xl z-40 flex flex-col
        ${className}
      `}
      radius="none"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Header with Logo and Toggle */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200/20 dark:border-gray-700/20">
        {!isCollapsed && (
          <Link href="/" className="flex items-center gap-2 group">
            <div className="relative">
              <Logo className="w-8 h-8 transition-transform group-hover:scale-110" />
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30 group-hover:opacity-50 transition-opacity" />
            </div>
            <p className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              QuickCV
            </p>
          </Link>
        )}
        {isCollapsed && (
          <Link href="/" className="relative mx-auto group">
            <Logo className="w-8 h-8 transition-transform group-hover:scale-110" />
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30 group-hover:opacity-50 transition-opacity" />
          </Link>
        )}
        <SidebarToggle />
      </div>

      {/* Navigation Items */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-2 px-3">
          {menuItems.map((item) => {
            if (item.signedInOnly) {
              return (
                <SignedIn key={item.href}>
                  <SidebarItem
                    item={item}
                    isActive={isActive(item.href)}
                    isCollapsed={isCollapsed}
                    label={getEnglishLabel(item.translationKey)}
                  />
                </SignedIn>
              );
            }
            return (
              <SidebarItem
                key={item.href}
                item={item}
                isActive={isActive(item.href)}
                isCollapsed={isCollapsed}
                label={getEnglishLabel(item.translationKey)}
              />
            );
          })}
        </nav>

        {/* Create Resume Section */}
        <SignedIn>
          <div className="px-3 py-4 border-t border-gray-200/20 dark:border-gray-700/20 mt-4">
            {isCollapsed ? (
              <Tooltip content="Create Resume" placement="right">
                <div>
                  <CreateResumeButton
                    size="sm"
                    text="+"
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
                  />
                </div>
              </Tooltip>
            ) : (
              <CreateResumeButton
                size="sm"
                text="Create Resume"
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
              />
            )}
          </div>
        </SignedIn>

        {/* User Menu Items */}
        <SignedIn>
          <div className="px-3 py-4 border-t border-gray-200/20 dark:border-gray-700/20">
            {!isCollapsed && (
              <p className="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3">
                Account
              </p>
            )}
            <div className="space-y-1">
              {userMenuItems.map((item) => (
                <SidebarItem
                  key={item.href}
                  item={item}
                  isActive={isActive(item.href)}
                  isCollapsed={isCollapsed}
                  label={getEnglishLabel(item.translationKey)}
                />
              ))}
            </div>
          </div>
        </SignedIn>
      </div>

      {/* Footer with Theme Switch and User */}
      <div className="border-t border-gray-200/20 dark:border-gray-700/20 p-4">
        <div className="flex items-center justify-between">
          {isCollapsed ? (
            <div className="flex flex-col items-center gap-3 w-full">
              <Tooltip content="Toggle theme" placement="right">
                <div className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <ThemeSwitch />
                </div>
              </Tooltip>
              <SignedIn>
                <UserDropdown trigger={<CustomUserButton size="sm" />} />
              </SignedIn>
            </div>
          ) : (
            <>
              <div className="flex items-center gap-3">
                <Tooltip content="Toggle theme">
                  <div className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                    <ThemeSwitch />
                  </div>
                </Tooltip>
              </div>
              <SignedIn>
                <UserDropdown trigger={<CustomUserButton size="sm" />} />
              </SignedIn>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};
