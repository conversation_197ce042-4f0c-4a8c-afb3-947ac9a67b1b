// Contact and support form configuration
export interface FormCategory {
  key: string;
  label: string;
  icon: string;
  supportOnly?: boolean; // Categories that only appear in support
  contactOnly?: boolean; // Categories that only appear in contact
}

export interface FormPriority {
  key: string;
  label: string;
  color: "success" | "primary" | "warning" | "danger";
  description: string;
}

// Contact and support form validation configuration
export const contactSupportFormConfig = {
  validation: {
    maxFileSize: 8 * 1024 * 1024, // 8MB (UploadThing limit)
    maxFiles: 3,
    allowedFileTypes: [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
    minFormProgress: 60, // Minimum progress required to submit
  },

  // Valid categories for both forms
  validCategories: ["general", "technical", "billing", "feature", "account"],

  // Valid priorities for both forms
  validPriorities: ["low", "normal", "high", "urgent"],

  // Response time mapping
  responseTimes: {
    urgent: "1-2",
    high: "2-4",
    normal: "4-8",
    low: "8-24",
  } as Record<string, string>,
};

// Unified categories configuration - includes all categories with flags for specific forms
export const getContactSupportFormCategories = (formType: "contact" | "support" = "contact"): FormCategory[] => {
  const allCategories = [
    { key: "general", label: "General", icon: "tabler:help" },
    { key: "technical", label: "Technical", icon: "tabler:bug" },
    { key: "billing", label: "Billing", icon: "tabler:credit-card" },
    { key: "feature", label: "Feature Request", icon: "tabler:bulb", contactOnly: true },
    { key: "account", label: "Account", icon: "tabler:user", contactOnly: true },
  ];

  // Filter categories based on form type
  return allCategories.filter((category: any) => {
    if (formType === "contact") {
      return !category.supportOnly;
    } else {
      return !category.contactOnly;
    }
  });
};

// Unified priority levels configuration
export const getContactSupportFormPriorities = (): FormPriority[] => [
  { key: "low", label: "Low", color: "success", description: "Response within 8-24 hours" },
  { key: "normal", label: "Normal", color: "primary", description: "Response within 4-8 hours" },
  { key: "high", label: "High", color: "warning", description: "Response within 2-4 hours" },
  { key: "urgent", label: "Urgent", color: "danger", description: "Response within 1-2 hours" },
];

// Contact and support form validation
export const validateContactSupportFormData = (formData: FormData, formType: "contact" | "support" = "contact") => {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const subject = formData.get("subject") as string;
  const message = formData.get("message") as string;
  const category = (formData.get("category") as string) || "general";
  const priority = (formData.get("priority") as string) || "normal";

  // Validate required fields
  if (!name || !email || !subject || !message) {
    return { success: false, error: "All fields are required" };
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { success: false, error: "Please enter a valid email address" };
  }

  // Validate category and priority
  const validCategories =
    formType === "contact"
      ? ["general", "technical", "billing", "feature", "account"]
      : ["general", "technical", "billing"];

  if (!validCategories.includes(category)) {
    return { success: false, error: "Invalid category selected" };
  }

  if (!contactSupportFormConfig.validPriorities.includes(priority)) {
    return { success: false, error: "Invalid priority selected" };
  }

  return {
    success: true,
    data: { name, email, subject, message, category, priority },
  };
};

// Calculate form progress for contact/support forms
export const calculateContactSupportFormProgress = (selectedCategory: string, selectedPriority: string): number => {
  const form = (document.getElementById("contact-form") || document.getElementById("support-form")) as HTMLFormElement;
  if (!form) return 0;

  const formData = new FormData(form);
  const fields = ["name", "email", "subject", "message"];
  const filledFields = fields.filter((field) => {
    const value = formData.get(field)?.toString().trim();
    return value && value.length > 0;
  });

  const categorySelected = selectedCategory ? 1 : 0;
  const prioritySelected = selectedPriority ? 1 : 0;

  return ((filledFields.length + categorySelected + prioritySelected) / (fields.length + 2)) * 100;
};
