import { Metadata } from "next";

import CTASection from "@/components/features/homepage/cta-section";
import FeaturesSection from "@/components/features/homepage/features-section";
import HeroSection from "@/components/features/homepage/hero-section";
import PricingSection from "@/components/features/homepage/pricing-section";
import StatsSection from "@/components/features/homepage/stats-section";
import StructuredData from "@/components/features/homepage/structured-data";
import TemplatesShowcase from "@/components/features/homepage/templates-showcase";
import TestimonialsSection from "@/components/features/homepage/testimonials-section";
import { FeatureShowcaseCarousel } from "@/components/features/showcase/feature-showcase-carousel";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Create Professional Resumes in Minutes - QuickCV",
    description:
      "Build ATS-optimized resumes with professional templates. Free resume builder with instant PDF download and expert-designed layouts.",
    keywords:
      "resume builder, CV maker, professional resume, ATS optimized, free resume templates, job application, career tools",
    openGraph: {
      title: "Create Professional Resumes in Minutes - QuickCV",
      description:
        "Build ATS-optimized resumes with professional templates. Free resume builder with instant PDF download and expert-designed layouts.",
      type: "website",
      url: "https://quickcv.app",
      images: [
        {
          url: "/og-image.jpg",
          width: 1200,
          height: 630,
          alt: "Create Professional Resumes in Minutes - QuickCV",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Create Professional Resumes in Minutes - QuickCV",
      description:
        "Build ATS-optimized resumes with professional templates. Free resume builder with instant PDF download and expert-designed layouts.",
      images: ["/og-image.jpg"],
    },
    metadataBase: new URL("https://quickcv.app"),
    alternates: {
      canonical: "https://quickcv.app",
    },
  };
}

export default async function Home() {
  return (
    <>
      <StructuredData />
      <HeroSection />
      <FeaturesSection />
      <FeatureShowcaseCarousel />
      <TemplatesShowcase />
      <PricingSection />
      <StatsSection />
      <TestimonialsSection />
      <CTASection />
    </>
  );
}
