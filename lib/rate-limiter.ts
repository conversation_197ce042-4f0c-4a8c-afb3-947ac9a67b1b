/**
 * Rate Limiter for AI API Endpoints
 *
 * This module provides rate limiting functionality to prevent abuse of AI services
 * and ensure fair usage across all users. It supports both in-memory and Redis-based
 * rate limiting with configurable windows and limits.
 *
 * Features:
 * - Per-user rate limiting with user identification
 * - Configurable time windows and request limits
 * - Memory-efficient sliding window implementation
 * - Support for different rate limit tiers (free/premium users)
 * - Automatic cleanup of expired entries
 * - Rate limit headers for API responses
 *
 * <AUTHOR> AI Team
 * @version 1.0.0
 */

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyPrefix?: string; // Prefix for storage keys
  skipSuccessfulRequests?: boolean; // Only count failed requests
  skipFailedRequests?: boolean; // Only count successful requests
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
}

interface RateLimitEntry {
  requests: number[]; // Array of timestamps
  createdAt: number;
}

export class RateLimiterError extends Error {
  constructor(
    message: string,
    public rateLimitResult: RateLimitResult,
  ) {
    super(message);
    this.name = "RateLimiterError";
  }
}

/**
 * In-memory rate limiter using sliding window algorithm
 */
export class MemoryRateLimiter {
  private store = new Map<string, RateLimitEntry>();
  private config: Required<RateLimitConfig>;
  private cleanupInterval: NodeJS.Timeout;

  constructor(config: RateLimitConfig) {
    this.config = {
      keyPrefix: "rl",
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config,
    };

    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(
      () => {
        this.cleanup();
      },
      5 * 60 * 1000,
    );
  }

  /**
   * Check if request is allowed and update counters
   */
  async hit(key: string): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    const storageKey = `${this.config.keyPrefix}:${key}`;

    // Get or create entry
    let entry = this.store.get(storageKey);
    if (!entry) {
      entry = { requests: [], createdAt: now };
      this.store.set(storageKey, entry);
    }

    // Remove requests outside the current window
    entry.requests = entry.requests.filter((timestamp) => timestamp > windowStart);

    // Check if limit exceeded
    const currentRequests = entry.requests.length;
    const allowed = currentRequests < this.config.maxRequests;

    // Add current request if allowed
    if (allowed) {
      entry.requests.push(now);
    }

    // Calculate reset time (start of next window)
    const oldestRequest = entry.requests[0] || now;
    const resetTime = oldestRequest + this.config.windowMs;

    return {
      allowed,
      remaining: Math.max(0, this.config.maxRequests - entry.requests.length),
      resetTime,
      totalHits: entry.requests.length,
    };
  }

  /**
   * Get current rate limit status without incrementing
   */
  async check(key: string): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    const storageKey = `${this.config.keyPrefix}:${key}`;

    const entry = this.store.get(storageKey);
    if (!entry) {
      return {
        allowed: true,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs,
        totalHits: 0,
      };
    }

    // Filter requests in current window
    const validRequests = entry.requests.filter((timestamp) => timestamp > windowStart);
    const oldestRequest = validRequests[0] || now;

    return {
      allowed: validRequests.length < this.config.maxRequests,
      remaining: Math.max(0, this.config.maxRequests - validRequests.length),
      resetTime: oldestRequest + this.config.windowMs,
      totalHits: validRequests.length,
    };
  }

  /**
   * Reset rate limit for a specific key
   */
  async reset(key: string): Promise<void> {
    const storageKey = `${this.config.keyPrefix}:${key}`;
    this.store.delete(storageKey);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredThreshold = now - this.config.windowMs * 2; // Keep some buffer

    for (const [key, entry] of Array.from(this.store.entries())) {
      // Remove entries that are completely expired
      if (entry.createdAt < expiredThreshold) {
        this.store.delete(key);
        continue;
      }

      // Clean up old requests within entries
      const windowStart = now - this.config.windowMs;
      entry.requests = entry.requests.filter((timestamp) => timestamp > windowStart);

      // Remove entry if no requests in current window
      if (entry.requests.length === 0 && entry.createdAt < windowStart) {
        this.store.delete(key);
      }
    }
  }

  /**
   * Get statistics about the rate limiter
   */
  getStats(): { totalKeys: number; totalRequests: number } {
    let totalRequests = 0;
    for (const entry of Array.from(this.store.values())) {
      totalRequests += entry.requests.length;
    }
    return {
      totalKeys: this.store.size,
      totalRequests,
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.store.clear();
  }
}

/**
 * Rate limiter manager with different configurations for different endpoints
 */
export class RateLimiterManager {
  private limiters = new Map<string, MemoryRateLimiter>();

  constructor() {
    // Initialize rate limiters for different endpoints/tiers
    this.setupLimiters();
  }

  private setupLimiters(): void {
    // Import AI configuration
    const { aiConfig } = require("./ai-config");

    // AI Generation - Free tier
    this.limiters.set(
      "ai-free",
      new MemoryRateLimiter({
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: aiConfig.rateLimit.freeHourly,
        keyPrefix: "ai-free",
      }),
    );

    // AI Generation - Premium tier
    this.limiters.set(
      "ai-premium",
      new MemoryRateLimiter({
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: aiConfig.rateLimit.premiumHourly,
        keyPrefix: "ai-premium",
      }),
    );

    // AI Generation - Per minute limit (burst protection)
    this.limiters.set(
      "ai-burst",
      new MemoryRateLimiter({
        windowMs: 60 * 1000, // 1 minute
        maxRequests: aiConfig.rateLimit.burstMinute,
        keyPrefix: "ai-burst",
      }),
    );

    // Global API rate limit
    this.limiters.set(
      "api-global",
      new MemoryRateLimiter({
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: aiConfig.rateLimit.globalHourly,
        keyPrefix: "api-global",
      }),
    );

    // Share token rate limit (per IP address)
    this.limiters.set(
      "share-token",
      new MemoryRateLimiter({
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 10, // 10 views per minute per IP
        keyPrefix: "share-token",
      }),
    );
  }

  /**
   * Check rate limit for AI endpoints
   */
  async checkAILimit(userId: string, isPremium: boolean = false): Promise<RateLimitResult> {
    // Free users are not allowed to use AI at all
    if (!isPremium) {
      throw new RateLimiterError(
        "AI content generation is a premium feature. Upgrade to access unlimited AI assistance.",
        {
          allowed: false,
          remaining: 0,
          resetTime: Date.now() + 60 * 60 * 1000, // 1 hour from now
          totalHits: 0,
        },
      );
    }

    // Check burst limit first (applies to premium users)
    const burstResult = await this.limiters.get("ai-burst")!.hit(userId);
    if (!burstResult.allowed) {
      throw new RateLimiterError("Too many requests. Please wait a moment before trying again.", burstResult);
    }

    // Check premium tier limit
    const tierResult = await this.limiters.get("ai-premium")!.hit(userId);
    if (!tierResult.allowed) {
      throw new RateLimiterError("Premium rate limit exceeded. Please try again later.", tierResult);
    }

    return tierResult;
  }

  /**
   * Check global API rate limit
   */
  async checkGlobalLimit(userId: string): Promise<RateLimitResult> {
    const result = await this.limiters.get("api-global")!.hit(userId);
    if (!result.allowed) {
      throw new RateLimiterError("API rate limit exceeded. Please try again later.", result);
    }
    return result;
  }

  /**
   * Check share token rate limit (by IP address)
   */
  async checkShareTokenLimit(ipAddress: string): Promise<RateLimitResult> {
    const result = await this.limiters.get("share-token")!.hit(ipAddress);
    if (!result.allowed) {
      throw new RateLimiterError("Too many share token requests. Please try again in a minute.", result);
    }
    return result;
  }

  /**
   * Get rate limit status without incrementing counters
   */
  async getStatus(
    userId: string,
    isPremium: boolean = false,
  ): Promise<{
    burst: RateLimitResult;
    tier: RateLimitResult;
    global: RateLimitResult;
  }> {
    const tierLimiter = isPremium ? "ai-premium" : "ai-free";

    return {
      burst: await this.limiters.get("ai-burst")!.check(userId),
      tier: await this.limiters.get(tierLimiter)!.check(userId),
      global: await this.limiters.get("api-global")!.check(userId),
    };
  }

  /**
   * Reset rate limits for a user (admin function)
   */
  async resetUserLimits(userId: string): Promise<void> {
    for (const limiter of Array.from(this.limiters.values())) {
      await limiter.reset(userId);
    }
  }

  /**
   * Get overall statistics
   */
  getStats(): Record<string, { totalKeys: number; totalRequests: number }> {
    const stats: Record<string, { totalKeys: number; totalRequests: number }> = {};
    for (const [name, limiter] of Array.from(this.limiters.entries())) {
      stats[name] = limiter.getStats();
    }
    return stats;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    for (const limiter of Array.from(this.limiters.values())) {
      limiter.destroy();
    }
    this.limiters.clear();
  }
}

// Export singleton instance
export const rateLimitManager = new RateLimiterManager();

/**
 * Helper function to add rate limit headers to responses
 */
export function addRateLimitHeaders(headers: Headers, result: RateLimitResult, windowMs: number): void {
  headers.set("X-RateLimit-Limit", result.totalHits + result.remaining + "");
  headers.set("X-RateLimit-Remaining", result.remaining + "");
  headers.set("X-RateLimit-Reset", Math.ceil(result.resetTime / 1000) + "");
  headers.set("X-RateLimit-Window", Math.ceil(windowMs / 1000) + "");
}

/**
 * Rate limit configuration presets
 */
export const RATE_LIMIT_PRESETS = {
  AI_FREE_TIER: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
  },
  AI_PREMIUM_TIER: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 100,
  },
  AI_BURST_PROTECTION: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,
  },
  GLOBAL_API: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 1000,
  },
  SHARE_TOKEN: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 views per minute per IP
  },
} as const;
