import { Button } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Progress } from "@heroui/progress";
import { Icon } from "@iconify/react";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-hot-toast";
import { contactConfig } from "@/config/contact";
import { useFileUpload } from "@/hooks/use-file-upload";
import styles from "../styles/contact.module.css";

interface UploadedFile {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface FileUploadProps {
  attachments: UploadedFile[];
  onAttachmentsChange: (attachments: UploadedFile[]) => void;
  onMessage: (message: { type: string; content: string }) => void;
}

export default function FileUpload({ attachments, onAttachmentsChange, onMessage }: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false);

  const [uploadingFiles, setUploadingFiles] = useState<File[]>([]);

  const { uploadFilesWithProgress, isUploading, uploadProgress } = useFileUpload({
    endpoint: "contactAttachmentsUploader",
    onSuccess: (res) => {
      // Convert UploadThing response to our format using original file names
      const newFiles: UploadedFile[] = res.map((file, index) => {
        const originalFile = uploadingFiles[index];
        return {
          name: originalFile?.name || `attachment-${Date.now()}-${index}`,
          url: file.url,
          size: originalFile?.size || 0,
          type: originalFile?.type || "application/octet-stream",
        };
      });

      const updatedAttachments = [...attachments, ...newFiles];
      onAttachmentsChange(updatedAttachments);
      setUploadingFiles([]); // Clear uploading files
      toast.success("Files uploaded successfully");
    },
    onError: (error) => {
      console.error("Upload error:", error);
      onMessage({ type: "error", content: "Failed to upload files. Please try again." });
    },
  });

  const validateFiles = useCallback(
    (files: File[]) => {
      const validFiles: File[] = [];
      const errors: string[] = [];

      // Check total file count
      if (attachments.length + files.length > contactConfig.validation.maxFiles) {
        errors.push(`Too many files. Maximum ${contactConfig.validation.maxFiles} files allowed.`);
        return { validFiles: [], errors };
      }

      files.forEach((file) => {
        // Check file size
        if (file.size > contactConfig.validation.maxFileSize) {
          errors.push(
            `File "${file.name}" is too large. Maximum size is ${Math.round(contactConfig.validation.maxFileSize / (1024 * 1024))}MB.`,
          );
          return;
        }

        // Check file type
        if (!contactConfig.validation.allowedFileTypes.includes(file.type)) {
          errors.push(`File "${file.name}" has an invalid file type.`);
          return;
        }

        validFiles.push(file);
      });

      return { validFiles, errors };
    },
    [attachments.length],
  );

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const { validFiles, errors } = validateFiles(acceptedFiles);

      if (errors.length > 0) {
        errors.forEach((error) => {
          onMessage({ type: "error", content: error });
        });
        return;
      }

      if (validFiles.length > 0) {
        setUploadingFiles(validFiles); // Store files for success callback
        await uploadFilesWithProgress(validFiles);
      }
    },
    [validateFiles, uploadFilesWithProgress, onMessage],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif", ".webp"],
      "application/pdf": [".pdf"],
      "text/plain": [".txt"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
    },
    disabled: isUploading,
    multiple: true,
  });

  const handleFileInput = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      await onDrop(files);
    }
    // Clear input
    event.target.value = "";
  };

  const removeAttachment = async (index: number) => {
    const fileToRemove = attachments[index];

    try {
      // Delete from UploadThing
      const response = await fetch("/api/uploadthing/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ fileUrl: fileToRemove.url }),
      });

      if (response.ok) {
        const updatedAttachments = attachments.filter((_, i) => i !== index);
        onAttachmentsChange(updatedAttachments);
        toast.success("File removed successfully");
      } else {
        throw new Error("Failed to delete file");
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      onMessage({ type: "error", content: "Failed to delete file. Please try again." });
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Icon icon="tabler:paperclip" className="w-5 h-5 text-orange-500" />
        Attachments
        <Chip size="sm" variant="flat">
          Optional
        </Chip>
      </h3>

      {/* Upload Zone with Drag & Drop */}
      <div
        {...getRootProps()}
        className={`${styles.attachmentZone} border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
          isDragActive || dragActive
            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : isUploading
              ? "border-orange-400 bg-orange-50 dark:bg-orange-900/20"
              : "border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500"
        } ${isUploading ? "pointer-events-none" : ""}`}
      >
        <input {...getInputProps()} />

        {isUploading ? (
          <div className="space-y-3">
            <Icon icon="tabler:upload" className="w-8 h-8 text-orange-500 mx-auto animate-pulse" />
            <div className="space-y-2">
              <p className="text-sm font-medium text-orange-700 dark:text-orange-300">Uploading files...</p>
              <Progress value={uploadProgress} className="max-w-xs mx-auto" size="sm" color="primary" showValueLabel />
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <Icon
              icon={isDragActive ? "tabler:cloud-upload" : "tabler:cloud-upload"}
              className={`w-8 h-8 mx-auto ${isDragActive ? "text-blue-500" : "text-gray-400"}`}
            />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                {isDragActive ? "Drop files here" : "Drag & drop files here, or click to select"}
              </p>
              <p className="text-xs text-gray-500">PDF, DOC, DOCX, TXT, Images • Max 8MB per file</p>
            </div>
          </div>
        )}
      </div>

      {/* Alternative file input button */}
      <div className="text-center">
        <Button
          variant="bordered"
          size="sm"
          startContent={<Icon icon="tabler:plus" className="w-4 h-4" />}
          onPress={() => document.getElementById("file-upload-input")?.click()}
          isDisabled={isUploading || attachments.length >= contactConfig.validation.maxFiles}
        >
          Select Files
        </Button>
        <input
          id="file-upload-input"
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx,.txt"
          onChange={handleFileInput}
          className="hidden"
        />
      </div>

      {/* Attachment List */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Attached Files ({attachments.length}/{contactConfig.validation.maxFiles})
            </p>
            {attachments.length > 0 && <p className="text-xs text-gray-500">Files uploaded successfully</p>}
          </div>

          {attachments.map((file, index) => {
            const fileExtension = file.url.split(".").pop()?.toLowerCase() || "";
            const getFileIcon = (extension: string) => {
              if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension)) return "tabler:photo";
              if (extension === "pdf") return "tabler:file-type-pdf";
              if (["doc", "docx"].includes(extension)) return "tabler:file-type-doc";
              if (extension === "txt") return "tabler:file-type-txt";
              return "tabler:file";
            };

            return (
              <div
                key={index}
                className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Icon icon={getFileIcon(fileExtension)} className="w-5 h-5 text-blue-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate text-gray-900 dark:text-gray-100">{file.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Chip size="sm" variant="flat" color="success">
                          Uploaded
                        </Chip>
                        {file.size > 0 && (
                          <span className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="light"
                    color="danger"
                    isIconOnly
                    onPress={() => removeAttachment(index)}
                    className="flex-shrink-0"
                  >
                    <Icon icon="tabler:x" className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* File count warning */}
      {attachments.length >= contactConfig.validation.maxFiles && (
        <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
          <div className="flex items-start gap-2">
            <Icon
              icon="tabler:alert-triangle"
              className="w-4 h-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0"
            />
            <p className="text-sm text-orange-800 dark:text-orange-200">
              Maximum number of files reached ({contactConfig.validation.maxFiles} files). Remove a file to add another.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
