"use client";

import React from "react";
import { getAvailableWebsiteTemplates, getWebsiteTemplate } from "./templates/template-registry";

interface WebsitePreviewProps {
  resume: any;
  website: any;
  templateSlug: string;
  className?: string;
  style?: React.CSSProperties;
}

export const WebsitePreview: React.FC<WebsitePreviewProps> = ({
  resume,
  website,
  templateSlug,
  className = "",
  style,
}) => {
  const TemplateComponent = getWebsiteTemplate(templateSlug);

  if (!TemplateComponent) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 ${className}`} style={style} dir="ltr">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Template Not Found</h3>
          <p className="text-gray-600">The selected template "{templateSlug}" could not be loaded.</p>
          <p className="text-sm text-gray-500 mt-2">Available templates: {getAvailableWebsiteTemplates().join(", ")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`website-preview ${className}`} style={style} dir="ltr">
      <TemplateComponent resume={resume} website={website} />
    </div>
  );
};
