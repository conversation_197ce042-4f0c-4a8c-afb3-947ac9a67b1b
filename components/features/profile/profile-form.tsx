"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Di<PERSON>r, Input, Textarea } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { Autosave } from "react-autosave";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";

export function ProfileForm() {
  // Fetch profile data
  const { data: profile, isLoading } = trpc.userProfile.get.useQuery();

  // Update mutation
  const updateMutation = trpc.userProfile.update.useMutation({
    onSuccess: () => {
      toast.success("Profile updated successfully");
    },
    onError: (error) => {
      toast.error("Failed to update profile");
      console.error("Failed to update profile:", error);
    },
  });

  // Form state
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    jobTitle: "",
    phone: "",
    website: "",
    bio: "",
    address: "",
    street: "",
    city: "",
    country: "",
  });

  // Load profile data when available
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        jobTitle: profile.jobTitle || "",
        phone: profile.phone || "",
        website: profile.website || "",
        bio: profile.bio || "",
        address: profile.address || "",
        street: profile.street || "",
        city: profile.city || "",
        country: profile.country || "",
      });
    }
  }, [profile]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center min-h-[400px]">
          <Icon icon="tabler:loader-2" className="w-8 h-8 animate-spin" />
          <span className="ml-2">Loading...</span>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <Autosave interval={1000} data={formData} onSave={() => updateMutation.mutate(formData)} />
      <CardHeader className="flex gap-3">
        <Icon icon="tabler:user-edit" className="w-6 h-6" />
        <div className="flex flex-col">
          <p className="text-lg font-semibold">Edit Personal Information</p>
          <p className="text-small text-default-500">Update your personal details and contact information</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-md font-semibold flex items-center gap-2">
            <Icon icon="tabler:user" className="w-5 h-5" />
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="First Name"
              placeholder="Enter your first name"
              value={formData.firstName}
              onValueChange={(value) => handleInputChange("firstName", value)}
              startContent={<Icon icon="tabler:user" className="w-4 h-4 text-default-400" />}
            />
            <Input
              label="Last Name"
              placeholder="Enter your last name"
              value={formData.lastName}
              onValueChange={(value) => handleInputChange("lastName", value)}
              startContent={<Icon icon="tabler:user" className="w-4 h-4 text-default-400" />}
            />
            <Input
              label="Job Title"
              placeholder="Enter your job title"
              value={formData.jobTitle}
              onValueChange={(value) => handleInputChange("jobTitle", value)}
              startContent={<Icon icon="tabler:briefcase" className="w-4 h-4 text-default-400" />}
              className="md:col-span-2"
            />
          </div>
        </div>

        <Divider />

        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className="text-md font-semibold flex items-center gap-2">
            <Icon icon="tabler:phone" className="w-5 h-5" />
            Contact Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Phone"
              placeholder="Enter your phone number"
              value={formData.phone}
              onValueChange={(value) => handleInputChange("phone", value)}
              startContent={<Icon icon="tabler:phone" className="w-4 h-4 text-default-400" />}
              type="tel"
            />
            <Input
              label="Website"
              placeholder="Enter your website URL"
              value={formData.website}
              onValueChange={(value) => handleInputChange("website", value)}
              startContent={<Icon icon="tabler:world" className="w-4 h-4 text-default-400" />}
              type="url"
            />
          </div>
        </div>

        <Divider />

        {/* Location Information */}
        <div className="space-y-4">
          <h3 className="text-md font-semibold flex items-center gap-2">
            <Icon icon="tabler:map-pin" className="w-5 h-5" />
            Location Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Address"
              placeholder="Enter your address"
              value={formData.address}
              onValueChange={(value) => handleInputChange("address", value)}
              startContent={<Icon icon="tabler:home" className="w-4 h-4 text-default-400" />}
              className="md:col-span-2"
            />
            <Input
              label="Street"
              placeholder="Enter your street"
              value={formData.street}
              onValueChange={(value) => handleInputChange("street", value)}
              startContent={<Icon icon="tabler:road" className="w-4 h-4 text-default-400" />}
            />
            <Input
              label="City"
              placeholder="Enter your city"
              value={formData.city}
              onValueChange={(value) => handleInputChange("city", value)}
              startContent={<Icon icon="tabler:building" className="w-4 h-4 text-default-400" />}
            />
            <Input
              label="Country"
              placeholder="Enter your country"
              value={formData.country}
              onValueChange={(value) => handleInputChange("country", value)}
              startContent={<Icon icon="tabler:flag" className="w-4 h-4 text-default-400" />}
              className="md:col-span-2"
            />
          </div>
        </div>

        <Divider />

        {/* Bio */}
        <div className="space-y-4">
          <h3 className="text-md font-semibold flex items-center gap-2">
            <Icon icon="tabler:file-text" className="w-5 h-5" />
            Bio
          </h3>
          <Textarea
            label="Bio"
            placeholder="Tell us about yourself"
            value={formData.bio}
            onValueChange={(value) => handleInputChange("bio", value)}
            minRows={4}
            maxRows={8}
          />
        </div>

        {/* Auto-save indicator */}
        {updateMutation.isPending && (
          <div className="flex items-center gap-2 text-sm text-default-500">
            <Icon icon="tabler:loader-2" className="w-4 h-4 animate-spin" />
            Saving...
          </div>
        )}
      </CardBody>
    </Card>
  );
}
