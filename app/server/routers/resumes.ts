import { TRPCError } from "@trpc/server";
import { and, desc, eq, inArray } from "drizzle-orm";
import { cookies } from "next/headers";
import { z } from "zod";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  hobbies,
  languages,
  profiles,
  projects,
  references,
  resumes,
  skills,
  users,
  volunteerings,
} from "@/db/schema";
import { LinkedInService, parseLinkedInToResume } from "@/lib/linkedin-service";
import { getFullResumeQuery, getResumeQuery } from "@/lib/resume-queries";
import { protectedProcedure, router } from "../trpc";

// const caller = appRouter.createCaller({});
export const resumesRouter = router({
  createResume: protectedProcedure
    .input(
      z.object({
        title: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const title = input.title || "New Resume";
      const templateId = 1;

      const [user] = await db
        .select({
          planId: users.planId,
        })
        .from(users)
        .where(eq(users.clerkId, ctx.user.clerkId))
        .limit(1);

      if (user.planId === "free") {
        const resumeCount = await db
          .select()
          .from(resumes)
          .where(eq(resumes.userId, ctx.user.clerkId));

        if (resumeCount.length >= 1) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Free plan users can only create one resume. Please upgrade to create more.",
          });
        }
      }

      // Create the main resume with user profile data
      const [resume] = await db
        .insert(resumes)
        .values({
          title,
          userId: ctx.user.clerkId,
          templateId,
          // Prefill with user profile data if available
          firstName: userProfile[0]?.firstName || "",
          lastName: userProfile[0]?.lastName || "",
          jobTitle: userProfile[0]?.jobTitle || "",
          phone: userProfile[0]?.phone || "",
          email: userProfile[0]?.email || "",
          website: userProfile[0]?.website || "",
          bio: userProfile[0]?.bio || "",
          address: userProfile[0]?.address || "",
          street: userProfile[0]?.street || "",
          city: userProfile[0]?.city || "",
          country: userProfile[0]?.country || "",
        })
        .returning();

      // Create empty nested resource records
      await Promise.all([
        db.insert(educations).values({ resumeId: resume.id }),
        db.insert(experiences).values({ resumeId: resume.id }),
        db.insert(projects).values({ resumeId: resume.id }),
        db.insert(awards).values({ resumeId: resume.id }),
        db.insert(certifications).values({ resumeId: resume.id }),
        db.insert(skills).values({ resumeId: resume.id }),
        db.insert(languages).values({ resumeId: resume.id }),
        db.insert(references).values({ resumeId: resume.id }),
        db.insert(hobbies).values({ resumeId: resume.id }),
        db.insert(volunteerings).values({ resumeId: resume.id }),
        db.insert(profiles).values({ resumeId: resume.id }),
      ]);

      return {
        success: true,
        resumeId: resume.id,
      };
    }),

  getResumes: protectedProcedure.query(async ({ ctx }) => {
    return await db.select().from(resumes).where(eq(resumes.userId, ctx.user.clerkId)).orderBy(desc(resumes.updatedAt));
  }),

  getFullResume: protectedProcedure.input(z.object({ id: z.number() })).query(async ({ input, ctx }) => {
    const resumeData = await getFullResumeQuery(input.id, ctx.user.clerkId);

    if (!resumeData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }

    return resumeData;
  }),

  getResume: protectedProcedure.input(z.object({ id: z.number() })).query(async ({ input, ctx }) => {
    const resumeData = await getResumeQuery(input.id, ctx.user.clerkId);
    if (!resumeData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }

    return resumeData;
  }),

  deleteResume: protectedProcedure.input(z.object({ id: z.number() })).mutation(async ({ input, ctx }) => {
    const result = await db
      .delete(resumes)
      .where(and(eq(resumes.id, input.id), eq(resumes.userId, ctx.user.clerkId)))
      .returning();

    if (result.length === 0) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }

    return { success: true };
  }),

  renameResume: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        title: z.string().min(1, "Title cannot be empty").max(100, "Title too long"),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const result = await db
        .update(resumes)
        .set({
          title: input.title,
          updatedAt: new Date().toISOString(),
        })
        .where(and(eq(resumes.id, input.id), eq(resumes.userId, ctx.user.clerkId)))
        .returning();

      if (result.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Resume not found or unauthorized",
        });
      }

      return { success: true, resume: result[0] };
    }),

  duplicateResume: protectedProcedure.input(z.object({ id: z.number() })).mutation(async ({ input, ctx }) => {
    const originalResume = await getFullResumeQuery(input.id, ctx.user.clerkId);

    if (!originalResume) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }
    const { id, ...resumeWithoutId } = originalResume;

    // Create the new resume with " copy" appended to the title
    const newResumeData = {
      ...resumeWithoutId,
      title: `${originalResume.title} copy`,
      userId: ctx.user.clerkId,
    };

    // Create the new resume
    const [newResume] = await db.insert(resumes).values(newResumeData).returning();

    // Helper function to duplicate nested relations
    const duplicateRelations = async (items: any[], table: any) => {
      if (items && items.length > 0) {
        const preparedItems = items.map((item) => {
          const { id, ...itemWithoutId } = item;
          return { ...itemWithoutId, resumeId: newResume.id };
        });
        await db.insert(table).values(preparedItems);
      }
    };

    // Duplicate all nested relations
    await Promise.all([
      duplicateRelations(originalResume.educations, educations),
      duplicateRelations(originalResume.experiences, experiences),
      duplicateRelations(originalResume.projects, projects),
      duplicateRelations(originalResume.awards, awards),
      duplicateRelations(originalResume.certifications, certifications),
      duplicateRelations(originalResume.skills, skills),
      duplicateRelations(originalResume.languages, languages),
      duplicateRelations(originalResume.references, references),
      duplicateRelations(originalResume.hobbies, hobbies),
      duplicateRelations(originalResume.volunteerings, volunteerings),
      duplicateRelations(originalResume.profiles, profiles),
    ]);

    return { success: true, resumeId: newResume.id };
  }),

  updateResume: protectedProcedure.input(z.any()).mutation(async ({ input, ctx }) => {
    const {
      id,
      educations: educationsData,
      experiences: experiencesData,
      projects: projectsData,
      awards: awardsData,
      certifications: certificationsData,
      skills: skillsData,
      languages: languagesData,
      references: referencesData,
      hobbies: hobbiesData,
      volunteerings: volunteeringsData,
      profiles: profilesData,
      ...resumeUpdateData
    } = input;

    // Verify the resume belongs to the user
    const existingResume = await db
      .select()
      .from(resumes)
      .where(and(eq(resumes.id, id), eq(resumes.userId, ctx.user.clerkId)))
      .limit(1);

    if (existingResume.length === 0) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found or unauthorized",
      });
    }

    // Update the main resume record if there are changes
    if (Object.keys(resumeUpdateData).length > 0) {
      await db.update(resumes).set(resumeUpdateData).where(eq(resumes.id, id));
    }

    // Helper to sync a collection using direct updates/inserts/deletes
    const syncCollection = async (data: any[] | undefined, table: any, resumeId: number) => {
      if (data === undefined) {
        return; // If no data is provided for a collection, do nothing.
      }

      // Get existing items from the DB for the current resume
      const dbItems = await db.select({ id: table.id }).from(table).where(eq(table.resumeId, resumeId));
      const dbIds = dbItems.map((item) => item.id);
      const clientIds = data.map((item) => item.id).filter(Boolean);

      // 1. Delete items that are in the DB but not in the client's payload
      const idsToDelete = dbIds.filter((id) => !clientIds.includes(id));
      if (idsToDelete.length > 0) {
        await db.delete(table).where(inArray(table.id, idsToDelete));
      }

      const itemsToUpdate = data.filter((item) => item.id);
      const itemsToInsert = data.filter((item) => !item.id);

      // 2. Update existing items
      if (itemsToUpdate.length > 0) {
        await Promise.all(
          itemsToUpdate.map((item) => {
            const { id, ...itemWithoutId } = item;
            return db
              .update(table)
              .set({ ...itemWithoutId, resumeId })
              .where(eq(table.id, id));
          }),
        );
      }

      // 3. Insert new items
      if (itemsToInsert.length > 0) {
        const preparedItems = itemsToInsert.map((item) => {
          return { ...item, resumeId };
        });
        await db.insert(table).values(preparedItems);
      }
    };

    // Update all nested collections
    await Promise.all([
      syncCollection(educationsData, educations, id),
      syncCollection(experiencesData, experiences, id),
      syncCollection(projectsData, projects, id),
      syncCollection(awardsData, awards, id),
      syncCollection(certificationsData, certifications, id),
      syncCollection(skillsData, skills, id),
      syncCollection(languagesData, languages, id),
      syncCollection(referencesData, references, id),
      syncCollection(hobbiesData, hobbies, id),
      syncCollection(volunteeringsData, volunteerings, id),
      syncCollection(profilesData, profiles, id),
    ]);

    return {
      success: true,
    };
  }),

  importFromLinkedIn: protectedProcedure.mutation(async ({ ctx }) => {
    // Get LinkedIn access token from cookies
    const cookieStore = await cookies();
    const linkedInToken = cookieStore.get("linkedin_token")?.value;

    if (!linkedInToken) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "LinkedIn not connected. Please authorize LinkedIn access first.",
      });
    }

    try {
      // Fetch LinkedIn profile data
      const linkedInService = new LinkedInService(linkedInToken);
      const linkedInProfile = await linkedInService.getProfile();

      // Parse LinkedIn data to resume format
      const resumeData = parseLinkedInToResume(linkedInProfile);

      // Create new resume with LinkedIn data
      const title = `${resumeData.firstName || "LinkedIn"} Resume`;
      const [newResume] = await db
        .insert(resumes)
        .values({
          title,
          userId: ctx.user.clerkId,
          templateId: 1,
          firstName: resumeData.firstName || "",
          lastName: resumeData.lastName || "",
          jobTitle: resumeData.jobTitle || "",
          bio: resumeData.bio || "",
          email: resumeData.email || "",
          city: resumeData.city || "",
        })
        .returning();

      const targetResumeId = newResume.id;

      // Create empty nested resource records for sections not imported
      await Promise.all([
        db.insert(projects).values({ resumeId: targetResumeId }),
        db.insert(awards).values({ resumeId: targetResumeId }),
        db.insert(certifications).values({ resumeId: targetResumeId }),
        db.insert(languages).values({ resumeId: targetResumeId }),
        db.insert(references).values({ resumeId: targetResumeId }),
        db.insert(hobbies).values({ resumeId: targetResumeId }),
        db.insert(volunteerings).values({ resumeId: targetResumeId }),
        db.insert(profiles).values({ resumeId: targetResumeId }),
      ]);

      // Import experiences
      if (resumeData.experiences && resumeData.experiences.length > 0) {
        const experienceData = resumeData.experiences.map((exp) => ({
          ...exp,
          resumeId: targetResumeId,
        }));
        await db.insert(experiences).values(experienceData);
      }

      // Import education
      if (resumeData.educations && resumeData.educations.length > 0) {
        const educationData = resumeData.educations.map((edu) => ({
          ...edu,
          resumeId: targetResumeId,
        }));
        await db.insert(educations).values(educationData);
      }

      // Import skills
      if (resumeData.skills && resumeData.skills.length > 0) {
        const skillsData = resumeData.skills.map((skill) => ({
          ...skill,
          resumeId: targetResumeId,
        }));
        await db.insert(skills).values(skillsData);
      }

      return {
        success: true,
        resumeId: targetResumeId,
        importedSections: {
          personalInfo: !!(
            resumeData.firstName ||
            resumeData.lastName ||
            resumeData.jobTitle ||
            resumeData.bio ||
            resumeData.email
          ),
          experiences: resumeData.experiences?.length || 0,
          educations: resumeData.educations?.length || 0,
          skills: resumeData.skills?.length || 0,
        },
      };
    } catch (error) {
      console.error("LinkedIn import error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to import LinkedIn profile data",
      });
    }
  }),
});

export type ResumesRouter = typeof resumesRouter;
