import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  ContactInfo,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
  getSectionTranslations,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom main content section for glalie template
const GlalieSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="resume-section mb-6">
    <h2 className="section-title text-lg font-bold mb-3 text-gray-900 border-b border-gray-400 pb-1">{title}</h2>
    {children}
  </section>
);

// Custom sidebar section for glalie template
const GlalieSidebarSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="sidebar-section mb-6">
    <h3 className="section-title text-lg font-bold mb-3 text-gray-900 border-b border-teal-400 pb-1">{title}</h3>
    {children}
  </section>
);

/**
 * Glalie Resume Template - Two-Column Layout with Teal Sidebar
 * - Teal/blue sidebar on left with photo and secondary info
 * - White main content area on right for primary sections
 * - Clean typography and professional appearance
 * - Professional color scheme matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
const GlalieTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`glalie-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white">
        {/* Two-Column Layout - Full Height */}
        <div className="flex min-h-[297mm]">
          {/* Left Sidebar - Teal Background */}
          <aside className="sidebar-content w-1/3 bg-teal-100 p-6">
            {/* Photo */}
            {resume.showPhoto && resume.photo ? (
              <div className="mb-6">
                <Image
                  alt={fullName}
                  className="w-32 h-40 object-cover mx-auto"
                  height={160}
                  src={resume.photo}
                  width={128}
                />
              </div>
            ) : null}

            {/* Name and Title */}
            <div className="text-center mb-6">
              <h1 className="text-xl font-bold text-gray-900 mb-1">{fullName}</h1>
              <p className="text-sm text-gray-700">{resume.jobTitle}</p>
            </div>

            {/* Contact Information Box */}
            <div className="contact-section bg-teal-200 border border-teal-300 rounded p-4 mb-6">
              <ContactInfo
                className="space-y-2 text-sm text-gray-700 [&_span:first-child]:text-teal-600"
                email={resume.email}
                location={location}
                phone={resume.phone}
                variant="vertical"
                website={resume.website}
              />
            </div>

            {/* Profiles */}
            {resume.profiles && resume.profiles.length > 0 && (
              <GlalieSidebarSection title={sectionTitles.profiles}>
                <SocialProfile
                  className="space-y-2"
                  layout="vertical"
                  profiles={resume.profiles}
                  showNetworkLabel={true}
                />
              </GlalieSidebarSection>
            )}

            {/* Skills */}
            {resume.skills && resume.skills.length > 0 && (
              <GlalieSidebarSection title={sectionTitles.skills}>
                <SkillsSection skills={resume.skills} layout="list" />
              </GlalieSidebarSection>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <GlalieSidebarSection title={sectionTitles.languages}>
                <div className="space-y-3">
                  {resume.languages.map((language) => (
                    <LanguageItem
                      key={language.id}
                      className="text-sm"
                      language={language}
                      showBars={false}
                      showLevel={true}
                    />
                  ))}
                </div>
              </GlalieSidebarSection>
            )}

            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <GlalieSidebarSection title={sectionTitles.certifications}>
                <div className="space-y-3">
                  {resume.certifications.map((cert) => (
                    <CertificationItem
                      key={cert.id}
                      certification={cert}
                      className="text-sm"
                      showCredentialId={false}
                    />
                  ))}
                </div>
              </GlalieSidebarSection>
            )}

            {/* Hobbies */}
            {resume.hobbies && resume.hobbies.length > 0 && (
              <GlalieSidebarSection title={sectionTitles.hobbies}>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {resume.hobbies.map((hobby) => hobby.name).join(", ")}
                </p>
              </GlalieSidebarSection>
            )}
          </aside>

          {/* Right Column - Main Content */}
          <main className="w-2/3 p-6">
            {/* Summary */}
            {resume.bio && (
              <GlalieSection title={sectionTitles.summary}>
                <ProfessionalSummary bio={resume.bio} variant="paragraph" />
              </GlalieSection>
            )}

            {/* Experience */}
            {resume.experiences && resume.experiences.length > 0 && (
              <GlalieSection title={sectionTitles.experience}>
                <div className="space-y-6">
                  {resume.experiences.map((exp) => (
                    <ExperienceItem
                      key={exp.id}
                      experience={exp}
                      locale={locale}
                      showWebsiteIcon={true}
                      variant="standard"
                    />
                  ))}
                </div>
              </GlalieSection>
            )}

            {/* Education */}
            {resume.educations && resume.educations.length > 0 && (
              <GlalieSection title={sectionTitles.education}>
                <div className="space-y-4">
                  {resume.educations.map((edu) => (
                    <EducationItem key={edu.id} education={edu} locale={locale} variant="standard" />
                  ))}
                </div>
              </GlalieSection>
            )}

            {/* Projects */}
            {resume.projects && resume.projects.length > 0 && (
              <GlalieSection title={sectionTitles.projects}>
                <div className="space-y-4">
                  {resume.projects.map((project) => (
                    <ProjectItem
                      key={project.id}
                      project={project}
                      showClient={true}
                      showTechnologies={false}
                      variant="standard"
                    />
                  ))}
                </div>
              </GlalieSection>
            )}

            {/* Awards */}
            {resume.awards && resume.awards.length > 0 && (
              <GlalieSection title={sectionTitles.awards}>
                <div className="space-y-3">
                  {resume.awards.map((award) => (
                    <div key={award.id} className="award-item flex justify-between items-start">
                      <div>
                        <h4 className="font-bold text-gray-900 text-sm">{award.title}</h4>
                        <p className="text-gray-700 text-sm">{award.issuer}</p>
                        {award.description && (
                          <div
                            dangerouslySetInnerHTML={{ __html: award.description }}
                            className="text-gray-700 text-sm mt-1"
                          />
                        )}
                      </div>
                      {award.dateReceived && (
                        <span className="text-gray-600 text-sm">{formatDate(award.dateReceived, false, locale)}</span>
                      )}
                    </div>
                  ))}
                </div>
              </GlalieSection>
            )}

            {/* Volunteering */}
            {resume.volunteerings && resume.volunteerings.length > 0 && (
              <GlalieSection title={sectionTitles.volunteering}>
                <div className="space-y-4">
                  {resume.volunteerings.map((volunteering) => (
                    <div key={volunteering.id} className="volunteering-item">
                      <div className="flex justify-between items-start mb-1">
                        <div>
                          <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
                          <p className="text-gray-700 text-sm">{volunteering.organization}</p>
                        </div>
                        {(volunteering.startDate || volunteering.endDate) && (
                          <span className="text-gray-600 text-sm">
                            {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                          </span>
                        )}
                      </div>
                      {volunteering.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: volunteering.description,
                          }}
                          className="text-gray-700 text-sm mt-2"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </GlalieSection>
            )}

            {/* References */}
            {resume.references && resume.references.length > 0 ? (
              <GlalieSection title={sectionTitles.references}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {resume.references.map((reference) => (
                    <div key={reference.id} className="reference-item">
                      <h4 className="font-bold text-gray-900">{reference.name}</h4>
                      <p className="text-gray-700 text-sm">
                        {reference.position} at {reference.company}
                      </p>
                      <div className="text-gray-600 text-sm mt-1">
                        {reference.email && (
                          <p className="flex items-center gap-1">
                            <span>✉️</span>
                            <span>{reference.email}</span>
                          </p>
                        )}
                        {reference.phone && (
                          <p className="flex items-center gap-1">
                            <span>📞</span>
                            <span>{reference.phone}</span>
                          </p>
                        )}
                      </div>
                      {reference.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: reference.description,
                          }}
                          className="text-gray-700 text-sm mt-2"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </GlalieSection>
            ) : (
              <GlalieSection title={sectionTitles.references}>
                <p className="text-gray-700 text-sm">Available upon request</p>
              </GlalieSection>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default GlalieTemplate;
