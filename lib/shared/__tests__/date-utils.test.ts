import { 
  formatDate, 
  formatDateRange, 
  calculateDuration,
  createSemanticTimeElement,
  createSemanticDateRange 
} from '../date-utils';

describe('Enhanced Date Utils', () => {
  describe('formatDate', () => {
    test('should format date in short format by default', () => {
      const result = formatDate('2023-06-15', 'short', 'en-US');
      expect(result).toContain('2023');
    });

    test('should format date with year only', () => {
      const result = formatDate('2023-06-15', 'year-only', 'en-US');
      expect(result).toBe('2023');
    });

    test('should handle Present case', () => {
      const result = formatDate(null, 'short', 'en-US', { showPresent: true });
      expect(result).toBe('Present');
    });

    test('should return empty string for invalid date', () => {
      const result = formatDate('invalid-date', 'short', 'en-US');
      expect(result).toBe('');
    });
  });

  describe('formatDateRange', () => {
    test('should format complete date range', () => {
      const result = formatDateRange('2020-01-01', '2023-06-15', false, 'en-US');
      expect(result).toContain('2020');
      expect(result).toContain('2023');
    });

    test('should handle current position', () => {
      const result = formatDateRange('2020-01-01', null, true, 'en-US');
      expect(result).toContain('Present');
    });

    test('should handle single start date', () => {
      const result = formatDateRange('2020-01-01', null, false, 'en-US');
      expect(result).toContain('2020');
    });

    test('should return empty string for no dates', () => {
      const result = formatDateRange(null, null, false, 'en-US');
      expect(result).toBe('');
    });
  });
});