# Setup & Configuration

This directory contains all setup and configuration documentation for QuickCV.

## Files

- **[ENV_SETUP.md](ENV_SETUP.md)** - Environment variables and application configuration
- **[browserless-setup.md](browserless-setup.md)** - PDF generation service setup guide
- **[clerk-webhook-setup.md](clerk-webhook-setup.md)** - Authentication webhook configuration
- **[generating-secret-tokens.md](generating-secret-tokens.md)** - Security token generation reference

## Setup Order

1. Start with [Environment Setup](ENV_SETUP.md) for basic configuration
2. Configure [Clerk Webhooks](clerk-webhook-setup.md) for authentication
3. Set up [Browserless](browserless-setup.md) for PDF generation
4. Use [Secret Tokens Guide](generating-secret-tokens.md) for security tokens

## Quick Setup

```bash
# Copy environment template
cp .env.example .env.local

# Set up database
bun run db:migrate

# Start development
bun run dev
```

For detailed instructions, see each individual setup file.