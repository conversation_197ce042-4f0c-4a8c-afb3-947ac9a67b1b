import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { resumes } from "@/db/schema";
import { EMAIL_TEMPLATES, type EmailVariables, renderEmailTemplate } from "@/lib/email-templates";
import { ShareTokenService } from "@/lib/share-token";
import { SocialMediaSharing, type SocialShareData } from "@/lib/social-sharing";
import { protectedProcedure, publicProcedure, router } from "../trpc";

export const sharingRouter = router({
  // Get available email templates
  getEmailTemplates: protectedProcedure.query(async () => {
    return EMAIL_TEMPLATES;
  }),

  // Generate email content for sharing
  generateEmailContent: protectedProcedure
    .input(
      z.object({
        resumeId: z.number(),
        templateId: z.string(),
        variables: z
          .object({
            recipientName: z.string().optional(),
            position: z.string().optional(),
            companyName: z.string().optional(),
            personalPitch: z.string().optional(),
            mutualConnection: z.string().optional(),
            industry: z.string().optional(),
            meetingContext: z.string().optional(),
            followUpNote: z.string().optional(),
            personalNote: z.string().optional(),
            yearsOfExperience: z.string().optional(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Verify resume belongs to user
      const resume = await db
        .select({
          id: resumes.id,
          firstName: resumes.firstName,
          lastName: resumes.lastName,
          jobTitle: resumes.jobTitle,
          email: resumes.email,
          phone: resumes.phone,
        })
        .from(resumes)
        .where(eq(resumes.id, input.resumeId))
        .limit(1);

      if (!resume[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Resume not found",
        });
      }

      const resumeData = resume[0];

      // Find the email template
      const template = EMAIL_TEMPLATES.find((t) => t.id === input.templateId);
      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Email template not found",
        });
      }

      // Prepare variables for template rendering
      const emailVariables: EmailVariables = {
        fullName: `${resumeData.firstName} ${resumeData.lastName}`.trim() || "User",
        firstName: resumeData.firstName || "",
        lastName: resumeData.lastName || "",
        jobTitle: resumeData.jobTitle || "",
        email: resumeData.email || "",
        phone: resumeData.phone || "",
        ...input.variables,
      };

      // Render the email template
      const renderedEmail = renderEmailTemplate(template, emailVariables);

      // Generate token-based shareable resume URL
      const resumeUrl = await ShareTokenService.generateShareableUrl(
        input.resumeId,
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      );

      return {
        subject: renderedEmail.subject,
        body: renderedEmail.body,
        resumeUrl,
        template: template,
      };
    }),

  // Generate social media sharing URLs
  generateSocialShareUrls: protectedProcedure
    .input(
      z.object({
        resumeId: z.number(),
        customMessage: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Verify resume belongs to user
      const resume = await db
        .select({
          id: resumes.id,
          firstName: resumes.firstName,
          lastName: resumes.lastName,
          jobTitle: resumes.jobTitle,
          website: resumes.website,
        })
        .from(resumes)
        .where(eq(resumes.id, input.resumeId))
        .limit(1);

      if (!resume[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Resume not found",
        });
      }

      const resumeData = resume[0];

      // Generate token-based shareable resume URL
      const resumeUrl = await ShareTokenService.generateShareableUrl(
        input.resumeId,
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      );

      // Prepare social share data
      const shareData: SocialShareData = {
        fullName: `${resumeData.firstName} ${resumeData.lastName}`.trim() || "User",
        jobTitle: resumeData.jobTitle || "Professional",
        resumeUrl,
        websiteUrl: resumeData.website || undefined,
        customMessage: input.customMessage,
      };

      // Generate sharing URLs for different platforms
      return {
        linkedin: SocialMediaSharing.generateLinkedInShareUrl(shareData),
        twitter: SocialMediaSharing.generateTwitterShareUrl(shareData),
        facebook: SocialMediaSharing.generateFacebookShareUrl(shareData),
        whatsapp: SocialMediaSharing.generateWhatsAppShareUrl(shareData),
        email: SocialMediaSharing.generateEmailShareUrl(shareData),
        resumeUrl,
      };
    }),

  // Get resume basic info for sharing
  getResumeForSharing: protectedProcedure.input(z.object({ resumeId: z.number() })).query(async ({ input, ctx }) => {
    const resume = await db
      .select({
        id: resumes.id,
        title: resumes.title,
        firstName: resumes.firstName,
        lastName: resumes.lastName,
        jobTitle: resumes.jobTitle,
        email: resumes.email,
        phone: resumes.phone,
        website: resumes.website,
      })
      .from(resumes)
      .where(eq(resumes.id, input.resumeId))
      .limit(1);

    if (!resume[0]) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Resume not found",
      });
    }

    return resume[0];
  }),
});

export type SharingRouter = typeof sharingRouter;
