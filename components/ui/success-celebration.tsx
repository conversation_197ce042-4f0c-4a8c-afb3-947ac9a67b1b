"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import { ConfettiEffect } from "./confetti-effect";
import { FloatingHearts } from "./floating-hearts";

interface SuccessCelebrationProps {
  isVisible: boolean;
  onClose: () => void;
  type: "resume_created" | "template_selected" | "upgrade_completed" | "profile_completed" | "first_download";
  title?: string;
  message?: string;
  encouragement?: string;
  showConfetti?: boolean;
  showHearts?: boolean;
  autoCloseDelay?: number;
  className?: string;
}

const CELEBRATION_CONFIGS = {
  resume_created: {
    icon: "heroicons:document-check-20-solid",
    color: "text-success",
    bgColor: "from-success-50 to-green-100",
    title: "Congratulations! Resume Created!",
    message: "You've taken the first step towards your dream job!",
    encouragement: "Now let's make it shine with your unique story",
    confetti: true,
    hearts: false,
  },
  template_selected: {
    icon: "heroicons:sparkles-20-solid",
    color: "text-primary",
    bgColor: "from-primary-50 to-purple-100",
    title: "Perfect Choice!",
    message: "This template will make you stand out from the crowd",
    encouragement: "Time to add your personal touch!",
    confetti: false,
    hearts: true,
  },
  upgrade_completed: {
    icon: "heroicons:star-20-solid",
    color: "text-warning",
    bgColor: "from-warning-50 to-yellow-100",
    title: "Welcome to Premium!",
    message: "You now have access to all our powerful features",
    encouragement: "Let's create something amazing together!",
    confetti: true,
    hearts: true,
  },
  profile_completed: {
    icon: "heroicons:user-circle-20-solid",
    color: "text-secondary",
    bgColor: "from-secondary-50 to-pink-100",
    title: "Profile Complete!",
    message: "You're all set up and ready to go",
    encouragement: "Your future employers will be impressed!",
    confetti: false,
    hearts: false,
  },
  first_download: {
    icon: "heroicons:arrow-down-tray-20-solid",
    color: "text-success",
    bgColor: "from-success-50 to-emerald-100",
    title: "Download Complete!",
    message: "Your professional resume is ready to impress",
    encouragement: "Go get that dream job! We believe in you!",
    confetti: true,
    hearts: false,
  },
};

const MOTIVATIONAL_PHRASES = [
  "You're closer to your dream job than ever!",
  "Every great career starts with a single step",
  "Your future self will thank you for this moment",
  "Success is just around the corner!",
  "You've got what it takes to succeed!",
];

export function SuccessCelebration({
  isVisible,
  onClose,
  type,
  title,
  message,
  encouragement,
  showConfetti,
  showHearts,
  autoCloseDelay = 8000,
  className = "",
}: SuccessCelebrationProps) {
  const [currentPhrase, setCurrentPhrase] = useState(0);
  const [showEffects, setShowEffects] = useState(false);

  const config = CELEBRATION_CONFIGS[type];
  const finalTitle = title || config.title;
  const finalMessage = message || config.message;
  const finalEncouragement = encouragement || config.encouragement;
  const shouldShowConfetti = showConfetti ?? config.confetti;
  const shouldShowHearts = showHearts ?? config.hearts;

  useEffect(() => {
    if (isVisible) {
      setShowEffects(true);

      // Cycle through motivational phrases
      const phraseTimer = setInterval(() => {
        setCurrentPhrase((prev) => (prev + 1) % MOTIVATIONAL_PHRASES.length);
      }, 2000);

      // Auto close
      const closeTimer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => {
        clearInterval(phraseTimer);
        clearTimeout(closeTimer);
      };
    }
  }, [isVisible, onClose, autoCloseDelay]);

  if (!isVisible) return null;

  return (
    <>
      {/* Celebration Effects */}
      <AnimatePresence>
        {showEffects && shouldShowConfetti && (
          <ConfettiEffect duration={4000} particleCount={80} onComplete={() => setShowEffects(false)} />
        )}
        {showEffects && shouldShowHearts && <FloatingHearts count={12} duration={5000} onComplete={() => {}} />}
      </AnimatePresence>

      {/* Modal Overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.7, opacity: 0, rotateY: -30 }}
          animate={{ scale: 1, opacity: 1, rotateY: 0 }}
          exit={{ scale: 0.8, opacity: 0, rotateY: 30 }}
          transition={{ type: "spring", damping: 15, stiffness: 300 }}
          className={`w-full max-w-md ${className}`}
        >
          <Card
            className={`border-none shadow-2xl bg-gradient-to-br ${config.bgColor} dark:from-default-900 dark:to-default-800`}
          >
            <CardBody className="p-8 text-center">
              {/* Animated Icon */}
              <motion.div
                className={`mx-auto mb-6 w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center`}
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 10, -10, 0],
                }}
                transition={{
                  scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                  rotate: { duration: 3, repeat: Infinity, ease: "easeInOut" },
                }}
              >
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Icon icon={config.icon} className={`w-10 h-10 ${config.color}`} />
                </motion.div>
              </motion.div>

              {/* Title */}
              <motion.h2
                className="text-2xl font-bold text-default-900 mb-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                {finalTitle}
              </motion.h2>

              {/* Message */}
              <motion.p
                className="text-default-700 mb-4 text-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {finalMessage}
              </motion.p>

              {/* Encouragement */}
              <motion.p
                className={`font-medium mb-6 ${config.color}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {finalEncouragement}
              </motion.p>

              {/* Rotating Motivational Phrases */}
              <div className="h-6 mb-6">
                <AnimatePresence mode="wait">
                  <motion.p
                    key={currentPhrase}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.5 }}
                    className="text-sm text-default-600 italic"
                  >
                    {MOTIVATIONAL_PHRASES[currentPhrase]}
                  </motion.p>
                </AnimatePresence>
              </div>

              {/* Action Button */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  color="primary"
                  size="lg"
                  className="w-full font-semibold bg-gradient-to-r from-primary to-secondary hover:shadow-2xl transition-all duration-300"
                  startContent={
                    <motion.div
                      animate={{ rotate: [0, 15, -15, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <Icon icon="heroicons:heart-20-solid" className="w-5 h-5" />
                    </motion.div>
                  }
                  onPress={onClose}
                >
                  Continue Journey
                </Button>
              </motion.div>

              {/* Progress indicator */}
              <motion.div
                className="mt-4 w-full bg-white/20 rounded-full h-1 overflow-hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <motion.div
                  className="h-full bg-primary rounded-full"
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ duration: autoCloseDelay / 1000, ease: "linear" }}
                />
              </motion.div>
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>
    </>
  );
}

export default SuccessCelebration;
