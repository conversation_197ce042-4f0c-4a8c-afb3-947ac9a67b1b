import { and, eq } from "drizzle-orm";
import { db } from "@/db";
import { resumes } from "@/db/schema";

export async function getFullResumeQuery(resumeId: number, userId: string) {
  const resume = await db.query.resumes.findFirst({
    where: and(eq(resumes.id, resumeId), eq(resumes.userId, userId)),
    with: {
      template: true,
      educations: {
        orderBy: (educations, { asc }) => [asc(educations.sort)],
      },
      experiences: {
        orderBy: (experiences, { asc }) => [asc(experiences.sort)],
      },
      projects: {
        orderBy: (projects, { asc }) => [asc(projects.sort)],
      },
      awards: {
        orderBy: (awards, { asc }) => [asc(awards.sort)],
      },
      certifications: {
        orderBy: (certifications, { asc }) => [asc(certifications.sort)],
      },
      skills: {
        orderBy: (skills, { asc }) => [asc(skills.sort)],
      },
      languages: {
        orderBy: (languages, { asc }) => [asc(languages.sort)],
      },
      references: {
        orderBy: (references, { asc }) => [asc(references.sort)],
      },
      hobbies: {
        orderBy: (hobbies, { asc }) => [asc(hobbies.sort)],
      },
      volunteerings: {
        orderBy: (volunteerings, { asc }) => [asc(volunteerings.sort)],
      },
      profiles: {
        orderBy: (profiles, { asc }) => [asc(profiles.sort)],
      },
    },
  });

  return resume;
}

export const getResumeQuery = async (id: number, userId: string) => {
  return await db.query.resumes.findFirst({
    where: and(eq(resumes.id, id), eq(resumes.userId, userId)),
  });
};
