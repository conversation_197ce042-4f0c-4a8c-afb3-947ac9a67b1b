"use server";

import { and, asc, eq } from "drizzle-orm";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  hobbies,
  languages,
  profiles,
  projects,
  resumes,
  skills,
  volunteerings,
  websites,
  websiteTemplates,
} from "@/db/schema";

// Get public website by slug (no auth required)
export async function getPublicWebsite(slug: string) {
  try {
    // Get the website with basic relations
    const [websiteData] = await db
      .select()
      .from(websites)
      .leftJoin(resumes, eq(websites.resumeId, resumes.id))
      .leftJoin(websiteTemplates, eq(websites.websiteTemplateId, websiteTemplates.id))
      .where(and(eq(websites.slug, slug), eq(websites.isPublic, 1)))
      .limit(1);

    if (!websiteData?.websites) {
      return {
        success: false,
        error: "Website not found or not public",
      };
    }

    const resumeId = websiteData.resumes?.id;
    if (!resumeId) {
      return {
        success: false,
        error: "Resume data not found",
      };
    }

    // Get all nested resume resources in parallel (excluding references for privacy)
    const [
      resumeEducations,
      resumeExperiences,
      resumeProjects,
      resumeAwards,
      resumeCertifications,
      resumeSkills,
      resumeLanguages,
      resumeHobbies,
      resumeVolunteerings,
      resumeProfiles,
    ] = await Promise.all([
      db.select().from(educations).where(eq(educations.resumeId, resumeId)).orderBy(asc(educations.sort)),
      db.select().from(experiences).where(eq(experiences.resumeId, resumeId)).orderBy(asc(experiences.sort)),
      db.select().from(projects).where(eq(projects.resumeId, resumeId)).orderBy(asc(projects.sort)),
      db.select().from(awards).where(eq(awards.resumeId, resumeId)).orderBy(asc(awards.sort)),
      db.select().from(certifications).where(eq(certifications.resumeId, resumeId)).orderBy(asc(certifications.sort)),
      db.select().from(skills).where(eq(skills.resumeId, resumeId)).orderBy(asc(skills.sort)),
      db.select().from(languages).where(eq(languages.resumeId, resumeId)).orderBy(asc(languages.sort)),
      db.select().from(hobbies).where(eq(hobbies.resumeId, resumeId)).orderBy(asc(hobbies.sort)),
      db.select().from(volunteerings).where(eq(volunteerings.resumeId, resumeId)).orderBy(asc(volunteerings.sort)),
      db.select().from(profiles).where(eq(profiles.resumeId, resumeId)).orderBy(asc(profiles.sort)),
    ]);

    // Combine the data
    const fullResume = {
      ...websiteData.resumes,
      educations: resumeEducations,
      experiences: resumeExperiences,
      projects: resumeProjects,
      awards: resumeAwards,
      certifications: resumeCertifications,
      skills: resumeSkills,
      languages: resumeLanguages,
      hobbies: resumeHobbies,
      volunteerings: resumeVolunteerings,
      profiles: resumeProfiles,
      references: [],
    };

    const website = {
      ...websiteData.websites,
      resume: fullResume,
      websiteTemplate: websiteData.website_templates,
    };

    // Remove sensitive data before returning
    const publicWebsite = {
      website,
      resume: fullResume,
      userId: undefined,
    };

    return {
      success: true,
      data: publicWebsite,
    };
  } catch (error) {
    console.error("Error fetching public website:", error);
    return {
      success: false,
      error: "Failed to fetch website",
    };
  }
}
