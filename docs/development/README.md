# Development Documentation

This directory contains development-related documentation and guides.

## Files

- **[FEATURE_ROADMAP.md](FEATURE_ROADMAP.md)** - Planned features and development timeline
- **[resume-templates.md](resume-templates.md)** - Template system architecture and development guide

## Development Guidelines

### 🏗️ Architecture
- **Framework**: Next.js 15 with App Router
- **Database**: Drizzle ORM with SQLite/Turso
- **Authentication**: Clerk with webhook sync
- **UI**: HeroUI v2 with Tailwind CSS
- **Icons**: Iconify React (not Lucide)

### 📝 Code Standards
- Use TypeScript for all new code
- Follow existing patterns and conventions
- Implement proper error handling
- Add translations for all user-facing text

### 🎨 Template Development
- Templates are React components in `components/resume/templates/`
- Each template must be registered in the template registry
- Support for RTL languages and internationalization
- ATS-compatible designs

### 🚀 Feature Development
- Use the feature flag system for new features
- Implement premium/free tier separation
- Add proper server actions for data operations
- Test responsive design and accessibility

### 🔄 Development Workflow
1. Create feature branch from main
2. Implement feature with proper testing
3. Update documentation and translations
4. Create pull request with detailed description

### 📚 Key Development Areas
- Template system expansion
- AI feature enhancements
- Payment system improvements
- Performance optimizations
- Accessibility improvements

See individual files for detailed development guides.