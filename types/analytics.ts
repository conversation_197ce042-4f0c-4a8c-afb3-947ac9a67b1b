// Enhanced Analytics Types for QuickCV

// PostHog Feature Flag Types
export interface PostHogFeatureFlagEvent {
  flagKey: string;
  variant: string | boolean;
  enabled: boolean;
  userId?: string;
  userEmail?: string;
  isPremium?: boolean;
  experimentName?: string;
  eventType?: "exposure" | "conversion" | "feedback";
  properties?: Record<string, any>;
}

export interface PostHogFeatureFlagConfig {
  flagKey: string;
  enabled: boolean;
  variant?: string | boolean;
  payload?: any;
  rolloutPercentage?: number;
  userOverrides?: Record<string, boolean>;
  segmentOverrides?: Record<string, boolean>;
}

export interface AnalyticsEvent {
  eventName: string;
  eventType: "view" | "click" | "conversion" | "error" | "demo" | "interaction" | "marketing" | "used" | "blocked";
  userId: string;
  properties?: Record<string, any>;
  timestamp?: string;
  sessionId?: string;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  screenResolution?: string;
  deviceType?: "desktop" | "mobile" | "tablet";
  location?: {
    country?: string;
    city?: string;
    timezone?: string;
  };
  duration?: number;
}

export interface ConversionFunnelData {
  step: string;
  name: string;
  users: number;
  conversions: number;
  conversionRate: number;
  dropOffRate: number;
  stepOrder: number;
}

export interface UserJourneyStep {
  stepNumber: number;
  feature: string;
  action: string;
  timestamp: string;
  duration: number;
  properties: Record<string, any>;
  nextStep?: string;
}

export interface MarketingMetrics {
  viralCoefficient: number;
  totalShares: number;
  socialEngagement: number;
  contentEngagement: {
    content: string;
    engagements: number;
    uniqueUsers: number;
    avgEngagementTime: number;
  }[];
  attributionSources: {
    source: string;
    users: number;
    conversions: number;
    conversionRate: number;
  }[];
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface PerformanceMetrics {
  averageLoadTime: number;
  averageRenderTime: number;
  overallErrorRate: number;
  slowestFeatures: {
    feature: string;
    avgLoadTime: number;
    requests: number;
  }[];
  highestErrorRates: {
    feature: string;
    errorRate: number;
    requests: number;
  }[];
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface UserSegment {
  isPremium?: boolean;
  cohort?: {
    startDate: string;
    endDate: string;
  };
  location?: string;
  deviceType?: string;
  acquisitionSource?: string;
  engagementLevel?: "high" | "medium" | "low";
}

export interface MicroInteractionMetrics {
  totalInteractions: number;
  uniqueUsers: number;
  interactionBreakdown: {
    type: string;
    count: number;
    uniqueUsers: number;
    avgDuration: number;
  }[];
  celebrationEngagement: {
    total: number;
    uniqueUsers: number;
  };
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface ComprehensiveAnalyticsReport {
  period: {
    startDate: string;
    endDate: string;
  };
  segment?: UserSegment;
  conversionFunnel: ConversionFunnelData[];
  marketingMetrics: MarketingMetrics;
  performanceMetrics: PerformanceMetrics;
  microInteractionMetrics: MicroInteractionMetrics;
  generatedAt: string;
}

// Dashboard specific types
export interface AnalyticsDashboardData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    conversions: number;
    revenue: number;
    period: { start: string; end: string };
  };
  trends: {
    userGrowth: { date: string; users: number; newUsers: number }[];
    conversionTrend: { date: string; rate: number; conversions: number }[];
    featureAdoption: { feature: string; adoption: number; growth: number }[];
  };
  segments: {
    name: string;
    users: number;
    conversionRate: number;
    revenue: number;
  }[];
  alerts: {
    type: "warning" | "error" | "info";
    message: string;
    timestamp: string;
    metric: string;
  }[];
}

export interface RealTimeMetrics {
  activeUsers: number;
  currentConversions: number;
  recentEvents: {
    eventName: string;
    userId: string;
    timestamp: string;
    properties: Record<string, any>;
  }[];
  performanceAlerts: {
    feature: string;
    issue: string;
    severity: "high" | "medium" | "low";
    timestamp: string;
  }[];
  lastUpdated: string;
}

// Hook types for React components
export interface UseAnalyticsReturn {
  trackEvent: (event: Omit<AnalyticsEvent, "timestamp">) => Promise<void>;
  trackPageView: (page: string, properties?: Record<string, any>) => Promise<void>;
  trackConversion: (type: string, value?: number, properties?: Record<string, any>) => Promise<void>;
  trackMicroInteraction: (type: string, properties?: Record<string, any>) => Promise<void>;
  trackMarketingEvent: (eventName: string, properties: Record<string, any>) => Promise<void>;
}

// Component tracking types
export interface ComponentAnalytics {
  componentName: string;
  trackInteraction: (action: string, properties?: Record<string, any>) => Promise<void>;
  trackView: (properties?: Record<string, any>) => Promise<void>;
  trackConversion: (conversionType: string, value?: number) => Promise<void>;
}

// Marketing campaign tracking
export interface CampaignMetrics {
  campaignId: string;
  name: string;
  startDate: string;
  endDate?: string;
  budget?: number;
  spend?: number;
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  conversionRate: number;
  costPerConversion: number;
  roi: number;
  attributedRevenue: number;
}

// Feature performance insights
export interface FeatureInsights {
  featureId: string;
  name: string;
  category: string;
  usageGrowth: number;
  adoptionRate: number;
  retentionRate: number;
  satisfactionScore?: number;
  performanceScore: number;
  businessImpact: {
    revenueAttribution: number;
    userEngagement: number;
    conversionLift: number;
  };
  recommendations: string[];
}

// Predictive analytics types
export interface PredictiveMetrics {
  churnProbability: {
    userId: string;
    probability: number;
    factors: string[];
    recommendedActions: string[];
  }[];
  ltv: {
    segment: string;
    predictedValue: number;
    confidence: number;
    timeframe: string;
  }[];
  growthForecast: {
    metric: string;
    currentValue: number;
    predictedValue: number;
    timeframe: string;
    confidence: number;
  }[];
}

// PostHog-specific hook return types
export interface UsePostHogFeatureFlagReturn {
  isEnabled: boolean;
  isLoading: boolean;
  variant: string | boolean;
  trackUsage: () => void;
}

export interface UsePostHogFeatureFlagPayloadReturn<T = any> {
  payload: T | undefined;
  isLoading: boolean;
  isEnabled: boolean;
  trackUsage: () => void;
}

export interface UsePostHogExperimentReturn {
  variant: string;
  isLoading: boolean;
  isEnabled: boolean;
  isControl: boolean;
  isVariant: boolean;
  trackConversion: (conversionEvent: string, value?: number) => void;
  trackUsage: () => void;
}
