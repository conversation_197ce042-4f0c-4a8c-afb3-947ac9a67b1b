// Server-side Paddle API client for fetching invoices and transactions
import type { Invoice, PaddleApiResponse, PaddleTransaction } from "@/types";

class PaddleServerService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.PADDLE_API_KEY || "";
    this.baseUrl =
      process.env.PADDLE_ENVIRONMENT === "production" ? "https://api.paddle.com" : "https://sandbox-api.paddle.com";

    if (!this.apiKey) {
      console.warn("Paddle API key not found. Invoice fetching will not work.");
    }
  }

  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    if (!this.apiKey) {
      throw new Error("Paddle API key not configured");
    }

    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("Paddle API error:", {
        status: response.status,
        statusText: response.statusText,
        body: errorData,
        url,
      });
      throw new Error(`Paddle API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getUserTransactions(customerId: string): Promise<Invoice[]> {
    try {
      // Fetch transactions for the specific customer using Paddle's customer_id filter
      const response = await this.makeRequest<PaddleApiResponse<PaddleTransaction>>(
        `/transactions?customer_id=${encodeURIComponent(customerId)}&status=completed&per_page=100`,
      );

      const invoices: Invoice[] = response.data.map((transaction) => {
        // Get the main product from the first item
        const mainItem = transaction.items[0];
        const productName = mainItem?.price?.name || "QuickCV Premium";

        return {
          id: transaction.id,
          date: transaction.created_at,
          amount: `$${(parseFloat(transaction.details.totals.grand_total) / 100).toFixed(2)}`,
          currency: transaction.details.totals.currency_code,
          status: transaction.status,
          description: productName,
          downloadUrl: transaction.receipt_data?.url,
          invoiceNumber: transaction.invoice_number || transaction.id,
        };
      });

      return invoices.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      console.error("Failed to fetch user transactions:", error);

      // Return empty array instead of throwing to prevent breaking the UI
      return [];
    }
  }
}

// Export singleton instance
export const paddleServer = new PaddleServerService();

// Re-export types for convenience
export type { Invoice, PaddleApiResponse, PaddleTransaction } from "@/types";
