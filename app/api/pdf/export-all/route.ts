import { eq } from "drizzle-orm";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { resumes } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

// Generate PDF for a single resume using the existing PDF generation service
async function generateSinglePDF(baseUrl: string, resumeId: string, userId: string, locale: string): Promise<Buffer> {
  const browserWSEndpoint = process.env.BROWSER_WS_ENDPOINT;

  if (!browserWSEndpoint) {
    throw new Error("BROWSER_WS_ENDPOINT environment variable is required");
  }

  const puppeteer = await import("puppeteer");
  const browser = await puppeteer.default.connect({
    browserWSEndpoint,
  });

  try {
    const page = await browser.newPage();

    // Set viewport for consistent rendering
    await page.setViewport({
      width: 1280,
      height: 1024,
      deviceScaleFactor: 2,
    });

    // Navigate directly to PDF route
    const pdfUrl = `${baseUrl}/pdf/${resumeId}?userId=${userId}&locale=${locale}`;

    await page.goto(pdfUrl, {
      waitUntil: "networkidle0",
      timeout: 30000,
    });

    // Wait for fonts to load
    try {
      await page.evaluate(() => {
        return document.fonts.ready;
      });
    } catch (_error) {
      // Font loading timeout, proceeding anyway
    }

    // Additional wait for rendering to complete
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Generate PDF with A4 format
    const pdf = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true,
      margin: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      },
    });

    await page.close();
    return Buffer.from(pdf);
  } finally {
    await browser.close();
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();

    // Check if user has premium access
    if (!user.isPremium) {
      return NextResponse.json({ error: "Premium subscription required for bulk PDF export" }, { status: 403 });
    }

    const { resumeIds, locale } = await request.json();

    // Limit the number of resumes that can be exported at once
    const MAX_EXPORT_LIMIT = 20;
    if (resumeIds && resumeIds.length > MAX_EXPORT_LIMIT) {
      return NextResponse.json(
        {
          error: `Cannot export more than ${MAX_EXPORT_LIMIT} resumes at once`,
        },
        { status: 400 },
      );
    }

    // If no specific IDs provided, get all user's resumes
    let userResumes;
    if (resumeIds && resumeIds.length > 0) {
      // Get specific resumes
      userResumes = await db
        .select()
        .from(resumes)
        .where(eq(resumes.userId, user.clerkId))
        .then((results) => results.filter((r) => resumeIds.includes(r.id)));
    } else {
      // Get all user's resumes
      userResumes = await db.select().from(resumes).where(eq(resumes.userId, user.clerkId));
    }

    if (userResumes.length === 0) {
      return NextResponse.json({ error: "No resumes found to export" }, { status: 404 });
    }

    // Get the base URL for the PDF rendering route
    const protocol = request.nextUrl.protocol;
    let hostname = request.nextUrl.hostname;
    const port = request.nextUrl.port;

    // Replace localhost with host.docker.internal for Docker-based Browserless service in development
    if (process.env.NODE_ENV === "development" && (hostname === "localhost" || hostname === "127.0.0.1")) {
      hostname = "host.docker.internal";
    }

    // Construct the base URL properly
    let baseUrl;
    if (port && port !== "80" && port !== "443") {
      baseUrl = `${protocol}//${hostname}:${port}`;
    } else {
      baseUrl = `${protocol}//${hostname}`;
    }

    // Create a zip file to contain all PDFs
    const zip = new JSZip();

    // Generate PDFs for each resume
    const pdfPromises = userResumes.map(async (resume, index) => {
      try {
        // Add a small delay between each PDF generation to avoid overwhelming the service
        await new Promise((resolve) => setTimeout(resolve, index * 1000));

        const pdfBuffer = await generateSinglePDF(baseUrl, resume.id.toString(), user.clerkId, locale || "en");

        // Create a safe filename
        const safeTitle = (resume.title || `resume_${index + 1}`).replace(/[^a-z0-9]/gi, "_").toLowerCase();
        const filename = `${safeTitle}_${new Date().toISOString().slice(0, 10)}.pdf`;

        // Add PDF to zip
        zip.file(filename, pdfBuffer);

        return { success: true, title: resume.title };
      } catch (error) {
        console.error(`Failed to generate PDF for resume ${resume.id}:`, error);
        return { success: false, title: resume.title, error };
      }
    });

    // Wait for all PDFs to be generated
    const results = await Promise.all(pdfPromises);

    // Check if any PDFs were successfully generated
    const successfulExports = results.filter((r) => r.success);
    if (successfulExports.length === 0) {
      return NextResponse.json({ error: "Failed to generate any PDFs" }, { status: 500 });
    }

    // Generate the zip file
    const zipBuffer = await zip.generateAsync({ type: "nodebuffer" });

    // Create response with zip file
    const response = new NextResponse(zipBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": `attachment; filename="resumes_export_${new Date().toISOString().slice(0, 10)}.zip"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });

    return response;
  } catch (error) {
    console.error("Bulk PDF export error:", error);

    // Return more specific error information
    const errorMessage = error instanceof Error ? error.message : "Failed to export PDFs";

    return NextResponse.json(
      {
        error: "Bulk PDF generation failed",
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
