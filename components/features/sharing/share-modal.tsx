"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { EmailShareContent } from "./email-share-content";
import { SocialShareButtons } from "./social-share-buttons";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  resumeId: number;
}

export function ShareModal({ isOpen, onClose, resumeId }: ShareModalProps) {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="tabler:share" className="w-6 h-6" />
            Share Resume
          </div>
          <p className="text-sm text-default-500 font-normal">
            Share your resume with potential employers and track engagement.
          </p>
        </ModalHeader>
        <ModalBody>
          <Tabs
            aria-label="Sharing options"
            color="primary"
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary h-0.5",
              tab: "max-w-fit px-4 h-12 data-[selected=true]:text-primary",
              tabContent: "group-data-[selected=true]:text-primary group-data-[selected=true]:font-semibold",
            }}
          >
            <Tab
              key="social"
              title={
                <div className="flex items-center space-x-2">
                  <Icon icon="tabler:share" className="w-4 h-4" />
                  <span>Social Sharing</span>
                </div>
              }
            >
              <div className="py-4">
                <SocialShareButtons resumeId={resumeId} />
              </div>
            </Tab>
            <Tab
              key="email"
              title={
                <div className="flex items-center space-x-2">
                  <Icon icon="tabler:mail" className="w-4 h-4" />
                  <span>Email Templates</span>
                </div>
              }
            >
              <div className="py-4">
                <EmailShareContent resumeId={resumeId} />
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
