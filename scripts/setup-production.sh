#!/bin/bash

# Database seeding script for QuickCV

set -e

echo "🌱 Seeding QuickCV database with templates..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️  DATABASE_URL not set, using default SQLite path"
    export DATABASE_URL="file:/app/data/production.db"
fi

# Seed the database with resume templates
echo "📄 Adding resume templates to database..."
bun run db/seed.ts

echo "✅ Database seeding complete!"
echo "🎯 Resume templates are now available!"