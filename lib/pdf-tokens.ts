import { JWTPayload, jwtVerify, SignJWT } from "jose";

const secret = new TextEncoder().encode(process.env.PDF_TOKEN_SECRET || "fallback-secret-for-development");

export interface PDFTokenPayload extends JWTPayload {
  userId: string;
  resumeId: number;
}

export async function createPDFToken(userId: string, resumeId: number): Promise<string> {
  const payload: PDFTokenPayload = {
    userId,
    resumeId,
  };

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("5m")
    .sign(secret);
}

export async function verifyPDFToken(token: string): Promise<PDFTokenPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload as PDFTokenPayload;
  } catch (error) {
    console.error("PDF token verification failed:", error);
    return null;
  }
}
