"use client";

import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { useTRPCLoading } from "@/lib/use-loading-action";

interface LinkedInCreateResumeButtonProps {
  onSuccess?: (resumeId: number) => void;
}

export function LinkedInCreateResumeButton({ onSuccess }: LinkedInCreateResumeButtonProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const importMutation = trpc.resumes.importFromLinkedIn.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  const wrappedImport = wrapTRPCMutation(importMutation, "linkedin-import", "Importing from LinkedIn...");

  const handleLinkedInAuth = () => {
    setIsConnecting(true);
    // Open LinkedIn OAuth in a popup window
    const popup = window.open(
      "/api/linkedin/auth",
      "linkedin-auth",
      "width=600,height=700,scrollbars=yes,resizable=yes",
    );

    // Listen for the popup to close or get redirected
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        setIsConnecting(false);
        // Check if user is now connected by trying to import
        handleImport();
      }
    }, 1000);

    // Cleanup if popup is still open after 5 minutes
    setTimeout(() => {
      if (popup && !popup.closed) {
        popup.close();
        clearInterval(checkClosed);
        setIsConnecting(false);
        toast.error("LinkedIn authentication timed out");
      }
    }, 300000);
  };

  const handleImport = async () => {
    try {
      // Call import to create a new resume
      const result = await wrappedImport.mutateAsync();

      if (result.success) {
        const { importedSections, resumeId } = result;
        let message = "Resume created from LinkedIn!";

        if (importedSections.personalInfo) {
          message += " Personal info imported.";
        }
        if (importedSections.experiences > 0) {
          message += ` ${importedSections.experiences} experiences imported.`;
        }
        if (importedSections.educations > 0) {
          message += ` ${importedSections.educations} educations imported.`;
        }
        if (importedSections.skills > 0) {
          message += ` ${importedSections.skills} skills imported.`;
        }

        toast.success(message);
        onSuccess?.(resumeId);
      }
    } catch (error: any) {
      if (error.message?.includes("not connected")) {
        // User needs to connect LinkedIn first
        handleLinkedInAuth();
      } else {
        console.error("LinkedIn import error:", error);
        toast.error("Failed to import from LinkedIn");
      }
    }
  };

  const isLoading = isConnecting || importMutation.isPending;

  return (
    <div className="flex flex-col gap-4 p-4">
      <p className="text-center text-default-600">
        Import your LinkedIn profile to create a professional resume instantly
      </p>
      <p className="text-sm text-default-500">
        Your LinkedIn profile data will be imported and formatted into a professional resume template
      </p>

      <Button
        color="primary"
        size="lg"
        className="w-full"
        startContent={<Icon icon="simple-icons:linkedin" width={16} height={16} />}
        onPress={handleImport}
        isLoading={isLoading}
        isDisabled={isLoading}
      >
        {isConnecting ? "Connecting to LinkedIn..." : "Connect with LinkedIn"}
      </Button>
    </div>
  );
}
