import React from 'react';
import { FullResume } from '@/db/schema';

interface OnyxTemplateProps {
  resume: FullResume;
}

export default function OnyxTemplate({ resume }: OnyxTemplateProps) {
  const colorClasses = {
    blue: 'text-blue-700 bg-blue-700 border-blue-700',
    green: 'text-green-700 bg-green-700 border-green-700',
    purple: 'text-purple-700 bg-purple-700 border-purple-700',
    red: 'text-red-700 bg-red-700 border-red-700',
    orange: 'text-orange-700 bg-orange-700 border-orange-700',
    teal: 'text-teal-700 bg-teal-700 border-teal-700',
    pink: 'text-pink-700 bg-pink-700 border-pink-700',
    indigo: 'text-indigo-700 bg-indigo-700 border-indigo-700',
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div className={`max-w-4xl mx-auto bg-white shadow-lg font-${resume.fontFamily || 'inter'} text-gray-900 print:shadow-none`}>
      {/* Bold header with strong visual impact */}
      <header className="relative bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white px-8 py-12">
        <div className={`absolute top-0 left-0 w-full h-2 ${selectedColor.split(' ')[1]}`}></div>
        <div className={`absolute bottom-0 left-0 w-full h-2 ${selectedColor.split(' ')[1]}`}></div>
        
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Bold and impactful */}
            <h1 className="text-5xl font-black text-white mb-3 leading-tight tracking-wide">
              {resume.firstName}
            </h1>
            <h1 className={`text-5xl font-black mb-4 leading-tight tracking-wide ${selectedColor.split(' ')[0].replace('text-', 'text-').replace('700', '300')}`}>
              {resume.lastName}
            </h1>
            
            {/* Job Title - Strong accent */}
            <h2 className="text-2xl font-bold text-white mb-6 leading-relaxed tracking-widest uppercase">
              {resume.jobTitle}
            </h2>
            
            {/* Bio - Prominent display */}
            {resume.bio && (
              <p className="text-lg text-gray-200 leading-relaxed max-w-3xl mb-6 font-medium">
                {resume.bio}
              </p>
            )}
          </div>
          
          {/* Photo - Bold framing */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0 relative">
              <div className={`absolute -inset-2 ${selectedColor.split(' ')[1]} opacity-30`}></div>
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="relative w-36 h-36 object-cover border-4 border-white shadow-2xl"
              />
            </div>
          )}
        </div>
        
        {/* Contact Information - Bold layout */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8 pt-6 border-t-2 border-gray-600">
          {resume.email && (
            <div className="text-center">
              <span className="block font-black text-gray-400 text-xs uppercase tracking-widest mb-2">Email</span>
              <span className="text-white text-sm font-semibold">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div className="text-center">
              <span className="block font-black text-gray-400 text-xs uppercase tracking-widest mb-2">Phone</span>
              <span className="text-white text-sm font-semibold">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div className="text-center">
              <span className="block font-black text-gray-400 text-xs uppercase tracking-widest mb-2">Website</span>
              <span className="text-white text-sm font-semibold">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div className="text-center">
              <span className="block font-black text-gray-400 text-xs uppercase tracking-widest mb-2">Location</span>
              <span className="text-white text-sm font-semibold">
                {[resume.city, resume.country].filter(Boolean).join(', ')}
              </span>
            </div>
          )}
        </div>
      </header>

      {/* Bold content sections */}
      <div className="px-8 py-8">
        {/* Experience Section - Bold headers */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-12">
            <div className={`${selectedColor.split(' ')[1]} text-white px-6 py-4 mb-8 shadow-lg`}>
              <h3 className="text-3xl font-black uppercase tracking-widest">
                Professional Experience
              </h3>
            </div>
            <div className="space-y-10">
              {resume.experiences.map((experience) => (
                <div key={experience.id} className="relative border-l-8 border-gray-300 pl-8 hover:border-gray-400 transition-colors duration-300">
                  <div className={`absolute -left-4 top-0 w-8 h-8 ${selectedColor.split(' ')[1]} transform rotate-45`}></div>
                  
                  {/* Job Title - Most prominent */}
                  <h4 className="text-2xl font-black text-gray-900 mb-3 leading-tight">
                    {experience.title}
                  </h4>
                  
                  {/* Company and Date - Bold styling */}
                  <div className="flex items-center justify-between mb-4">
                    <h5 className={`text-xl font-black ${selectedColor.split(' ')[0]} leading-relaxed uppercase tracking-wide`}>
                      {experience.company}
                    </h5>
                    <div className="text-right bg-gray-900 text-white px-4 py-2">
                      <span className="text-base font-bold">
                        {experience.startDate} - {experience.isCurrent ? 'Present' : experience.endDate}
                      </span>
                      {(experience.city || experience.country) && (
                        <p className="text-sm text-gray-300 mt-1">
                          {[experience.city, experience.country].filter(Boolean).join(', ')}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {/* Description - Strong formatting */}
                  {experience.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none pl-6 border-l-4 border-gray-200 bg-gray-50 p-4"
                      dangerouslySetInnerHTML={{ __html: experience.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects Section - Bold styling */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-12">
            <div className={`${selectedColor.split(' ')[1]} text-white px-6 py-4 mb-8 shadow-lg`}>
              <h3 className="text-3xl font-black uppercase tracking-widest">
                Key Projects
              </h3>
            </div>
            <div className="grid gap-8">
              {resume.projects.map((project) => (
                <div key={project.id} className="bg-gradient-to-r from-gray-50 to-white border-l-8 border-gray-300 pl-8 p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className={`absolute -left-4 top-6 w-8 h-8 ${selectedColor.split(' ')[1]} transform rotate-45`}></div>
                  
                  {/* Project Title */}
                  <h4 className="text-xl font-black text-gray-900 mb-3 leading-tight">
                    {project.title}
                  </h4>
                  
                  {/* Client and Date */}
                  <div className="flex items-center justify-between mb-4">
                    {project.client && (
                      <h5 className={`text-lg font-black ${selectedColor.split(' ')[0]} leading-relaxed uppercase tracking-wide`}>
                        {project.client}
                      </h5>
                    )}
                    <span className="text-base font-bold text-white bg-gray-800 px-4 py-2">
                      {project.startDate} - {project.endDate}
                    </span>
                  </div>
                  
                  {/* URL */}
                  {project.url && (
                    <p className="text-sm text-gray-600 mb-4">
                      <a href={project.url} className={`${selectedColor.split(' ')[0]} hover:underline font-bold flex items-center`}>
                        🔗 {project.url}
                      </a>
                    </p>
                  )}
                  
                  {/* Description */}
                  {project.description && (
                    <div
                      className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: project.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section - Bold grid */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-12">
            <div className={`${selectedColor.split(' ')[1]} text-white px-6 py-4 mb-8 shadow-lg`}>
              <h3 className="text-3xl font-black uppercase tracking-widest">
                Core Competencies
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {resume.skills.map((skill) => (
                <div key={skill.id} className="bg-gradient-to-br from-white to-gray-100 p-6 border-l-4 border-gray-400 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-lg font-black text-gray-800">{skill.name}</span>
                    {skill.proficiency && (
                      <span className="text-sm font-black text-white bg-gray-800 px-3 py-1">
                        {skill.proficiency}%
                      </span>
                    )}
                  </div>
                  {skill.proficiency && (
                    <div className="w-full bg-gray-300 h-4">
                      <div
                        className={`h-4 ${selectedColor.split(' ')[1]} shadow-md`}
                        style={{ width: `${skill.proficiency}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Additional sections in bold grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Education */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.split(' ')[1]} text-white px-4 py-3 mb-6 shadow-lg`}>
                <h3 className="text-xl font-black uppercase tracking-widest">
                  Education
                </h3>
              </div>
              <div className="space-y-6">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-gradient-to-r from-gray-50 to-white border-l-4 border-gray-400 pl-6 p-4 shadow-md">
                    {/* Degree - Strong emphasis */}
                    <h4 className="text-lg font-black text-gray-900 mb-2 leading-tight">
                      {education.degree}
                    </h4>
                    
                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-base font-bold text-gray-700 mb-2">
                        {education.fieldOfStudy}
                      </p>
                    )}
                    
                    {/* Institution */}
                    <h5 className={`text-base font-black ${selectedColor.split(' ')[0]} mb-3 uppercase`}>
                      {education.institution}
                    </h5>
                    
                    {/* Date and Location */}
                    <div className="text-sm text-gray-600 font-bold">
                      <p>{education.startDate} - {education.isCurrent ? 'Present' : education.endDate}</p>
                      {(education.city || education.country) && (
                        <p>{[education.city, education.country].filter(Boolean).join(', ')}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.split(' ')[1]} text-white px-4 py-3 mb-6 shadow-lg`}>
                <h3 className="text-xl font-black uppercase tracking-widest">
                  Languages
                </h3>
              </div>
              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-gradient-to-r from-gray-50 to-white border-l-4 border-gray-400 pl-6 p-4 shadow-md">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-base font-black text-gray-800">{language.name}</span>
                      <span className="text-sm font-black text-white bg-gray-800 px-3 py-1">
                        {language.proficiency}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-300 h-3">
                      <div
                        className={`h-3 ${selectedColor.split(' ')[1]} shadow-sm`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Final sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-8">
          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.split(' ')[1]} text-white px-4 py-3 mb-6 shadow-lg`}>
                <h3 className="text-xl font-black uppercase tracking-widest">
                  Certifications
                </h3>
              </div>
              <div className="space-y-4">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-gradient-to-r from-gray-50 to-white border-l-4 border-gray-400 pl-6 p-4 shadow-md">
                    <h4 className="text-sm font-black text-gray-900 leading-tight mb-1">
                      {cert.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-bold">{cert.issuer}</p>
                    {cert.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1 font-semibold">{cert.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.split(' ')[1]} text-white px-4 py-3 mb-6 shadow-lg`}>
                <h3 className="text-xl font-black uppercase tracking-widest">
                  Awards & Recognition
                </h3>
              </div>
              <div className="space-y-4">
                {resume.awards.map((award) => (
                  <div key={award.id} className="bg-gradient-to-r from-gray-50 to-white border-l-4 border-gray-400 pl-6 p-4 shadow-md">
                    <h4 className="text-sm font-black text-gray-900 leading-tight mb-1">
                      {award.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-bold">{award.issuer}</p>
                    {award.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1 font-semibold">{award.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}