import { eq } from "drizzle-orm";
import { staticResumeTemplates, staticWebsiteTemplates } from "@/lib/constants";
import { db } from "./index";
import { templates, websiteTemplates } from "./schema";

async function main() {
  // Create templates based on the existing template files

  console.log("Seeding templates...");

  for (const template of staticResumeTemplates) {
    // Check if template already exists
    const existing = await db.select().from(templates).where(eq(templates.slug, template.slug)).limit(1);

    if (existing.length === 0) {
      await db.insert(templates).values(template);
      console.log(`Created template: ${template.name}`);
    } else {
      console.log(`Template already exists: ${template.name}`);
    }
  }

  console.log("Seeding website templates...");

  for (const websiteTemplate of staticWebsiteTemplates) {
    // Check if website template already exists
    const existing = await db
      .select()
      .from(websiteTemplates)
      .where(eq(websiteTemplates.slug, websiteTemplate.slug))
      .limit(1);

    if (existing.length === 0) {
      await db.insert(websiteTemplates).values(websiteTemplate);
      console.log(`Created website template: ${websiteTemplate.name}`);
    } else {
      console.log(`Website template already exists: ${websiteTemplate.name}`);
    }
  }

  console.log("Seeding completed!");
}

main()
  .then(() => {
    console.log("Database seeding finished successfully!");
    process.exit(0);
  })
  .catch((e) => {
    console.error("Error during seeding:", e);
    process.exit(1);
  });
