// Import enhanced date utilities
import { formatDate as enhancedFormatDate, formatDateRange as enhancedFormatDateRange } from "@/lib/shared/date-utils";

// Legacy wrapper functions for backward compatibility
// These maintain the original API while using the enhanced utilities internally
export const formatDate = (
  dateString: string | null,
  showPresent: boolean = false,
  locale: string = "en-US",
  yearOnly: boolean = false,
): string => {
  return enhancedFormatDate(
    dateString,
    yearOnly ? 'year-only' : 'short',
    locale,
    {
      showPresent,
      yearOnly,
    }
  );
};

export const formatDateRange = (
  startDate: string | null,
  endDate: string | null,
  isCurrent: number | null = 0,
  locale: string = "en-US",
): string => {
  return enhancedFormatDateRange(
    startDate,
    endDate,
    isCurrent,
    locale,
    {
      format: 'short',
      separator: '–',
    }
  );
};

export const getFullName = (firstName: string, lastName: string, locale: string = "en-US"): string => {
  // In Arabic, sometimes family name comes first
  if (locale === "ar" && lastName && firstName) {
    return `${firstName} ${lastName}`.trim();
  }
  return `${firstName} ${lastName}`.trim();
};

export const formatLocation = (city: string, country: string): string => {
  const parts = [city, country].filter(Boolean);
  return parts.join(", ");
};

// Hook to get current locale for templates
export const useTemplateLocale = () => {
  return "en"; // Always English since we removed i18n
};

import { getSectionTranslations as getI18nSectionTranslations } from '@/lib/i18n/date-translations';

export const getSectionTranslations = (locale: string = 'en-US') => {
  return getI18nSectionTranslations(locale);
};

// Responsive utility functions
export const getResponsiveClasses = (baseClasses: string, mobileClasses?: string, tabletClasses?: string, desktopClasses?: string): string => {
  let classes = baseClasses;
  
  if (mobileClasses) {
    classes += ` ${mobileClasses.split(' ').map(cls => `sm:${cls}`).join(' ')}`;
  }
  
  if (tabletClasses) {
    classes += ` ${tabletClasses.split(' ').map(cls => `md:${cls}`).join(' ')}`;
  }
  
  if (desktopClasses) {
    classes += ` ${desktopClasses.split(' ').map(cls => `lg:${cls}`).join(' ')}`;
  }
  
  return classes;
};

// Print utility functions
export const getPrintSafeClasses = (classes: string): string => {
  return classes + ' resume-template-base';
};

// A4 page wrapper utility
export const getA4PageClasses = (additionalClasses = ''): string => {
  return `a4-page ${additionalClasses}`.trim();
};

// Two-column layout utility
export const getTwoColumnClasses = (): string => {
  return 'two-column-layout flex flex-col md:flex-row';
};

// Sidebar content utility
export const getSidebarClasses = (colorScheme = ''): string => {
  return `sidebar-content w-full md:w-1/3 lg:w-1/3 ${colorScheme}`.trim();
};

// Main content utility  
export const getMainContentClasses = (): string => {
  return 'main-content w-full md:w-2/3 lg:w-2/3';
};

// Grid responsive utilities
export const getGridClasses = (cols: number): string => {
  const baseClass = 'grid';
  const responsiveClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };
  
  return `${baseClass} ${responsiveClasses[cols] || responsiveClasses[1]}`;
};

// Contact section responsive utility
export const getContactClasses = (): string => {
  return 'contact-section flex flex-col md:flex-row flex-wrap items-center justify-center gap-2 md:gap-4';
};

// Header responsive utility
export const getHeaderClasses = (): string => {
  return 'resume-header text-center p-4 md:p-6 lg:p-8';
};

// Photo responsive utility
export const getPhotoClasses = (): string => {
  return 'resume-photo w-20 h-25 md:w-24 md:h-32 lg:w-24 lg:h-32 object-cover mx-auto';
};

// Section spacing utility
export const getSectionSpacing = (): string => {
  return 'resume-section mb-4 md:mb-6 lg:mb-8';
};

// Typography responsive utilities
export const getTitleClasses = (level: 1 | 2 | 3 | 4 = 1): string => {
  const classes = {
    1: 'text-2xl md:text-3xl lg:text-3xl font-bold',
    2: 'text-lg md:text-xl lg:text-xl font-bold section-title',
    3: 'text-base md:text-lg lg:text-lg font-semibold',
    4: 'text-sm md:text-base lg:text-base font-medium'
  };
  
  return classes[level];
};

export const getTextClasses = (size: 'xs' | 'sm' | 'base' | 'lg' = 'sm'): string => {
  const classes = {
    'xs': 'text-xs md:text-xs lg:text-xs',
    'sm': 'text-xs md:text-sm lg:text-sm', 
    'base': 'text-sm md:text-base lg:text-base',
    'lg': 'text-base md:text-lg lg:text-lg'
  };
  
  return classes[size];
};
