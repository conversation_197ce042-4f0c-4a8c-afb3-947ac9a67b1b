/**
 * Ollama Client for Local LLM Integration
 *
 * This module provides a client interface for integrating with Ollama,
 * a local LLM server for running language models on your machine.
 *
 * Features:
 * - Connection management with health checks
 * - Request/response handling with proper error management
 * - Streaming and non-streaming support
 * - Automatic retry logic with exponential backoff
 * - Model management and validation
 *
 * <AUTHOR> AI Team
 * @version 1.0.0
 */

interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  stream?: boolean;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    max_tokens?: number;
    stop?: string[];
    seed?: number;
  };
}

interface OllamaGenerateResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

interface OllamaModelInfo {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
}

interface OllamaListResponse {
  models: OllamaModelInfo[];
}

export class OllamaClientError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
  ) {
    super(message);
    this.name = "OllamaClientError";
  }
}

export class OllamaClient {
  private baseUrl: string;
  private defaultModel: string;
  private timeout: number;
  private maxRetries: number;

  constructor(
    config: {
      baseUrl?: string;
      defaultModel?: string;
      timeout?: number;
      maxRetries?: number;
    } = {},
  ) {
    this.baseUrl = config.baseUrl || process.env.OLLAMA_BASE_URL || "http://localhost:11434";
    this.defaultModel = config.defaultModel || process.env.OLLAMA_DEFAULT_MODEL || "llama2";
    this.timeout = config.timeout || 30000; // 30 seconds
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * Check if Ollama server is running and accessible
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: "GET",
        signal: AbortSignal.timeout(5000), // 5 second timeout for health check
      });
      return response.ok;
    } catch (error) {
      console.warn("Ollama health check failed:", error);
      return false;
    }
  }

  /**
   * List all available models
   */
  async listModels(): Promise<OllamaModelInfo[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: "GET",
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        throw new OllamaClientError(`Failed to list models: ${response.statusText}`, response.status);
      }

      const data: OllamaListResponse = await response.json();
      return data.models;
    } catch (error) {
      if (error instanceof OllamaClientError) {
        throw error;
      }
      throw new OllamaClientError(
        `Network error while listing models: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Check if a specific model is available
   */
  async isModelAvailable(modelName: string): Promise<boolean> {
    try {
      const models = await this.listModels();
      return models.some((model) => model.name === modelName || model.name.startsWith(`${modelName}:`));
    } catch (error) {
      console.warn(`Failed to check model availability for ${modelName}:`, error);
      return false;
    }
  }

  /**
   * Generate text using Ollama
   */
  async generate(
    prompt: string,
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      stopSequences?: string[];
    } = {},
  ): Promise<string> {
    const model = options.model || this.defaultModel;

    // Check if model is available
    const modelAvailable = await this.isModelAvailable(model);
    if (!modelAvailable) {
      throw new OllamaClientError(
        `Model "${model}" not found. Please ensure it's downloaded in Ollama.`,
        404,
        "MODEL_NOT_FOUND",
      );
    }

    const request: OllamaGenerateRequest = {
      model,
      prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        stop: options.stopSequences,
      },
    };

    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => "Unknown error");
        throw new OllamaClientError(`Ollama API error: ${response.statusText} - ${errorText}`, response.status);
      }

      const data: OllamaGenerateResponse = await response.json();

      if (!data.response) {
        throw new OllamaClientError("Empty response from Ollama", 500, "EMPTY_RESPONSE");
      }

      return data.response.trim();
    });
  }

  /**
   * Generate text with streaming support
   */
  async *generateStream(
    prompt: string,
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      stopSequences?: string[];
    } = {},
  ): AsyncGenerator<string, void, unknown> {
    const model = options.model || this.defaultModel;

    const request: OllamaGenerateRequest = {
      model,
      prompt,
      stream: true,
      options: {
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        stop: options.stopSequences,
      },
    };

    const response = await fetch(`${this.baseUrl}/api/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "Unknown error");
      throw new OllamaClientError(`Ollama API error: ${response.statusText} - ${errorText}`, response.status);
    }

    if (!response.body) {
      throw new OllamaClientError("No response body from Ollama", 500);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          try {
            const data: OllamaGenerateResponse = JSON.parse(line);
            if (data.response) {
              yield data.response;
            }
            if (data.done) {
              return;
            }
          } catch (e) {}
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Execute a request with retry logic
   */
  private async executeWithRetry<T>(operation: () => Promise<T>, attempt: number = 1): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (attempt >= this.maxRetries) {
        throw error;
      }

      // Only retry on network errors or server errors (5xx)
      if (error instanceof OllamaClientError && error.status && error.status < 500) {
        throw error;
      }

      // Exponential backoff: 1s, 2s, 4s
      const delay = 2 ** (attempt - 1) * 1000;
      await new Promise((resolve) => setTimeout(resolve, delay));

      return this.executeWithRetry(operation, attempt + 1);
    }
  }

  /**
   * Pull/download a model from Ollama registry
   */
  async pullModel(modelName: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/pull`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ name: modelName }),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "Unknown error");
      throw new OllamaClientError(
        `Failed to pull model ${modelName}: ${response.statusText} - ${errorText}`,
        response.status,
      );
    }

    // Note: This is a simple implementation. For production,
    // you might want to handle the streaming response to show progress
  }
}

// Import AI configuration
import { aiConfig } from "./ai-config";

// Export singleton instance with configuration
export const ollamaClient = new OllamaClient({
  baseUrl: aiConfig.ollama.baseUrl,
  defaultModel: aiConfig.ollama.model,
  timeout: aiConfig.ollama.timeout,
  maxRetries: aiConfig.ollama.maxRetries,
});

// Export default configuration
export const OLLAMA_CONFIG = {
  RECOMMENDED_MODELS: [
    "llama2", // Meta's Llama 2 - Good general purpose
    "llama2:13b", // Larger Llama 2 model - Better quality
    "codellama", // Code-focused model
    "mistral", // Mistral 7B - Fast and efficient
    "neural-chat", // Intel's neural chat model
    "starling-lm", // Berkeley's Starling model
  ],
  DEFAULT_SETTINGS: {
    temperature: 0.7,
    maxTokens: 1000,
    timeout: 30000,
    maxRetries: 3,
  },
  HEALTH_CHECK_INTERVAL: 60000, // 1 minute
} as const;
