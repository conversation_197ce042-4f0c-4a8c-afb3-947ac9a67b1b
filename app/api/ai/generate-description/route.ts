import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/auth-clerk";
import { OllamaClientError, ollamaClient } from "@/lib/ollama-client";
import { addRateLimitHeaders, RateLimiterError, rateLimitManager } from "@/lib/rate-limiter";

interface GenerateDescriptionRequest {
  contextType:
    | "experience"
    | "project"
    | "education"
    | "award"
    | "certification"
    | "hobby"
    | "volunteering"
    | "reference"
    | "bio";
  resumeContext: {
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    bio?: string;
    skills?: Array<{ name: string; category?: string }>;
  };
  itemContext: {
    title?: string;
    company?: string;
    organization?: string;
    institution?: string;
    startDate?: string;
    endDate?: string;
    isCurrent?: boolean;
    role?: string;
    degree?: string;
    fieldOfStudy?: string;
    issuer?: string;
    currentDescription?: string;
  };
  options: {
    tone: "professional" | "casual" | "technical";
    length: "short" | "medium" | "detailed";
    language?: "en" | "ar";
  };
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const user = await requireAuth();

    // Check rate limits
    const isPremium = user.isPremium || false; // Assuming isPremium field exists
    const rateLimitResult = await rateLimitManager.checkAILimit(user.clerkId, isPremium);

    const body: GenerateDescriptionRequest = await request.json();
    const { contextType, resumeContext, itemContext, options } = body;

    // Validate required fields
    if (!contextType || !options) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Generate context-aware prompt
    const prompt = await generatePrompt(contextType, resumeContext, itemContext, options);

    // Call local LLM service
    const generatedDescription = await callLocalLLM(prompt, options);

    // Create response with rate limit headers
    const response = NextResponse.json({
      success: true,
      description: generatedDescription,
      contextType,
      prompt: process.env.NODE_ENV === "development" ? prompt : undefined,
    });

    // Add rate limit headers
    addRateLimitHeaders(response.headers, rateLimitResult, 60 * 60 * 1000); // 1 hour window

    return response;
  } catch (error) {
    console.error("AI description generation error:", error);

    // Handle rate limit errors
    if (error instanceof RateLimiterError) {
      const response = NextResponse.json(
        {
          error: error.message,
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((error.rateLimitResult.resetTime - Date.now()) / 1000),
        },
        { status: 429 },
      );

      // Add rate limit headers
      addRateLimitHeaders(response.headers, error.rateLimitResult, 60 * 60 * 1000);
      return response;
    }

    // Handle specific LLM errors
    if (error instanceof OllamaClientError) {
      const status = error.status || 500;
      const message = error.code === "MODEL_NOT_FOUND" ? "AI model not found" : "AI service unavailable";

      return NextResponse.json({ error: message, code: error.code }, { status });
    }

    return NextResponse.json({ error: "Failed to generate description" }, { status: 500 });
  }
}

async function generatePrompt(
  contextType: string,
  resumeContext: any,
  itemContext: any,
  options: any,
): Promise<string> {
  const { tone, length } = options;

  // Get base prompt for context type
  let basePrompt = getBasePrompt(contextType);

  // Add tone and length guidance
  const toneGuidance = getToneGuidance(tone);
  const lengthGuidance = getLengthGuidance(length);

  basePrompt += ` ${toneGuidance} ${lengthGuidance}.`;

  // Add context-specific details
  switch (contextType) {
    case "experience":
      if (itemContext.title && itemContext.company) {
        basePrompt += `\n\nPosition: ${itemContext.title}\nCompany: ${itemContext.company}`;
      }
      if (itemContext.startDate) {
        const period = itemContext.isCurrent
          ? `${itemContext.startDate} to Present`
          : `${itemContext.startDate} to ${itemContext.endDate || ""}`;
        basePrompt += `\nPeriod: ${period}`;
      }
      break;

    case "project":
      if (itemContext.title) {
        basePrompt += `\n\nProject: ${itemContext.title}`;
      }
      if (itemContext.client) {
        basePrompt += `\nClient: ${itemContext.client}`;
      }
      break;

    case "education":
      if (itemContext.degree && itemContext.institution) {
        basePrompt += `\n\nDegree: ${itemContext.degree}\nInstitution: ${itemContext.institution}`;
      }
      if (itemContext.fieldOfStudy) {
        basePrompt += `\nField of Study: ${itemContext.fieldOfStudy}`;
      }
      break;

    case "award":
    case "certification":
      if (itemContext.title && itemContext.issuer) {
        basePrompt += `\n\nTitle: ${itemContext.title}\nIssuer: ${itemContext.issuer}`;
      }
      break;

    case "volunteering":
      if (itemContext.role && itemContext.organization) {
        basePrompt += `\n\nRole: ${itemContext.role}\nOrganization: ${itemContext.organization}`;
      }
      break;

    case "bio":
      if (resumeContext.jobTitle) {
        basePrompt += `\n\nJob Title: ${resumeContext.jobTitle}`;
      }
      break;
  }

  // Add resume context if available
  if (resumeContext.skills && resumeContext.skills.length > 0) {
    const skillsList = resumeContext.skills.map((skill: any) => skill.name).join(", ");
    basePrompt += `\n\nRelevant Skills: ${skillsList}`;
  }

  // Add current description for improvement
  if (itemContext.currentDescription) {
    basePrompt += `\n\nCurrent Description: ${itemContext.currentDescription}`;
  }

  // Add formatting instructions
  basePrompt += `\n\nPlease format the response as HTML with appropriate paragraph and list tags. Focus on achievements and impact.`;

  return basePrompt;
}

function getBasePrompt(contextType: string): string {
  const prompts = {
    experience:
      "Write a professional work experience description that highlights key responsibilities, achievements, and impact.",
    project:
      "Create a compelling project description that showcases technical skills, problem-solving abilities, and project outcomes.",
    education:
      "Write an educational background description that emphasizes academic achievements, relevant coursework, and skills gained.",
    award: "Create a concise award description that highlights the significance and impact of this recognition.",
    certification: "Write a professional certification description that emphasizes the skills and knowledge validated.",
    hobby: "Create an engaging hobby description that demonstrates personal interests and transferable skills.",
    volunteering:
      "Write a meaningful volunteer experience description that shows community involvement and developed skills.",
    reference: "Create a professional reference description highlighting the relationship and context.",
    bio: "Write a compelling professional bio that captures career highlights, expertise, and personal brand.",
  };
  return prompts[contextType as keyof typeof prompts] || "Create a professional description for this resume section.";
}

function getToneGuidance(tone: string): string {
  const guidance = {
    professional: "Use formal, business-appropriate language with industry terminology.",
    casual: "Write in a conversational, approachable tone while maintaining professionalism.",
    technical: "Focus on technical details, methodologies, and quantifiable results.",
  };
  return guidance[tone as keyof typeof guidance] || "Maintain a professional tone.";
}

function getLengthGuidance(length: string): string {
  const guidance = {
    short: "Keep it concise - 2-3 sentences focusing on key points.",
    medium: "Provide moderate detail - 4-6 sentences with specific examples.",
    detailed: "Include comprehensive information - multiple paragraphs with extensive detail.",
  };
  return guidance[length as keyof typeof guidance] || "Provide appropriate detail level.";
}

async function callLocalLLM(prompt: string, options: any): Promise<string> {
  try {
    // Health check first
    const isHealthy = await ollamaClient.healthCheck();
    if (!isHealthy) {
      throw new OllamaClientError("Ollama service is unavailable", 503, "SERVICE_UNAVAILABLE");
    }

    // Get model from environment or use default
    const model = process.env.OLLAMA_MODEL || "llama2";

    // Configure generation settings based on context
    const temperature = getTemperatureForTone(options.tone);
    const maxTokens = getMaxTokensForLength(options.length);

    // Generate response using Ollama
    const response = await ollamaClient.generate(prompt, {
      model,
      temperature,
      maxTokens,
      stopSequences: ["Human:", "Assistant:", "\n\n---"],
    });

    // Post-process the response to ensure it's in HTML format
    return formatResponseAsHTML(response, options.language);
  } catch (error) {
    console.error("Local LLM call failed:", error);

    // Re-throw OllamaClientError as-is
    if (error instanceof OllamaClientError) {
      throw error;
    }

    // Wrap other errors
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new OllamaClientError(`Generation failed: ${errorMessage}`, 500, "GENERATION_FAILED");
  }
}

/**
 * Get temperature setting based on requested tone
 */
function getTemperatureForTone(tone: string): number {
  switch (tone) {
    case "professional":
      return 0.5; // More focused and consistent
    case "casual":
      return 0.8; // More creative and varied
    case "technical":
      return 0.3; // Very focused and precise
    default:
      return 0.7; // Balanced default
  }
}

/**
 * Get max tokens based on requested length
 */
function getMaxTokensForLength(length: string): number {
  switch (length) {
    case "short":
      return 200; // ~150 words
    case "medium":
      return 400; // ~300 words
    case "detailed":
      return 800; // ~600 words
    default:
      return 400; // Default to medium
  }
}

/**
 * Format the LLM response as HTML
 */
function formatResponseAsHTML(response: string, language: string = "en"): string {
  // Remove any potential markdown formatting
  let formatted = response
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>") // Bold
    .replace(/\*(.*?)\*/g, "<em>$1</em>") // Italic
    .replace(/^- (.*$)/gim, "<li>$1</li>") // List items
    .replace(/^\d+\. (.*$)/gim, "<li>$1</li>"); // Numbered list items

  // Handle lists
  if (formatted.includes("<li>")) {
    const listItems = formatted.match(/<li>.*?<\/li>/g);
    if (listItems) {
      const listContent = listItems.join("");
      formatted = formatted
        .replace(/<li>.*?<\/li>/g, "")
        .replace(/\s+/g, " ")
        .trim();
      formatted += `<ul>${listContent}</ul>`;
    }
  }

  // Split into paragraphs if not already formatted
  if (!formatted.includes("<p>") && !formatted.includes("<ul>")) {
    const paragraphs = formatted
      .split("\n\n")
      .filter((p) => p.trim())
      .map((p) => `<p>${p.trim()}</p>`)
      .join("");
    formatted = paragraphs;
  } else if (!formatted.includes("<p>")) {
    // Wrap in paragraph if it has other HTML but no paragraphs
    formatted = `<p>${formatted}</p>`;
  }

  // Clean up any extra whitespace
  formatted = formatted
    .replace(/\s*<\/p>\s*<p>\s*/g, "</p><p>")
    .replace(/\s*<\/li>\s*<li>\s*/g, "</li><li>")
    .trim();

  return formatted;
}
