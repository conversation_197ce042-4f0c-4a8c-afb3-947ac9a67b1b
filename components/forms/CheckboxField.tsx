"use client";

import { Checkbox } from "@heroui/react";
import React, { useState } from "react";

interface CheckboxFieldProps {
  name: string;
  defaultSelected?: boolean;
  className?: string;
  children: React.ReactNode;
  onChange?: (value: boolean) => void;
}

const CheckboxField: React.FC<CheckboxFieldProps> = ({
  name,
  defaultSelected = false,
  className,
  children,
  onChange,
}) => {
  const [isSelected, setIsSelected] = useState(defaultSelected);

  return (
    <>
      <Checkbox
        className={className}
        isSelected={isSelected}
        onValueChange={(value) => {
          setIsSelected(value);
          if (onChange) {
            onChange(value);
          }
        }}
      >
        {children}
      </Checkbox>
      <input name={name} type="hidden" value={isSelected ? "true" : "false"} />
    </>
  );
};

export default CheckboxField;
