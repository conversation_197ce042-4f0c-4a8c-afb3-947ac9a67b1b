"use client";

import { useUser } from "@clerk/nextjs";
import { trpc } from "@/app/_trpc/client";

export function useCurrentUser() {
  const { user: clerkUser, isLoaded: isClerkLoaded } = useUser();

  // Fetch the user's premium status from our database
  const { data: userData, isLoading: isUserLoading } = trpc.user.getCurrentUser.useQuery(undefined, {
    enabled: !!clerkUser && isClerkLoaded,
  });

  return {
    user: userData,
    isPremium: userData?.isPremium ?? false,
    isLoading: !isClerkLoaded || isUserLoading,
  };
}
