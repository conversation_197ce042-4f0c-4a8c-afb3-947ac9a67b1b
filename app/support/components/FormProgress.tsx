import { Progress } from "@heroui/progress";
import styles from "../styles/contact.module.css";

interface FormProgressProps {
  progress: number;
}

export default function FormProgress({ progress }: FormProgressProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-500">Progress</span>
      <Progress
        value={progress}
        className={`${styles.progressBar} w-20`}
        color={progress === 100 ? "success" : "primary"}
        size="sm"
      />
    </div>
  );
}
