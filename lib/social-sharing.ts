export interface SocialShareData {
  fullName: string;
  jobTitle: string;
  resumeUrl: string;
  websiteUrl?: string;
  customMessage?: string;
}

export class SocialMediaSharing {
  static generateLinkedInShareUrl(data: SocialShareData): string {
    const defaultMessage = `Check out ${data.fullName}'s professional resume. ${data.jobTitle} looking for new opportunities.`;
    const message = data.customMessage || defaultMessage;

    const params = new URLSearchParams({
      mini: "true",
      url: data.resumeUrl,
      title: `${data.fullName} - ${data.jobTitle}`,
      summary: message,
      source: "QuickCV",
    });

    return `https://www.linkedin.com/sharing/share-offsite/?${params.toString()}`;
  }

  static generateTwitterShareUrl(data: SocialShareData): string {
    const defaultMessage = `Check out my professional resume: ${data.fullName} - ${data.jobTitle}`;
    const message = data.customMessage || defaultMessage;

    const tweetText = `${message}\n\n${data.resumeUrl}\n\n#Resume #JobSearch #Career`;

    const params = new URLSearchParams({
      text: tweetText,
    });

    return `https://twitter.com/intent/tweet?${params.toString()}`;
  }

  static generateFacebookShareUrl(data: SocialShareData): string {
    const params = new URLSearchParams({
      u: data.resumeUrl,
      quote: data.customMessage || `${data.fullName} - ${data.jobTitle}`,
    });

    return `https://www.facebook.com/sharer/sharer.php?${params.toString()}`;
  }

  static generateWhatsAppShareUrl(data: SocialShareData): string {
    const defaultMessage = `Check out ${data.fullName}'s resume: ${data.jobTitle}`;
    const message = data.customMessage || defaultMessage;
    const text = `${message}\n\n${data.resumeUrl}`;

    const params = new URLSearchParams({
      text: text,
    });

    return `https://wa.me/?${params.toString()}`;
  }

  static copyToClipboard(text: string): Promise<boolean> {
    if (navigator.clipboard && window.isSecureContext) {
      return navigator.clipboard
        .writeText(text)
        .then(() => true)
        .catch(() => false);
    } else {
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand("copy");
        document.body.removeChild(textArea);
        return Promise.resolve(successful);
      } catch (err) {
        document.body.removeChild(textArea);
        return Promise.resolve(false);
      }
    }
  }

  static generateShareableResumeUrl(resumeId: number, baseUrl: string): string {
    // For backward compatibility, return the old format
    // The actual token-based URL generation happens in the sharing router
    return `${baseUrl}/cv/${resumeId}`;
  }

  static generateEmailShareUrl(data: SocialShareData): string {
    const subject = encodeURIComponent(`Resume - ${data.fullName}`);
    const body = encodeURIComponent(
      `Hi,\n\nI wanted to share ${data.fullName}'s resume with you.\n\n` +
        `Position: ${data.jobTitle}\n\n` +
        `Resume: ${data.resumeUrl}\n\n` +
        `Best regards`,
    );

    return `mailto:?subject=${subject}&body=${body}`;
  }
}
