"use server";

import { auth } from "@clerk/nextjs/server";
import { createId } from "@paralleldrive/cuid2";
import { and, desc, eq } from "drizzle-orm";
import { db } from "@/db";
import { supportTickets } from "@/db/schema";
import { getUserFromAuth, validateAndExtractContactSupportFormData } from "./contact-support";

// Generate a unique ticket number
function generateTicketNumber(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `QCV-${timestamp}-${random}`;
}

export async function submitSupportTicket(formData: FormData) {
  try {
    // Use shared validation
    const validation = await validateAndExtractContactSupportFormData(formData, "support");

    if (!validation.success) {
      return validation;
    }

    const { name, email, subject, message, category, priority } = validation.data!;

    // Get authenticated user info
    const { userIdValue } = await getUserFromAuth();

    // Generate unique ticket number
    const ticketNumber = generateTicketNumber();

    // Insert support ticket
    const [ticket] = await db
      .insert(supportTickets)
      .values({
        ticketNumber,
        name,
        email,
        subject,
        message,
        category,
        priority,
        userId: userIdValue,
      })
      .returning();

    return { success: true, ticketNumber, ticketId: ticket.id };
  } catch (error) {
    console.error("Support ticket submission error:", error);
    return { success: false, error: "Failed to submit support ticket. Please try again." };
  }
}

export async function getUserSupportTickets(userId: string) {
  try {
    // Find user by clerk ID
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, userId),
    });

    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Get user's support tickets
    const tickets = await db.query.supportTickets.findMany({
      where: (supportTickets, { eq }) => eq(supportTickets.userId, user.id),
      orderBy: [desc(supportTickets.createdAt)],
    });

    return { success: true, tickets };
  } catch (error) {
    console.error("Error fetching user support tickets:", error);
    return { success: false, error: "Failed to fetch support tickets" };
  }
}

export async function getSupportTicketByNumber(ticketNumber: string, email?: string) {
  try {
    let whereCondition;

    if (email) {
      // Public access - require email match
      whereCondition = and(
        eq(supportTickets.ticketNumber, ticketNumber),
        eq(supportTickets.email, email.toLowerCase()),
      );
    } else {
      // Authenticated access
      const { userId } = await auth();
      if (!userId) {
        return { success: false, error: "Authentication required" };
      }

      const user = await db.query.users.findFirst({
        where: (users, { eq }) => eq(users.clerkId, userId),
      });

      if (!user) {
        return { success: false, error: "User not found" };
      }

      whereCondition = and(eq(supportTickets.ticketNumber, ticketNumber), eq(supportTickets.userId, user.id));
    }

    const ticket = await db.query.supportTickets.findFirst({
      where: whereCondition,
    });

    if (!ticket) {
      return { success: false, error: "Ticket not found" };
    }

    return { success: true, ticket };
  } catch (error) {
    console.error("Error fetching support ticket:", error);
    return { success: false, error: "Failed to fetch support ticket" };
  }
}
