"use client";

import { useEffect } from "react";
import { useSidebarStore } from "@/stores/sidebar-store";

interface LayoutWrapperProps {
  children: React.ReactNode;
}

export const LayoutWrapper = ({ children }: LayoutWrapperProps) => {
  const { isCollapsed, isMobile } = useSidebarStore();

  useEffect(() => {
    const mainContent = document.getElementById("main-content");
    if (!mainContent || isMobile) return;

    // Remove existing sidebar classes
    mainContent.classList.remove("sidebar-expanded", "sidebar-collapsed");

    // Add appropriate class based on sidebar state
    if (isCollapsed) {
      mainContent.classList.add("sidebar-collapsed");
    } else {
      mainContent.classList.add("sidebar-expanded");
    }
  }, [isCollapsed, isMobile]);

  return <>{children}</>;
};
