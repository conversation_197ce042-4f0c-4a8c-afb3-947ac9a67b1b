{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/*.css"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "lineEnding": "lf", "includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/*.css"]}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "off"}, "correctness": {"useExhaustiveDependencies": "off", "noUnusedVariables": "warn"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "complexity": {"noForEach": "off"}, "performance": {"noDelete": "off"}}, "includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}}, "json": {"formatter": {"trailingCommas": "none"}}, "css": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}