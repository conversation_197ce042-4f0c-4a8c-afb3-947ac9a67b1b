"use client";

import { Card, CardBody } from "@heroui/card";
import { Icon } from "@iconify/react";

export default function MissionSection() {
  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="inline-flex items-center gap-3 mb-6">
              <div className="p-3 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-lg">
                <Icon icon="tabler:target" className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
                Our Mission
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed mb-6">
              We believe everyone deserves a chance to showcase their talents effectively. Our mission is to democratize
              professional resume creation by providing powerful, accessible tools.
            </p>
            <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed">
              Started by professionals who understand the job market challenges, we've built QuickCV to bridge the gap
              between talent and opportunity through better resume design.
            </p>
          </div>
          <Card className="bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20 border-0">
            <CardBody className="p-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">12+</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Templates</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">2</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Languages</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">$100</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Lifetime Access</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600 mb-2">∞</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Unlimited Use</div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </section>
  );
}
