# QuickCV Documentation

Welcome to the QuickCV documentation! This guide will help you understand, set up, and contribute to the QuickCV resume builder application.

## 📚 Table of Contents

### 🚀 Getting Started
- [Main README](../README.md) - Project overview and quick start
- [CLAUDE.md](../CLAUDE.md) - Claude Code assistant instructions

### ⚙️ Setup & Configuration
- [Environment Setup](setup/ENV_SETUP.md) - Environment variables and configuration
- [Browserless Setup](setup/browserless-setup.md) - PDF generation service setup
- [Clerk Webhook Setup](setup/clerk-webhook-setup.md) - Authentication webhook configuration
- [Generating Secret Tokens](setup/generating-secret-tokens.md) - Security token generation guide

### 🎯 Features & Systems
- [Payment System](features/PAYMENT_SYSTEM.md) - Payment integration and premium features
- [AI Feature Documentation](features/AI_FEATURE_DOCUMENTATION.md) - AI-powered content generation
- [LinkedIn Import Guide](features/linkedin-import-guide.md) - LinkedIn profile import system
- [Resume Import System](features/resume-import-system.md) - Resume data import functionality
- [Email Sharing System](features/email-sharing-and-tokens-system.md) - Email sharing and token management

### 🔧 Development
- [Feature Roadmap](development/FEATURE_ROADMAP.md) - Planned features and development timeline
- [Resume Templates](development/resume-templates.md) - Template system and development guide

### 🚀 Deployment
- [Deployment Guide](deployment/DEPLOYMENT.md) - General deployment instructions
- [Coolify Deployment](deployment/COOLIFY_DEPLOYMENT.md) - Coolify-specific deployment guide

### 🔒 Security
- [Security Enhancements](security/security-enhancements.md) - Security features and best practices

## 🏗️ Architecture Overview

QuickCV is built with:
- **Framework**: Next.js 15 with TypeScript and App Router
- **Database**: SQLite (development) with Drizzle ORM and Turso
- **Authentication**: Clerk for user management
- **UI Components**: HeroUI v2 with Tailwind CSS
- **File Uploads**: UploadThing for photo uploads
- **Rich Text**: TipTap editor for descriptions

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quickcv
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Set up environment**
   - Copy `.env.example` to `.env.local`
   - Follow [Environment Setup](setup/ENV_SETUP.md) guide

4. **Run database migrations**
   ```bash
   bun run db:migrate
   ```

5. **Start development server**
   ```bash
   bun run dev
   ```

## 📝 Development Workflow

1. **Feature Development**
   - Check [Feature Roadmap](development/FEATURE_ROADMAP.md) for planned features
   - Follow existing patterns in the codebase
   - Use feature flags for new functionality

2. **Testing**
   - Test all features locally
   - Verify responsive design
   - Check accessibility compliance

3. **Documentation**
   - Update relevant documentation
   - Add new features to this index

## 🤝 Contributing

1. **Code Style**
   - Follow existing TypeScript patterns
   - Use Tailwind CSS for styling
   - Implement proper error handling

2. **Security**
   - Never commit secrets or API keys
   - Follow security best practices
   - Review [Security Enhancements](security/security-enhancements.md)

3. **Features**
   - Use the feature flag system for new features
   - Implement proper premium/free tier separation
   - Add proper translations for new text

## 📞 Support

For questions or issues:
- Check existing documentation first
- Review the codebase and comments
- Create an issue with detailed information

## 🔄 Recent Updates

- **Feature Flag System**: Comprehensive premium/free tier management
- **AI Integration**: Content generation with usage limits
- **Security Enhancements**: Token management and secure sharing
- **Payment Integration**: Lifetime premium subscription model

---

*Last updated: January 2025*