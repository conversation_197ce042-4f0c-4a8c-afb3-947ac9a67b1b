# LinkedIn Profile Import - Implementation Guide

## Overview

The LinkedIn Profile Import feature allows users to automatically populate their resumes with basic professional information from their LinkedIn profiles using auto-approved permissions. This feature imports personal information only (name, email, headline, location).

## Quick Setup Checklist

### Prerequisites ✅
- [ ] LinkedIn Developer Account
- [ ] Company LinkedIn Page (required for app creation)
- [ ] Privacy Policy URL (required for app)

### Setup Steps (2 minutes) ⏱️
1. [ ] Create LinkedIn Developer App
2. [ ] Request "Sign In with LinkedIn" permission (auto-approved)
3. [ ] Copy Client ID & Client Secret
4. [ ] Add redirect URLs to LinkedIn app
5. [ ] Set environment variables in `.env.local`
6. [ ] Test OAuth flow

### What You Get ✅
- ✅ **Personal Info**: Name, email, headline, profile picture
- ✅ **Location**: City/location information
- ⚠️ **Note**: Work experience, education, skills, and bio/summary require LinkedIn Partner Program approval and are not included in this implementation

## Architecture

```
User Action → LinkedIn OAuth → API Routes → TRPC → Database
     ↓              ↓            ↓         ↓        ↓
   UI Button → Auth Flow → Token Storage → Import → Resume Update
```

## Setup Instructions

### 1. LinkedIn App Configuration

#### Create LinkedIn Developer Application
1. Go to [LinkedIn Developer Portal](https://developer.linkedin.com/apps)
2. Click "Create App"
3. Fill in the required information:
   - **App name**: QuickCV LinkedIn Import
   - **LinkedIn Page**: Your company page (or create one)
   - **Privacy policy URL**: Your app's privacy policy
   - **App logo**: Upload your app logo

#### Configure OAuth Settings
1. In your LinkedIn app dashboard, go to "Auth" tab
2. Add authorized redirect URLs:
   ```
   Development: http://localhost:3000/api/linkedin/callback
   Production: https://yourdomain.com/api/linkedin/callback
   ```

#### Request LinkedIn API Access

1. **Go to "Products" tab** in your LinkedIn app dashboard
2. **Request access to this LinkedIn API product**:
   
   **Sign In with LinkedIn using OpenID Connect**
   - Click "Request access"
   - This provides: `openid`, `profile`, `email` scopes
   - **Auto-approved** ✅

3. **Verify scopes** in the "Auth" tab once approved

#### OAuth 2.0 Scopes Used:
```javascript
const scope = "openid profile email";
```

These scopes provide:
- ✅ `openid` - Basic OpenID Connect authentication
- ✅ `profile` - Basic profile information (name, photo, headline)
- ✅ `email` - Email address

## LinkedIn API Limitations

### What's Available with Basic Access

The LinkedIn API provides limited data with basic (auto-approved) access:

#### ✅ Available Data:
- **Name**: First and last name
- **Email**: Primary email address
- **Headline**: Professional headline/title
- **Profile Picture**: Profile photo URL
- **Location**: City/location information

#### ❌ Not Available:
- Work experience history
- Education background
- Skills and endorsements
- Recommendations
- Certifications
- Full bio/summary

### Alternative Data Sources

To complement the limited LinkedIn data, we recommend:

1. **PDF Resume Import**: Upload existing resumes to extract complete work history, education, and skills
2. **Manual Entry**: Use the resume builder forms to add missing information
3. **Smart Templates**: Pre-filled templates for common professions

#### Get Credentials
1. Copy your **Client ID** and **Client Secret** from the Auth tab
2. Keep these secure - you'll add them to your environment variables

### 2. Environment Variables Setup

Add the following variables to your `.env.local` file:

```bash
# LinkedIn OAuth Configuration
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here
LINKEDIN_REDIRECT_URI=http://localhost:3000/api/linkedin/callback


# For production, update the redirect URI:
# LINKEDIN_REDIRECT_URI=https://yourdomain.com/api/linkedin/callback
```

### 3. Database Schema (Already Implemented)

The feature uses existing database tables:
- `resumes` - Main resume data
- `experiences` - Work experience entries
- `educations` - Education entries  
- `skills` - Skills with proficiency levels

No additional database changes required.

### 4. Deployment Checklist

#### Development Environment
1. ✅ LinkedIn app created with dev redirect URL
2. ✅ Environment variables set in `.env.local`
3. ✅ Run `bun run dev` to start development server
4. ✅ Test LinkedIn import functionality

#### Production Environment
1. **Update LinkedIn App Settings**:
   - Add production redirect URL to LinkedIn app
   - Verify app is approved for production use

2. **Set Production Environment Variables**:
   ```bash
   LINKEDIN_CLIENT_ID=your_linkedin_client_id
   LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
   LINKEDIN_REDIRECT_URI=https://yourdomain.com/api/linkedin/callback
   ```

3. **Deploy Application**:
   - Deploy to your hosting platform (Vercel, Railway, etc.)
   - Verify environment variables are set correctly
   - Test OAuth flow in production

## User Flow

### For New Resume Creation
1. User clicks "Get Started" or "Create Resume"
2. User enters resume title and clicks "Create"
3. Modal shows import options with LinkedIn import button
4. User clicks "Import from LinkedIn"
5. OAuth popup opens for LinkedIn authorization
6. User authorizes the application
7. Data is imported and user sees success message
8. User is redirected to resume editor with imported data

### For Existing Resumes
1. User navigates to "My Resumes" page
2. User clicks three-dots menu on any resume card
3. User selects "Import from LinkedIn" from dropdown
4. LinkedIn import modal opens
5. User clicks "Import from LinkedIn" button
6. OAuth flow completes and data is imported
7. Page refreshes to show updated resume data

## Technical Implementation Details

### API Routes

#### `/api/linkedin/auth` (GET)
- Initiates LinkedIn OAuth flow
- Generates secure state parameter
- Redirects to LinkedIn authorization URL
- **Parameters**: None
- **Returns**: Redirect to LinkedIn

#### `/api/linkedin/callback` (GET)
- Handles OAuth callback from LinkedIn
- Exchanges authorization code for access token
- Stores token in secure HTTP-only cookie
- **Parameters**: `code`, `state` (from LinkedIn)
- **Returns**: Redirect to app with success/error

### TRPC Procedures

#### `resumes.importFromLinkedIn`
- **Input**: `{ resumeId: number }`
- **Process**: 
  1. Validates LinkedIn token from cookies
  2. Fetches LinkedIn profile data
  3. Parses and transforms data to resume format
  4. Updates database with imported information
- **Returns**: Success status and import summary

### Components

#### `LinkedInImportButton`
- Reusable button component for LinkedIn import
- Handles OAuth flow and loading states
- Provides success/error feedback
- **Props**:
  ```typescript
  {
    resumeId: number;
    onImportSuccess?: () => void;
    variant?: "solid" | "ghost" | "flat";
    size?: "sm" | "md" | "lg";
    className?: string;
  }
  ```

## Data Mapping

### LinkedIn → Resume Mapping
```javascript
{
  // Personal Information (Available with Basic Access)
  firstName: linkedIn.firstName,
  lastName: linkedIn.lastName,
  jobTitle: linkedIn.headline,
  email: linkedIn.emailAddress,
  city: linkedIn.location,
  photo: linkedIn.profilePicture,
  
  // These fields remain empty with basic access:
  bio: "",
  experiences: [],
  educations: [],
  skills: []
}
```

### Import Summary
After importing from LinkedIn, users will see:
- ✅ Name and contact information populated
- ℹ️ Message encouraging them to complete their resume using:
  - PDF import for full work history
  - Manual forms for additional details
  - Template suggestions for their profession

## Security Considerations

### OAuth Security
- State parameter prevents CSRF attacks
- Secure HTTP-only cookies for token storage
- Short token expiration (1 hour)
- User authentication required before import

### Data Protection
- User ownership verification for all operations
- No persistent storage of LinkedIn tokens
- LinkedIn data only imported with explicit user consent
- HTTPS required for production OAuth

## Error Handling

### Common Error Scenarios
1. **LinkedIn not connected**: Prompts user to authorize
2. **Token expired**: Requires re-authorization
3. **API rate limits**: Shows appropriate error message
4. **Network failures**: Graceful fallback with retry option
5. **Invalid resume access**: Prevents unauthorized imports

### Error Messages
- User-friendly error messages in multiple languages
- Technical errors logged for debugging
- Fallback options provided for all error states

## Testing

### Manual Testing Steps
1. **New Resume Flow**:
   - Create new resume
   - Test LinkedIn import in modal
   - Verify basic contact information is imported
   - Confirm work experience, education, and skills remain empty

2. **Existing Resume Flow**:
   - Open resume actions menu
   - Test LinkedIn import option
   - Verify data merges correctly
   - Test with resumes containing existing data

3. **Error Scenarios**:
   - Test with invalid LinkedIn credentials
   - Test network connectivity issues
   - Test OAuth cancellation
   - Test unauthorized resume access

### Automated Testing
Consider adding:
- Unit tests for LinkedIn service
- Integration tests for TRPC procedures
- E2E tests for OAuth flow
- Mock LinkedIn API responses

## Monitoring & Analytics

### Key Metrics to Track
- LinkedIn import success rate
- OAuth completion rate
- Data quality metrics
- User adoption rates
- Error frequencies

### Logging
- OAuth flow events
- Import success/failure events
- API response times
- Error details with user context

## Troubleshooting

### Common Issues

#### "LinkedIn not connected" Error
- **Cause**: User hasn't authorized LinkedIn access
- **Solution**: Click LinkedIn import button to start OAuth flow

#### "Failed to import LinkedIn profile data" Error
- **Cause**: API error or network issue
- **Solution**: Try again in a few minutes, check LinkedIn app settings

#### Import Button Not Working
- **Cause**: Missing environment variables or incorrect LinkedIn app setup
- **Solution**: Verify LinkedIn app configuration and environment variables

#### Only Basic Information Imported
- **Cause**: This is expected behavior - LinkedIn only provides basic profile data with auto-approved access
- **Solution**: Use PDF import or manual entry to add work experience, education, and skills

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify environment variables are set correctly
3. Check LinkedIn app OAuth settings
4. Review server logs for API errors
5. Test OAuth flow in incognito mode

## Future Enhancements

### Potential Improvements
1. **Enhanced Manual Forms**: Smart form suggestions based on imported name and title
2. **Profile Photo Integration**: Better handling of LinkedIn profile pictures
3. **Industry Templates**: Suggest resume templates based on LinkedIn headline
4. **Import History**: Track what was imported and when

### Integration Opportunities
1. **Other Platforms**: Add similar import for Indeed, AngelList, etc.
2. **Export Features**: Export resume data back to LinkedIn
3. **AI Enhancement**: Use AI to improve imported descriptions
4. **Template Matching**: Suggest best template based on imported data

## Implementation Files

### Created Files
- `app/api/linkedin/auth/route.ts` - OAuth initiation endpoint
- `app/api/linkedin/callback/route.ts` - OAuth callback handler
- `lib/linkedin-service.ts` - LinkedIn API service and data parser
- `components/features/resume/linkedin-import-button.tsx` - Import UI component
- `types/resume.ts` - TypeScript interfaces for resume data

### Modified Files
- `app/server/routers/resumes.ts` - Added `importFromLinkedIn` TRPC procedure
- `components/features/resume/createResumeButton.tsx` - Added import options to creation flow
- `components/features/resume/resume-card-actions.tsx` - Added import option to existing resumes
- `messages/en.json` - Added LinkedIn import translations
- `.env.example` - Added LinkedIn OAuth environment variables

This comprehensive guide provides everything needed to implement and maintain the LinkedIn Profile Import feature in your QuickCV application.