import React from 'react';
import { FullResume } from '@/db/schema';
import SkillsDisplay from '@/components/resume/skills-display';

interface PikachuTemplateProps {
  resume: FullResume;
}

export default function PikachuTemplate({ resume }: PikachuTemplateProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-600 border-blue-600 bg-blue-50',
    green: 'text-green-600 bg-green-600 border-green-600 bg-green-50',
    purple: 'text-purple-600 bg-purple-600 border-purple-600 bg-purple-50',
    red: 'text-red-600 bg-red-600 border-red-600 bg-red-50',
    orange: 'text-orange-600 bg-orange-600 border-orange-600 bg-orange-50',
    teal: 'text-teal-600 bg-teal-600 border-teal-600 bg-teal-50',
    pink: 'text-pink-600 bg-pink-600 border-pink-600 bg-pink-50',
    indigo: 'text-indigo-600 bg-indigo-600 border-indigo-600 bg-indigo-50',
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div className={`max-w-4xl mx-auto bg-white shadow-lg font-${resume.fontFamily || 'inter'} text-gray-900 print:shadow-none rounded-lg overflow-hidden`}>
      {/* Modern header with excellent ATS compatibility */}
      <header className={`${selectedColor.split(' ')[3]} px-8 py-10 border-b border-gray-200`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Modern and highly readable */}
            <h1 className="text-5xl font-bold text-gray-900 mb-3 leading-tight tracking-tight">
              {resume.firstName} {resume.lastName}
            </h1>
            
            {/* Job Title - Clear hierarchy */}
            <h2 className={`text-2xl font-semibold ${selectedColor.split(' ')[0]} mb-6 leading-relaxed`}>
              {resume.jobTitle}
            </h2>
            
            {/* Contact Information - ATS-friendly layout */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm font-medium text-gray-700">
              {resume.email && (
                <div className="flex items-center">
                  <span className="mr-2">✉️</span>
                  <span>{resume.email}</span>
                </div>
              )}
              {resume.phone && (
                <div className="flex items-center">
                  <span className="mr-2">📞</span>
                  <span>{resume.phone}</span>
                </div>
              )}
              {resume.website && (
                <div className="flex items-center">
                  <span className="mr-2">🌐</span>
                  <span>{resume.website}</span>
                </div>
              )}
              {(resume.city || resume.country) && (
                <div className="flex items-center">
                  <span className="mr-2">📍</span>
                  <span>{[resume.city, resume.country].filter(Boolean).join(', ')}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Photo - Modern styling */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-36 h-36 rounded-2xl object-cover border-4 border-white shadow-xl"
              />
            </div>
          )}
        </div>
        
        {/* Professional Summary - Modern placement */}
        {resume.bio && (
          <div className="mt-8 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className={`text-lg font-semibold ${selectedColor.split(' ')[0]} mb-3 uppercase tracking-wide`}>
              Professional Summary
            </h3>
            <p className="text-base text-gray-700 leading-relaxed">
              {resume.bio}
            </p>
          </div>
        )}
      </header>

      {/* Modern two-column layout optimized for ATS */}
      <div className="flex">
        {/* Left Column - Main content */}
        <div className="w-2/3 px-8 py-8">
          {/* Experience Section - ATS optimized */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-10">
              <h3 className={`text-2xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-3 border-b-2 ${selectedColor.split(' ')[2]} uppercase tracking-wide`}>
                Professional Experience
              </h3>
              <div className="space-y-8">
                {resume.experiences.map((experience) => (
                  <div key={experience.id} className="relative bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                    {/* Job Title - Most prominent for ATS */}
                    <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">
                      {experience.title}
                    </h4>
                    
                    {/* Company and Date - Clear hierarchy */}
                    <div className="flex items-center justify-between mb-3">
                      <h5 className={`text-lg font-semibold ${selectedColor.split(' ')[0]} leading-relaxed`}>
                        {experience.company}
                      </h5>
                      <span className="text-base font-semibold text-gray-700 bg-gray-100 px-3 py-1 rounded-full">
                        {experience.startDate} - {experience.isCurrent ? 'Present' : experience.endDate}
                      </span>
                    </div>
                    
                    {/* Location */}
                    {(experience.city || experience.country) && (
                      <p className="text-sm text-gray-600 mb-4 font-medium">
                        📍 {[experience.city, experience.country].filter(Boolean).join(', ')}
                      </p>
                    )}
                    
                    {/* Description - ATS-friendly formatting */}
                    {experience.description && (
                      <div
                        className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section - Modern layout */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-10">
              <h3 className={`text-2xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-3 border-b-2 ${selectedColor.split(' ')[2]} uppercase tracking-wide`}>
                Key Projects
              </h3>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div key={project.id} className="bg-gradient-to-r from-white to-gray-50 rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                    {/* Project Title */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">
                      {project.title}
                    </h4>
                    
                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-3">
                      {project.client && (
                        <h5 className={`text-base font-semibold ${selectedColor.split(' ')[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm font-semibold text-gray-700 bg-gray-100 px-3 py-1 rounded-full">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>
                    
                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a href={project.url} className={`${selectedColor.split(' ')[0]} hover:underline font-medium flex items-center`}>
                          🔗 {project.url}
                        </a>
                      </p>
                    )}
                    
                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Sidebar with modern cards */}
        <div className="w-1/3 bg-gray-50 px-6 py-8 border-l border-gray-200">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-4 uppercase tracking-wide`}>
                Education
              </h3>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                    {/* Degree - Prominent */}
                    <h4 className="text-base font-bold text-gray-900 mb-1 leading-tight">
                      {education.degree}
                    </h4>
                    
                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-sm font-semibold text-gray-700 mb-2">
                        {education.fieldOfStudy}
                      </p>
                    )}
                    
                    {/* Institution */}
                    <h5 className={`text-sm font-semibold ${selectedColor.split(' ')[0]} mb-2`}>
                      {education.institution}
                    </h5>
                    
                    {/* Date and Location */}
                    <div className="text-xs text-gray-600 space-y-1">
                      <p className="font-medium">
                        {education.startDate} - {education.isCurrent ? 'Present' : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>📍 {[education.city, education.country].filter(Boolean).join(', ')}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Modern visualization */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-4 uppercase tracking-wide`}>
                Skills
              </h3>
              <SkillsDisplay
                skills={resume.skills}
                colorScheme={resume.colorScheme}
                variant="dots"
                groupByCategory={true}
                maxColumns={1}
                showProficiency={true}
              />
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-4 uppercase tracking-wide`}>
                Languages
              </h3>
              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-sm font-semibold text-gray-800">{language.name}</span>
                      <span className="text-xs font-bold text-white bg-gray-800 px-2 py-1 rounded-full">
                        {language.proficiency}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${selectedColor.split(' ')[1]} shadow-sm`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications Section */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-4 uppercase tracking-wide`}>
                Certifications
              </h3>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">
                      {cert.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-medium">{cert.issuer}</p>
                    {cert.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards Section */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-4 uppercase tracking-wide`}>
                Awards & Recognition
              </h3>
              <div className="space-y-3">
                {resume.awards.map((award) => (
                  <div key={award.id} className="bg-white rounded-lg p-3 shadow-sm border border-gray-200">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">
                      {award.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-medium">{award.issuer}</p>
                    {award.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1">{award.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}