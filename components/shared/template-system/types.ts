import React from "react";
import { FullResume, Website } from "@/db/schema";

export interface BaseTemplateProps {
  className?: string;
}

export interface ResumeTemplateProps extends BaseTemplateProps {
  resume: FullResume; // Define proper type based on your schema
}

export interface WebsiteTemplateProps extends BaseTemplateProps {
  resume: FullResume;
  website: Website;
}

export interface TemplateRegistryEntry<T = any> {
  id: string;
  slug: string;
  name: string;
  component: React.FC<T>;
  category?: string;
  tags?: string[];
}

export interface TemplateRendererProps<T = any> {
  templateSlug: string;
  templateProps: T;
  fallbackComponent?: React.FC<T>;
  className?: string;
}

export interface TemplateSelectorProps {
  templates: TemplateRegistryEntry[];
  selectedTemplate?: string;
  onTemplateSelect: (slug: string) => void;
  className?: string;
}
