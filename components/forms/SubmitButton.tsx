// submit-button.tsx
"use client";

import { Button, Progress } from "@heroui/react";
import { useFormStatus } from "react-dom";
import { useLoadingStore } from "@/lib/loading-store";

export default function SubmitButton({
  children,
  className,
  size,
  progress,
  progressLabel,
  loadingId,
  globalLoading = false,
}: {
  children?: React.ReactNode;
  className?: string;
  size?: "lg" | "md" | "sm";
  progress?: number; // 0-100 for determinate progress
  progressLabel?: string;
  loadingId?: string;
  globalLoading?: boolean;
}) {
  const { pending } = useFormStatus();
  const { isLoading: isGlobalLoading, isGlobalLoading: hasAnyLoading } = useLoadingStore();

  // Check for loading from multiple sources
  const isLoading =
    pending || (loadingId ? isGlobalLoading(loadingId) : false) || (globalLoading ? hasAnyLoading : false);

  const showProgress = isLoading;

  return (
    <div className="flex flex-col gap-2">
      <Button
        className={className}
        color="primary"
        disabled={isLoading}
        fullWidth={true}
        isLoading={isLoading}
        size={size}
        type="submit"
      >
        {children}
      </Button>
      {showProgress && (
        <Progress
          aria-label={progressLabel || "Form submission progress"}
          color="primary"
          size="sm"
          value={progress}
          showValueLabel={true}
          className="w-full"
          label={progressLabel}
        />
      )}
    </div>
  );
}
