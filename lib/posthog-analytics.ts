// Conditional import to avoid build errors
let posthog: any;
if (typeof window !== "undefined") {
  try {
    posthog = require("posthog-js").posthog;
  } catch (error) {
    console.warn("PostHog not available:", error);
  }
}

/**
 * PostHog Analytics Integration Layer
 * Simple wrapper functions for PostHog event tracking
 */

export interface PostHogEventProperties {
  [key: string]: any;
}

export interface FeatureEventProperties extends PostHogEventProperties {
  featureId: string;
  action: "accessed" | "used" | "blocked" | "error" | "shown" | "clicked" | "dismissed";
  isPremium?: boolean;
  remainingUses?: number;
  requiresUpgrade?: boolean;
  blockReason?: string;
  sessionId?: string;
  userAgent?: string;
  duration?: number;
  promptType?: string;
  canAccess?: boolean;
}

/**
 * Track feature flag events to PostHog
 */
export function trackFeatureEvent(eventName: string, properties: FeatureEventProperties): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.capture(eventName, {
      ...properties,
      timestamp: new Date().toISOString(),
      category: "feature_flags",
    });
  } catch (error) {
    console.error("Error tracking feature event to PostHog:", error);
  }
}

/**
 * Track feature access events
 */
export function trackFeatureAccess(
  featureId: string,
  canAccess: boolean,
  reason?: string,
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("feature_access_check", {
    featureId,
    action: canAccess ? "accessed" : "blocked",
    canAccess,
    blockReason: reason,
    ...properties,
  });
}

/**
 * Track feature usage events
 */
export function trackFeatureUsage(
  featureId: string,
  action: "accessed" | "used" | "blocked" | "error",
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("feature_usage", {
    featureId,
    action,
    ...properties,
  });
}

/**
 * Track upgrade prompt events
 */
export function trackUpgradePrompt(
  featureId: string,
  action: "shown" | "clicked" | "dismissed",
  promptType?: string,
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("upgrade_prompt", {
    featureId,
    action,
    promptType,
    ...properties,
  });
}

/**
 * Track conversion events
 */
export function trackConversion(conversionType: string, value?: number, properties?: PostHogEventProperties): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.capture("conversion", {
      conversionType,
      value,
      ...properties,
      category: "conversions",
    });
  } catch (error) {
    console.error("Error tracking conversion to PostHog:", error);
  }
}

/**
 * Identify user in PostHog (called when user signs in)
 */
export function identifyUser(
  userId: string,
  properties?: {
    email?: string;
    isPremium?: boolean;
    createdAt?: string;
    [key: string]: any;
  },
): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.identify(userId, properties);
  } catch (error) {
    console.error("Error identifying user in PostHog:", error);
  }
}

/**
 * Set user properties
 */
export function setUserProperties(properties: PostHogEventProperties): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.setPersonProperties(properties);
  } catch (error) {
    console.error("Error setting user properties in PostHog:", error);
  }
}

/**
 * Track page views
 */
export function trackPageView(page: string, properties?: PostHogEventProperties): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.capture("$pageview", {
      $current_url: window.location.href,
      page,
      ...properties,
    });
  } catch (error) {
    console.error("Error tracking page view to PostHog:", error);
  }
}

/**
 * Track custom events
 */
export function trackEvent(eventName: string, properties?: PostHogEventProperties): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.capture(eventName, properties);
  } catch (error) {
    console.error("Error tracking event to PostHog:", error);
  }
}

/**
 * PostHog Feature Flags Native API Integration
 */

/**
 * Check if a feature flag is enabled
 * @param flagKey - The feature flag key
 * @param defaultValue - Default value if flag is not found
 * @returns boolean indicating if flag is enabled
 */
export function isFeatureFlagEnabled(flagKey: string, defaultValue: boolean = false): boolean {
  if (typeof window === "undefined" || !posthog) return defaultValue;

  try {
    return posthog.isFeatureEnabled(flagKey, defaultValue);
  } catch (error) {
    console.error("Error checking feature flag:", error);
    return defaultValue;
  }
}

/**
 * Get feature flag payload/variant
 * @param flagKey - The feature flag key
 * @param defaultValue - Default payload if flag is not found
 * @returns Feature flag payload
 */
export function getFeatureFlagPayload(flagKey: string, defaultValue?: any): any {
  if (typeof window === "undefined" || !posthog) return defaultValue;

  try {
    return posthog.getFeatureFlagPayload(flagKey) || defaultValue;
  } catch (error) {
    console.error("Error getting feature flag payload:", error);
    return defaultValue;
  }
}

/**
 * Get feature flag variant key
 * @param flagKey - The feature flag key
 * @param defaultValue - Default variant if flag is not found
 * @returns Feature flag variant key
 */
export function getFeatureFlagVariant(flagKey: string, defaultValue?: string): string | boolean {
  if (typeof window === "undefined" || !posthog) return defaultValue || false;

  try {
    return posthog.getFeatureFlag(flagKey) || defaultValue || false;
  } catch (error) {
    console.error("Error getting feature flag variant:", error);
    return defaultValue || false;
  }
}

/**
 * Force reload feature flags from PostHog
 * Useful when user context changes
 */
export function reloadFeatureFlags(): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.reloadFeatureFlags();
  } catch (error) {
    console.error("Error reloading feature flags:", error);
  }
}

/**
 * Set up feature flag callbacks
 * @param callback - Function to call when feature flags are loaded
 */
export function onFeatureFlags(callback: (flags: Record<string, boolean | string>) => void): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.onFeatureFlags(callback);
  } catch (error) {
    console.error("Error setting up feature flag callback:", error);
  }
}

/**
 * Track feature flag usage events
 * @param flagKey - The feature flag key
 * @param variant - The variant that was shown
 * @param properties - Additional properties
 */
export function trackFeatureFlagUsage(
  flagKey: string,
  variant: string | boolean,
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("feature_flag_used", {
    featureId: flagKey,
    action: "used",
    variant: variant,
    flagKey,
    ...properties,
  });
}

/**
 * Track feature flag exposure (when flag is evaluated)
 * @param flagKey - The feature flag key
 * @param variant - The variant that was returned
 * @param properties - Additional properties
 */
export function trackFeatureFlagExposure(
  flagKey: string,
  variant: string | boolean,
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("feature_flag_exposure", {
    featureId: flagKey,
    action: "shown",
    variant: variant,
    flagKey,
    exposureType: "automatic",
    ...properties,
  });
}

/**
 * Track A/B test conversion events
 * @param flagKey - The feature flag key
 * @param variant - The variant
 * @param conversionEvent - The conversion event name
 * @param value - Optional conversion value
 * @param properties - Additional properties
 */
export function trackABTestConversion(
  flagKey: string,
  variant: string | boolean,
  conversionEvent: string,
  value?: number,
  properties?: PostHogEventProperties,
): void {
  trackFeatureEvent("ab_test_conversion", {
    featureId: flagKey,
    action: "used",
    variant: variant,
    flagKey,
    conversionEvent,
    conversionValue: value,
    eventType: "conversion",
    ...properties,
  });
}

/**
 * Get all active feature flags for the current user
 * @returns Object with flag keys and their values
 */
export function getAllFeatureFlags(): Record<string, boolean | string> {
  if (typeof window === "undefined" || !posthog) return {};

  try {
    // PostHog doesn't have a direct method to get all flags,
    // but we can check specific known flags
    const knownFlags = [
      "pdf_export_v2",
      "website_builder",
      "ai_generation_enhanced",
      "premium_templates",
      "analytics_dashboard",
      "social_sharing",
      "real_time_collaboration",
      "custom_branding",
      "advanced_export",
      "template_customization",
      "resume_scoring",
      "job_matching",
      "interview_prep",
      "portfolio_builder",
      "referral_program",
    ];

    const flags: Record<string, boolean | string> = {};

    knownFlags.forEach((flag) => {
      try {
        flags[flag] = posthog.getFeatureFlag(flag) || false;
      } catch (error) {
        console.warn(`Error getting flag ${flag}:`, error);
        flags[flag] = false;
      }
    });

    return flags;
  } catch (error) {
    console.error("Error getting all feature flags:", error);
    return {};
  }
}

/**
 * Force reload all feature flags and call callback when ready
 * @param callback - Function to call when flags are reloaded
 */
export function reloadFeatureFlagsWithCallback(callback?: (flags: Record<string, boolean | string>) => void): void {
  if (typeof window === "undefined" || !posthog) {
    callback?.({});
    return;
  }

  try {
    if (callback) {
      onFeatureFlags((flags) => {
        callback(flags);
      });
    }
    reloadFeatureFlags();
  } catch (error) {
    console.error("Error reloading feature flags with callback:", error);
    callback?.({});
  }
}

/**
 * Reset PostHog (called on logout)
 */
export function resetAnalytics(): void {
  if (typeof window === "undefined" || !posthog) return;

  try {
    posthog.reset();
  } catch (error) {
    console.error("Error resetting PostHog:", error);
  }
}
