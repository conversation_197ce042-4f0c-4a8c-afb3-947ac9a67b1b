import React from 'react';
import { FullResume } from '@/db/schema';

// Import all template components
import AzurillTemplate from './azurill';
import BronzorTemplate from './bronzor';
import ChikoritaTemplate from './chikorita';
import DittoTemplate from './ditto';
import GengarTemplate from './gengar';
import GlalieTemplate from './glalie';
import KakunaTemplate from './kakuna';
import LeafishTemplate from './leafish';
import NosepassTemplate from './nosepass';
import OnyxTemplate from './onyx';
import PikachuTemplate from './pikachu';
import RhyhornTemplate from './rhyhorn';

export interface TemplateComponentProps {
  resume: FullResume;
}

export interface TemplateInfo {
  id: string;
  name: string;
  slug: string;
  category: string;
  atsScore: number;
  features: string;
  description: string;
  component: React.ComponentType<TemplateComponentProps>;
  layout: 'single-column' | 'two-column' | 'asymmetric';
  style: 'classic' | 'modern' | 'creative' | 'professional' | 'minimalist';
}

export const templateRegistry: Record<string, TemplateInfo> = {
  azurill: {
    id: 'azurill',
    name: 'Azurill',
    slug: 'azurill',
    category: 'modern',
    atsScore: 85,
    features: 'Clean, professional two-column layout with timeline design',
    description: 'A modern, clean template with excellent readability and professional styling',
    component: AzurillTemplate,
    layout: 'two-column',
    style: 'modern',
  },
  bronzor: {
    id: 'bronzor',
    name: 'Bronzor',
    slug: 'bronzor',
    category: 'classic',
    atsScore: 90,
    features: 'Traditional single-column format with centered header',
    description: 'Classic professional resume template with traditional formatting',
    component: BronzorTemplate,
    layout: 'single-column',
    style: 'classic',
  },
  chikorita: {
    id: 'chikorita',
    name: 'Chikorita',
    slug: 'chikorita',
    category: 'modern',
    atsScore: 88,
    features: 'Colorful section headers with modern styling',
    description: 'Modern template with colorful section headers and vibrant design',
    component: ChikoritaTemplate,
    layout: 'two-column',
    style: 'modern',
  },
  ditto: {
    id: 'ditto',
    name: 'Ditto',
    slug: 'ditto',
    category: 'minimalist',
    atsScore: 92,
    features: 'Clean minimalist design with excellent typography',
    description: 'Minimalist design focusing on content with subtle styling',
    component: DittoTemplate,
    layout: 'single-column',
    style: 'minimalist',
  },
  gengar: {
    id: 'gengar',
    name: 'Gengar',
    slug: 'gengar',
    category: 'creative',
    atsScore: 87,
    features: 'Creative layout with diagonal design elements',
    description: 'Creative template with unique styling and modern elements',
    component: GengarTemplate,
    layout: 'asymmetric',
    style: 'creative',
  },
  glalie: {
    id: 'glalie',
    name: 'Glalie',
    slug: 'glalie',
    category: 'professional',
    atsScore: 89,
    features: 'Corporate style with dark header',
    description: 'Professional corporate template with formal styling',
    component: GlalieTemplate,
    layout: 'two-column',
    style: 'professional',
  },
  kakuna: {
    id: 'kakuna',
    name: 'Kakuna',
    slug: 'kakuna',
    category: 'modern',
    atsScore: 91,
    features: 'Well-structured centered sections',
    description: 'Well-structured modern template with centered design',
    component: KakunaTemplate,
    layout: 'single-column',
    style: 'modern',
  },
  leafish: {
    id: 'leafish',
    name: 'Leafish',
    slug: 'leafish',
    category: 'creative',
    atsScore: 86,
    features: 'Nature-inspired design with organic elements',
    description: 'Creative template with nature-inspired design elements',
    component: LeafishTemplate,
    layout: 'asymmetric',
    style: 'creative',
  },
  nosepass: {
    id: 'nosepass',
    name: 'Nosepass',
    slug: 'nosepass',
    category: 'classic',
    atsScore: 88,
    features: 'Traditional format with formal styling',
    description: 'Classic template with traditional formatting and formal appearance',
    component: NosepassTemplate,
    layout: 'two-column',
    style: 'classic',
  },
  onyx: {
    id: 'onyx',
    name: 'Onyx',
    slug: 'onyx',
    category: 'professional',
    atsScore: 90,
    features: 'Bold headers with strong visual impact',
    description: 'Professional template with bold section headers and strong styling',
    component: OnyxTemplate,
    layout: 'single-column',
    style: 'professional',
  },
  pikachu: {
    id: 'pikachu',
    name: 'Pikachu',
    slug: 'pikachu',
    category: 'modern',
    atsScore: 94,
    features: 'Popular modern design with excellent ATS compatibility',
    description: 'Most popular modern template with excellent ATS compatibility',
    component: PikachuTemplate,
    layout: 'two-column',
    style: 'modern',
  },
  rhyhorn: {
    id: 'rhyhorn',
    name: 'Rhyhorn',
    slug: 'rhyhorn',
    category: 'robust',
    atsScore: 89,
    features: 'Strong structure with robust visual hierarchy',
    description: 'Robust template with strong visual hierarchy and bold design',
    component: RhyhornTemplate,
    layout: 'asymmetric',
    style: 'professional',
  },
};

/**
 * Get template component by slug
 */
export const getTemplateComponent = (slug: string): React.ComponentType<TemplateComponentProps> | null => {
  const template = templateRegistry[slug];
  return template ? template.component : null;
};

/**
 * Get template info by slug
 */
export const getTemplateInfo = (slug: string): TemplateInfo | null => {
  return templateRegistry[slug] || null;
};

/**
 * Get all template slugs
 */
export const getAllTemplateSlugs = (): string[] => {
  return Object.keys(templateRegistry);
};

/**
 * Get all templates info
 */
export const getAllTemplates = (): TemplateInfo[] => {
  return Object.values(templateRegistry);
};

/**
 * Get templates by category
 */
export const getTemplatesByCategory = (category: string): TemplateInfo[] => {
  return Object.values(templateRegistry).filter(template => template.category === category);
};

/**
 * Get templates by style
 */
export const getTemplatesByStyle = (style: string): TemplateInfo[] => {
  return Object.values(templateRegistry).filter(template => template.style === style);
};

/**
 * Get templates by layout
 */
export const getTemplatesByLayout = (layout: string): TemplateInfo[] => {
  return Object.values(templateRegistry).filter(template => template.layout === layout);
};

/**
 * Get templates sorted by ATS score
 */
export const getTemplatesByAtsScore = (): TemplateInfo[] => {
  return Object.values(templateRegistry).sort((a, b) => b.atsScore - a.atsScore);
};

/**
 * Template renderer component
 */
export interface TemplateRendererProps {
  templateSlug: string;
  resume: FullResume;
  className?: string;
}

export const TemplateRenderer: React.FC<TemplateRendererProps> = ({ 
  templateSlug, 
  resume, 
  className = '' 
}) => {
  const TemplateComponent = getTemplateComponent(templateSlug);
  
  if (!TemplateComponent) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-500 ${className}`}>
        <p>Template "{templateSlug}" not found</p>
      </div>
    );
  }
  
  return (
    <div className={className}>
      <TemplateComponent resume={resume} />
    </div>
  );
};

export default TemplateRenderer;