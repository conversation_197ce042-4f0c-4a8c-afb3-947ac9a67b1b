CREATE TABLE `ab_tests` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`feature_id` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text,
	`traffic_allocation` integer DEFAULT 100 NOT NULL,
	`variants` text NOT NULL,
	`success_metrics` text,
	`hypothesis` text,
	`results` text,
	`status` text DEFAULT 'draft' NOT NULL,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`created_by` text
);
--> statement-breakpoint
CREATE TABLE `feature_analytics` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`feature_id` text NOT NULL,
	`date` text NOT NULL,
	`unique_users` integer DEFAULT 0 NOT NULL,
	`total_usage` integer DEFAULT 0 NOT NULL,
	`successful_usage` integer DEFAULT 0 NOT NULL,
	`blocked_usage` integer DEFAULT 0 NOT NULL,
	`premium_users` integer DEFAULT 0 NOT NULL,
	`free_users` integer DEFAULT 0 NOT NULL,
	`average_duration` integer,
	`conversion_rate` integer,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE INDEX `feature_analytics_feature_date_idx` ON `feature_analytics` (`feature_id`,`date`);--> statement-breakpoint
CREATE INDEX `feature_analytics_date_idx` ON `feature_analytics` (`date`);--> statement-breakpoint
CREATE INDEX `feature_analytics_feature_id_idx` ON `feature_analytics` (`feature_id`);--> statement-breakpoint
CREATE TABLE `feature_flags` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`key` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`is_enabled` integer DEFAULT true NOT NULL,
	`requires_premium` integer DEFAULT false NOT NULL,
	`category` text DEFAULT 'exploration' NOT NULL,
	`lifecycle` text DEFAULT 'stable' NOT NULL,
	`rollout_percentage` integer DEFAULT 100 NOT NULL,
	`target_audience` text,
	`block_message` text,
	`global_limit` integer,
	`metadata` text,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`created_by` text,
	`deprecated_at` text
);
--> statement-breakpoint
CREATE UNIQUE INDEX `feature_flags_key_unique` ON `feature_flags` (`key`);--> statement-breakpoint
CREATE TABLE `feature_usage` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` text NOT NULL,
	`feature_id` text NOT NULL,
	`action` text NOT NULL,
	`context` text,
	`user_agent` text,
	`ip_address` text,
	`session_id` text,
	`ab_test_variant` text,
	`duration` integer,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE INDEX `feature_usage_user_id_idx` ON `feature_usage` (`user_id`);--> statement-breakpoint
CREATE INDEX `feature_usage_feature_id_idx` ON `feature_usage` (`feature_id`);--> statement-breakpoint
CREATE INDEX `feature_usage_created_at_idx` ON `feature_usage` (`createdAt`);--> statement-breakpoint
CREATE INDEX `feature_usage_user_feature_idx` ON `feature_usage` (`user_id`,`feature_id`);--> statement-breakpoint
CREATE TABLE `user_feature_assignments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` text NOT NULL,
	`feature_id` text NOT NULL,
	`is_enabled` integer NOT NULL,
	`ab_test_id` integer,
	`variant` text,
	`assigned_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`expires_at` text,
	`reason` text
);
--> statement-breakpoint
CREATE INDEX `user_feature_assignments_user_feature_idx` ON `user_feature_assignments` (`user_id`,`feature_id`);--> statement-breakpoint
CREATE INDEX `user_feature_assignments_user_id_idx` ON `user_feature_assignments` (`user_id`);--> statement-breakpoint
CREATE INDEX `user_feature_assignments_feature_id_idx` ON `user_feature_assignments` (`feature_id`);--> statement-breakpoint
CREATE INDEX `user_feature_assignments_ab_test_id_idx` ON `user_feature_assignments` (`ab_test_id`);