"use client";

import { <PERSON>, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import NextLink from "next/link";
import type { NavbarMenuItem } from "@/config/navbar-menu";
import { getMenuItemColorsFromItem } from "@/lib/utils/colors";

interface SidebarItemProps {
  item: NavbarMenuItem;
  isActive: boolean;
  isCollapsed: boolean;
  label: string;
}

export const SidebarItem = ({ item, isActive, isCollapsed, label }: SidebarItemProps) => {
  const colors = getMenuItemColorsFromItem(item);

  const content = (
    <Link
      as={NextLink}
      href={item.href}
      className={`
        w-full px-3 py-3 rounded-xl flex items-center gap-3 transition-all duration-200
        ${
          isActive
            ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
            : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800"
        }
        ${isCollapsed ? "justify-center" : ""}
      `}
    >
      <div
        className={`
          w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0
          ${isActive ? colors.background : "bg-gray-100 dark:bg-gray-800"}
        `}
      >
        <Icon icon={item.icon} className={`w-5 h-5 ${isActive ? colors.text : "text-gray-600 dark:text-gray-400"}`} />
      </div>
      {!isCollapsed && (
        <div className="flex-1 min-w-0">
          <p className="font-medium truncate">{label}</p>
        </div>
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <Tooltip content={label} placement="right">
        <div>{content}</div>
      </Tooltip>
    );
  }

  return content;
};
