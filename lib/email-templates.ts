export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  category: "professional" | "casual" | "networking";
}

export const EMAIL_TEMPLATES: EmailTemplate[] = [
  {
    id: "professional-introduction",
    name: "Professional Introduction",
    subject: "Resume - {{fullName}}",
    body: `Dear {{recipientName}},

I hope this email finds you well. I am writing to share my resume with you for your consideration.

I am {{fullName}}, {{jobTitle}} with {{yearsOfExperience}} years of experience. I am currently seeking new opportunities and would be interested in discussing how my skills and experience could contribute to your organization.

Please find my resume attached for your review. I would welcome the opportunity to discuss my background further at your convenience.

Best regards,
{{fullName}}
{{email}}
{{phone}}`,
    category: "professional",
  },
  {
    id: "job-application",
    name: "Job Application",
    subject: "Application for {{position}} - {{fullName}}",
    body: `Dear Hiring Manager,

I am writing to express my interest in the {{position}} position at {{companyName}}. I believe my skills and experience make me a strong candidate for this role.

{{personalPitch}}

I have attached my resume for your review and would appreciate the opportunity to discuss how I can contribute to your team.

Thank you for your time and consideration.

Sincerely,
{{fullName}}
{{email}}
{{phone}}`,
    category: "professional",
  },
  {
    id: "networking-introduction",
    name: "Networking Introduction",
    subject: "Introduction - {{fullName}}",
    body: `Hi {{recipientName}},

I hope you're doing well! {{mutualConnection}} suggested I reach out to you.

I'm {{fullName}}, {{jobTitle}} with a passion for {{industry}}. I'm currently exploring new opportunities and would love to connect with professionals in the field.

I've attached my resume so you can learn more about my background. I'd be grateful for any insights you might have about the industry or potential opportunities.

Would you be available for a brief coffee chat in the coming weeks?

Best,
{{fullName}}
{{email}}`,
    category: "networking",
  },
  {
    id: "follow-up",
    name: "Follow-up",
    subject: "Following up - {{fullName}}",
    body: `Hi {{recipientName}},

I wanted to follow up on our conversation from {{meetingContext}}. As discussed, I'm attaching my updated resume for your reference.

{{followUpNote}}

I appreciate your time and look forward to hearing from you.

Best regards,
{{fullName}}
{{email}}
{{phone}}`,
    category: "professional",
  },
  {
    id: "casual-share",
    name: "Casual Share",
    subject: "My Resume - {{fullName}}",
    body: `Hey {{recipientName}},

Hope you're doing great! I wanted to share my latest resume with you.

{{personalNote}}

Let me know what you think! I'd love any feedback you might have.

Thanks!
{{fullName}}`,
    category: "casual",
  },
];

export interface EmailVariables {
  fullName: string;
  firstName: string;
  lastName: string;
  jobTitle: string;
  email: string;
  phone: string;
  recipientName?: string;
  position?: string;
  companyName?: string;
  personalPitch?: string;
  mutualConnection?: string;
  industry?: string;
  meetingContext?: string;
  followUpNote?: string;
  personalNote?: string;
  yearsOfExperience?: string;
}

export function renderEmailTemplate(
  template: EmailTemplate,
  variables: EmailVariables,
): { subject: string; body: string } {
  let subject = template.subject;
  let body = template.body;

  // Replace all variables in the template
  Object.entries(variables).forEach(([key, value]) => {
    if (value) {
      const placeholder = `{{${key}}}`;
      subject = subject.replace(new RegExp(placeholder, "g"), value);
      body = body.replace(new RegExp(placeholder, "g"), value);
    }
  });

  // Clean up any remaining placeholders
  subject = subject.replace(/\{\{.*?\}\}/g, "");
  body = body.replace(/\{\{.*?\}\}/g, "");

  return { subject: subject.trim(), body: body.trim() };
}
