"use client";
import { Input } from "@heroui/react";

export const ColorThemeInput = ({
  label,
  value,
  onChange,
}: {
  label: string;
  value: string;
  onChange: (value: string) => void;
}) => {
  return (
    <Input
      label={label}
      startContent={
        <div
          className={`w-5 h-5 rounded-full`}
          style={{
            backgroundColor: value,
          }}
        />
      }
      value={value}
      onValueChange={(value) => {
        onChange(value);
      }}
    />
  );
};
