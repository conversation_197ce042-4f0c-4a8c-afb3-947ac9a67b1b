import React from "react";
import { Skill } from "@/db/schema";

export interface SkillsSectionProps {
  skills: Skill[];
  layout?: "grid" | "list";
  showProficiency?: boolean;
  className?: string;
}

export const SkillsSection: React.FC<SkillsSectionProps> = ({ skills, layout = "grid", className = "" }) => {
  if (!skills || skills.length === 0) return null;

  // Group skills by category
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      const category = skill.category || "";
      if (!acc[category]) acc[category] = [];
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, typeof skills>,
  );

  const renderSkillGroup = (category: string, categorySkills: typeof skills) => {
    const skillNames = categorySkills.map((skill) => skill.name).join(", ");

    return (
      <div key={category} className="mb-3">
        <h4 className="font-semibold text-gray-800 text-sm mb-1">{category}</h4>
        <p className="text-gray-700 text-sm flex flex-wrap text-start">{skillNames}</p>
      </div>
    );
  };

  return (
    <div className={`skills-section ${layout === "grid" ? "grid grid-cols-3 gap-2" : "flex-col"}  ${className}`}>
      {Object.entries(skillsByCategory).map(([category, categorySkills]) => renderSkillGroup(category, categorySkills))}
    </div>
  );
};
