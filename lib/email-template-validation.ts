import { z } from "zod";

// Base validation schema for all templates
const baseSchema = z.object({
  recipientName: z.string().min(1, "Recipient name is required"),
});

// Template-specific validation schemas
export const emailTemplateValidationSchemas = {
  "professional-introduction": baseSchema.extend({
    yearsOfExperience: z.string().min(1, "Years of experience is required"),
  }),

  "job-application": baseSchema.extend({
    position: z.string().min(1, "Position is required"),
    companyName: z.string().min(1, "Company name is required"),
    personalPitch: z.string().min(10, "Personal pitch is required (minimum 10 characters)"),
  }),

  "networking-introduction": baseSchema.extend({
    mutualConnection: z.string().min(1, "Mutual connection is required"),
    industry: z.string().min(1, "Industry is required"),
  }),

  "follow-up": baseSchema.extend({
    meetingContext: z.string().min(1, "Meeting context is required"),
    followUpNote: z.string().min(5, "Follow-up note is required (minimum 5 characters)"),
  }),

  "casual-share": baseSchema.extend({
    personalNote: z.string().min(5, "Personal note is required (minimum 5 characters)"),
  }),
};

// Get required fields for a specific template
export function getRequiredFields(templateId: string): string[] {
  const schema = emailTemplateValidationSchemas[templateId as keyof typeof emailTemplateValidationSchemas];
  if (!schema) return [];

  const shape = schema.shape;
  return Object.keys(shape);
}

// Validate template variables
export function validateTemplateVariables(templateId: string, variables: Record<string, string>) {
  const schema = emailTemplateValidationSchemas[templateId as keyof typeof emailTemplateValidationSchemas];
  if (!schema) {
    throw new Error(`No validation schema found for template: ${templateId}`);
  }

  return schema.safeParse(variables);
}

// Get field validation error for a specific field
export function getFieldError(templateId: string, fieldName: string, value: string): string | null {
  const schema = emailTemplateValidationSchemas[templateId as keyof typeof emailTemplateValidationSchemas];
  if (!schema) return null;

  const fieldSchema = schema.shape[fieldName as keyof typeof schema.shape];
  if (!fieldSchema) return null;

  const result = fieldSchema.safeParse(value);
  if (!result.success) {
    return result.error.issues[0]?.message || "Invalid value";
  }

  return null;
}

// Check if a field is required for a template
export function isFieldRequired(templateId: string, fieldName: string): boolean {
  const requiredFields = getRequiredFields(templateId);
  return requiredFields.includes(fieldName);
}
