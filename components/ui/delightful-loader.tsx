"use client";

import { Icon } from "@iconify/react";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface DelightfulLoaderProps {
  isVisible: boolean;
  type?: "resume" | "upload" | "generation" | "processing" | "default";
  message?: string;
  subMessage?: string;
  size?: "sm" | "md" | "lg";
  showProgress?: boolean;
  progress?: number;
  className?: string;
}

const LOADER_CONFIGS = {
  resume: {
    icon: "heroicons:document-text-20-solid",
    color: "text-primary",
    bgColor: "bg-primary/10",
    messages: [
      "Crafting your perfect resume...",
      "Adding that special touch...",
      "Making it shine...",
      "Almost ready to impress!",
    ],
  },
  upload: {
    icon: "heroicons:cloud-arrow-up-20-solid",
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    messages: [
      "Uploading your amazing photo...",
      "Making it look perfect...",
      "Just a few more seconds...",
      "Ready to wow employers!",
    ],
  },
  generation: {
    icon: "heroicons:sparkles-20-solid",
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    messages: [
      "AI is working its magic...",
      "Crafting compelling content...",
      "Adding professional flair...",
      "Creating something amazing!",
    ],
  },
  processing: {
    icon: "heroicons:cog-6-tooth-20-solid",
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
    messages: [
      "Processing your request...",
      "Working behind the scenes...",
      "Putting it all together...",
      "Almost done!",
    ],
  },
  default: {
    icon: "heroicons:arrow-path-20-solid",
    color: "text-primary",
    bgColor: "bg-primary/10",
    messages: [
      "Loading something awesome...",
      "Preparing your experience...",
      "Just a moment please...",
      "Great things are coming!",
    ],
  },
};

const SIZE_CLASSES = {
  sm: {
    container: "w-16 h-16",
    icon: "w-6 h-6",
    text: "text-sm",
    subText: "text-xs",
  },
  md: {
    container: "w-20 h-20",
    icon: "w-8 h-8",
    text: "text-base",
    subText: "text-sm",
  },
  lg: {
    container: "w-24 h-24",
    icon: "w-10 h-10",
    text: "text-lg",
    subText: "text-base",
  },
};

export function DelightfulLoader({
  isVisible,
  type = "default",
  message,
  subMessage,
  size = "md",
  showProgress = false,
  progress = 0,
  className = "",
}: DelightfulLoaderProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [showFloatingElements, setShowFloatingElements] = useState(false);

  const config = LOADER_CONFIGS[type];
  const sizeClasses = SIZE_CLASSES[size];
  const currentMessage = message || config.messages[currentMessageIndex];

  useEffect(() => {
    if (!isVisible) return;

    // Cycle through messages
    const messageTimer = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % config.messages.length);
    }, 2000);

    // Show floating elements after a delay
    const elementsTimer = setTimeout(() => {
      setShowFloatingElements(true);
    }, 500);

    return () => {
      clearInterval(messageTimer);
      clearTimeout(elementsTimer);
    };
  }, [isVisible, config.messages.length]);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.3 }}
        className={`flex flex-col items-center justify-center p-8 ${className}`}
      >
        {/* Main loader container */}
        <div className="relative mb-6">
          {/* Animated background circle */}
          <motion.div
            className={`${sizeClasses.container} ${config.bgColor} rounded-full flex items-center justify-center relative overflow-hidden`}
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            {/* Spinning gradient border */}
            <motion.div
              className="absolute inset-0 rounded-full"
              style={{
                background: `conic-gradient(from 0deg, transparent, ${config.color.includes("primary") ? "rgb(var(--primary))" : config.color.replace("text-", "")}, transparent)`,
              }}
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />

            {/* Inner circle */}
            <div
              className={`absolute inset-1 bg-white dark:bg-default-900 rounded-full flex items-center justify-center`}
            >
              {/* Main icon */}
              <motion.div
                animate={{
                  rotate: type === "processing" || type === "default" ? 360 : [0, 10, -10, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  rotate: {
                    duration: type === "processing" || type === "default" ? 2 : 4,
                    repeat: Infinity,
                    ease: type === "processing" || type === "default" ? "linear" : "easeInOut",
                  },
                  scale: { duration: 1.5, repeat: Infinity, ease: "easeInOut" },
                }}
              >
                <Icon icon={config.icon} className={`${sizeClasses.icon} ${config.color}`} />
              </motion.div>
            </div>
          </motion.div>

          {/* Floating sparkles */}
          <AnimatePresence>
            {showFloatingElements &&
              [
                { icon: "heroicons:star-20-solid", delay: 0, radius: 40, angle: 0 },
                { icon: "heroicons:heart-20-solid", delay: 0.5, radius: 45, angle: 120 },
                { icon: "heroicons:sparkles-20-solid", delay: 1, radius: 35, angle: 240 },
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="absolute"
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 0.6, 0],
                    x: Math.cos((item.angle * Math.PI) / 180 + Date.now() * 0.001) * item.radius,
                    y: Math.sin((item.angle * Math.PI) / 180 + Date.now() * 0.001) * item.radius,
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: item.delay,
                    ease: "easeInOut",
                  }}
                  style={{
                    left: "50%",
                    top: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                >
                  <Icon icon={item.icon} className="w-4 h-4 text-primary/60" />
                </motion.div>
              ))}
          </AnimatePresence>
        </div>

        {/* Loading message */}
        <div className="text-center min-h-[3rem] flex flex-col justify-center">
          <AnimatePresence mode="wait">
            <motion.h3
              key={currentMessageIndex}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.4 }}
              className={`font-semibold text-default-900 mb-1 ${sizeClasses.text}`}
            >
              {currentMessage}
            </motion.h3>
          </AnimatePresence>

          {subMessage && (
            <motion.p
              className={`text-default-600 ${sizeClasses.subText}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {subMessage}
            </motion.p>
          )}
        </div>

        {/* Progress bar */}
        {showProgress && (
          <motion.div
            className="w-full max-w-xs mt-4"
            initial={{ opacity: 0, scaleX: 0 }}
            animate={{ opacity: 1, scaleX: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div className="h-2 bg-default-200 dark:bg-default-700 rounded-full overflow-hidden">
              <motion.div
                className={`h-full bg-gradient-to-r from-primary to-secondary rounded-full`}
                initial={{ width: "0%" }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <div className="flex justify-between text-xs text-default-500 mt-1">
              <span>0%</span>
              <span>{Math.round(progress)}%</span>
              <span>100%</span>
            </div>
          </motion.div>
        )}

        {/* Bouncing dots */}
        <div className="flex space-x-1 mt-4">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className={`w-2 h-2 ${config.bgColor.replace("/10", "")} rounded-full`}
              animate={{ y: [0, -8, 0] }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>

        {/* Encouraging footer */}
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <div className="flex items-center justify-center gap-1 text-xs text-default-500">
            <Icon icon="heroicons:heart-20-solid" className="w-3 h-3 text-red-400" />
            <span>Made with love</span>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

export default DelightfulLoader;
