import React from "react";
import { Project } from "@/db/schema";
import { ProjectDateRange } from "./DateDisplay";

export interface ProjectItemProps {
  project: Project;
  variant?: "standard" | "compact" | "card";
  showClient?: boolean;
  showTechnologies?: boolean;
  className?: string;
}

export const ProjectItem: React.FC<ProjectItemProps> = ({
  project,
  variant = "standard",
  showClient = true,
  showTechnologies: _showTechnologies = true,
  className = "",
}) => {
  const hasDateRange = project.startDate && project.endDate;

  if (variant === "card") {
    return (
      <div className={`project-item mb-4 p-4 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-bold text-gray-900 mb-1">{project.title}</h3>
          {hasDateRange && (
            <ProjectDateRange
              startDate={project.startDate}
              endDate={project.endDate}
              isCurrent={false}
              locale="en-US"
            />
          )}
        </div>
        {showClient && project.client && <p className="text-gray-700 text-sm font-medium mb-2">{project.client}</p>}
        {project.description && (
          <div dangerouslySetInnerHTML={{ __html: project.description }} className="text-gray-700 text-sm mb-2" />
        )}
        {project.url && <p className="text-blue-500 text-sm mt-2">🔗 {project.url}</p>}
      </div>
    );
  }

  return (
    <div className={`project-item mb-4 ${className}`}>
      <div className="flex justify-between items-start mb-1">
        <h3 className="font-bold text-gray-900">{project.title}</h3>
        {hasDateRange && (
          <ProjectDateRange
            startDate={project.startDate}
            endDate={project.endDate}
            isCurrent={false}
            locale="en-US"
          />
        )}
      </div>
      {showClient && project.client && <p className="text-gray-700 text-sm font-medium mb-1">{project.client}</p>}
      {project.description && (
        <div dangerouslySetInnerHTML={{ __html: project.description }} className="text-gray-700 text-sm mb-2" />
      )}
      {project.url && <p className="text-blue-500 text-sm">🔗 {project.url}</p>}
    </div>
  );
};
