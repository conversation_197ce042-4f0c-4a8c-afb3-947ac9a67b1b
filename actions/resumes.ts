"use server";

import { and, eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { db } from "@/db";
import { resumes } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

// Function to save resume thumbnail
export async function saveResumeThumbnail(resumeId: number, thumbnailBase64: string) {
  try {
    const user = await requireAuth();

    // Verify the resume belongs to the user
    const [resume] = await db
      .select()
      .from(resumes)
      .where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
      .limit(1);

    if (!resume) {
      throw new Error("Resume not found or unauthorized");
    }

    // Update the resume with the new thumbnail
    await db.update(resumes).set({ thumbnail: thumbnailBase64 }).where(eq(resumes.id, resumeId));

    revalidatePath(`/resumes`);
    revalidatePath(`/resumes/edit/${resumeId}`);

    return { success: true };
  } catch (error) {
    console.error("Error saving resume thumbnail:", error);
    return { success: false, error: "Failed to save thumbnail." };
  }
}
