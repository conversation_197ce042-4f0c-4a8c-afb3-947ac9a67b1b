"use client";

import { But<PERSON>, Dropdown, Dropdown<PERSON><PERSON>, DropdownMenu, DropdownTrigger } from "@heroui/react";
import { Icon } from "@iconify/react";
import { ReactNode } from "react";
import { SortOption } from "@/hooks/use-filter-sort";
import FilterIndicator from "./filter-indicator";
import "@/styles/animations.css";

interface FilterOption {
  key: string;
  icon: string;
  label: string;
}

interface PageHeaderProps {
  // Header configuration
  icon: string;
  titleKey: string;
  subtitleKey: string;
  itemCount: number;
  translationNamespace: string;

  // Primary action
  primaryAction?: ReactNode;

  // Actions
  onExportAll?: () => void;
  onFilterChange?: (filter: string) => void;
  onSortChange?: (sort: SortOption) => void;
  onReset?: () => void;

  // Filter/Sort state
  currentFilter?: string;
  currentSort?: SortOption;
  filteredCount?: number;

  // Configuration
  filterOptions?: FilterOption[];
  showExportAll?: boolean;
  exportDisabled?: boolean;
  itemType?: string;
}

const DEFAULT_SORT_OPTIONS = [
  { key: "newest", icon: "tabler:sort-descending", labelKey: "newest_first" },
  { key: "oldest", icon: "tabler:sort-ascending", labelKey: "oldest_first" },
  {
    key: "name-asc",
    icon: "tabler:sort-ascending-letters",
    labelKey: "name_a_z",
  },
  {
    key: "name-desc",
    icon: "tabler:sort-descending-letters",
    labelKey: "name_z_a",
  },
  { key: "updated", icon: "tabler:refresh", labelKey: "recently_updated" },
];

export default function PageHeader({
  icon,
  titleKey,
  subtitleKey,
  itemCount,
  translationNamespace,
  primaryAction,
  onExportAll,
  onFilterChange,
  onSortChange,
  onReset,
  currentFilter = "all",
  currentSort = "newest",
  filteredCount,
  filterOptions = [],
  showExportAll = true,
  exportDisabled = false,
  itemType = "items",
}: PageHeaderProps) {
  // Sort options with static labels
  const translatedSortOptions = [
    { key: "newest", icon: "tabler:sort-descending", label: "Newest First" },
    { key: "oldest", icon: "tabler:sort-ascending", label: "Oldest First" },
    { key: "name-asc", icon: "tabler:sort-ascending-letters", label: "Name A-Z" },
    { key: "name-desc", icon: "tabler:sort-descending-letters", label: "Name Z-A" },
    { key: "updated", icon: "tabler:refresh", label: "Recently Updated" },
  ];

  return (
    <div className="mb-12 animate-fade-in">
      {/* Main Header Section */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-6 mb-6">
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-110 transition-transform">
              <Icon icon={icon} className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-pulse" />
          </div>
          <div>
            <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              {titleKey}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">{subtitleKey}</p>
          </div>
        </div>

        {primaryAction && <div className="flex items-center gap-3">{primaryAction}</div>}
      </div>

      {/* Quick Actions Bar */}
      <div className="flex flex-wrap gap-2">
        {showExportAll && (
          <Button
            size="sm"
            variant="flat"
            className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
            startContent={<Icon icon="tabler:download" className="w-4 h-4" />}
            onPress={onExportAll}
            isDisabled={exportDisabled}
          >
            Export All
          </Button>
        )}

        {filterOptions.length > 0 && (
          <Dropdown>
            <DropdownTrigger>
              <Button
                size="sm"
                variant="flat"
                className="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                startContent={<Icon icon="tabler:filter" className="w-4 h-4" />}
                endContent={<Icon icon="tabler:chevron-down" className="w-3 h-3" />}
              >
                Filter
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Filter options" onAction={(key) => onFilterChange?.(key as string)}>
              {filterOptions.map((option) => (
                <DropdownItem key={option.key} startContent={<Icon icon={option.icon} className="w-4 h-4" />}>
                  {option.label}
                </DropdownItem>
              ))}
            </DropdownMenu>
          </Dropdown>
        )}

        {onSortChange && (
          <Dropdown>
            <DropdownTrigger>
              <Button
                size="sm"
                variant="flat"
                className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                startContent={<Icon icon="tabler:sort-ascending" className="w-4 h-4" />}
                endContent={<Icon icon="tabler:chevron-down" className="w-3 h-3" />}
              >
                Sort
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Sort options" onAction={(key) => onSortChange(key as SortOption)}>
              {translatedSortOptions.map((option) => (
                <DropdownItem key={option.key} startContent={<Icon icon={option.icon} className="w-4 h-4" />}>
                  {option.label}
                </DropdownItem>
              ))}
            </DropdownMenu>
          </Dropdown>
        )}
      </div>

      {/* Active filters indicator */}
      {filteredCount !== undefined && onReset && (
        <FilterIndicator
          currentFilter={currentFilter}
          currentSort={currentSort}
          filteredCount={filteredCount}
          totalCount={itemCount}
          itemType={itemType}
          onClear={onReset}
        />
      )}
    </div>
  );
}
