import { initializePaddle as paddleInit } from "@paddle/paddle-js";

// Initialize Paddle client - this runs on the client side
export async function initializePaddle() {
  if (typeof window === "undefined") return null;

  const environment = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as "sandbox" | "production";
  const token = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN;

  if (!token) {
    console.error("Paddle client token is not configured");
    return null;
  }

  try {
    const paddle = await paddleInit({
      token,
      environment: environment || "sandbox",
      checkout: {
        settings: {
          displayMode: "overlay",
          theme: "light",
        },
      },
    });
    return paddle;
  } catch (error) {
    console.error("Failed to initialize Paddle:", error);
    return null;
  }
}

// Product configuration
export const PADDLE_PRODUCTS = {
  lifetime: {
    priceId: process.env.NEXT_PUBLIC_PADDLE_LIFETIME_PRICE_ID || "",
    productId: process.env.NEXT_PUBLIC_PADDLE_LIFETIME_PRODUCT_ID || "",
  },
};

// Paddle webhook event types we care about
export enum PaddleEventType {
  TransactionCompleted = "transaction.completed",
  TransactionUpdated = "transaction.updated",
}

// Type for the webhook payload
export interface PaddleWebhookPayload {
  event_id: string;
  event_type: string;
  occurred_at: string;
  data: {
    id: string;
    status: string;
    customer_id: string | null;
    custom_data?: {
      userId?: string;
    };
    items: Array<{
      price_id: string;
      quantity: number;
    }>;
    details: {
      tax_amount: string;
      totals: {
        total: string;
        tax: string;
        subtotal: string;
      };
    };
  };
}
