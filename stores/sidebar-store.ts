import { create } from "zustand";
import { persist } from "zustand/middleware";

interface SidebarState {
  isOpen: boolean;
  isCollapsed: boolean;
  isMobile: boolean;
  toggle: () => void;
  setCollapsed: (collapsed: boolean) => void;
  setMobile: (mobile: boolean) => void;
  close: () => void;
  open: () => void;
}

export const useSidebarStore = create<SidebarState>()(
  persist(
    (set, get) => ({
      isOpen: false,
      isCollapsed: false,
      isMobile: false,
      toggle: () => set((state) => ({ isOpen: !state.isOpen })),
      setCollapsed: (collapsed: boolean) => set({ isCollapsed: collapsed }),
      setMobile: (mobile: boolean) => set({ isMobile: mobile }),
      close: () => set({ isOpen: false }),
      open: () => set({ isOpen: true }),
    }),
    {
      name: "sidebar-storage",
      partialize: (state) => ({
        isCollapsed: state.isCollapsed,
        // Don't persist isOpen or isMobile as they should reset on page load
      }),
    },
  ),
);
