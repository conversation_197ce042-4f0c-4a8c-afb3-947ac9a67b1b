import { cn } from "@heroui/react";
import React from "react";
import { RichEditor } from "./rich-editor";

interface TrixEditorFieldProps {
  id: string;
  name: string;
  label: string;
  value: string;

  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  onChange?: (value: string) => void;

  // AI Integration props
  enableAI?: boolean;
  resumeContext?: any;
  itemContext?: any;
  schemaEntity?: string;
}

export const TrixEditorField: React.FC<TrixEditorFieldProps> = ({
  id,
  name, // Used as a data attribute for form identification
  label,
  value,

  placeholder,
  required = false,
  error,
  className,
  labelClassName,
  onChange,

  enableAI = true,
  resumeContext,
  itemContext,
  schemaEntity,
}) => {
  // // Handle change from Trix editor and update form data
  // const handleChange = (newValue: string) => {
  //   onChange(newValue);
  // };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between">
        <label
          className={cn(
            "text-sm font-medium text-foreground",
            required && 'after:content-["*"] after:ml-0.5 after:text-red-500',
            labelClassName,
          )}
          htmlFor={id}
        >
          {label}
        </label>
      </div>

      <RichEditor
        className={cn("!w-full !max-w-none", error && "border-red-500 focus-visible:ring-red-500")}
        id={id}
        name={name} // Add name as a data attribute for form submissions
        placeholder={placeholder}
        value={value || ""}
        onChange={onChange}
        enableAI={enableAI}
        fieldName={name}
        resumeContext={resumeContext}
        itemContext={itemContext}
        schemaEntity={schemaEntity}
      />

      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};
