import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  ContactInfo,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
  getSectionTranslations,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom section component for kakuna template with centered styling
const KakunaSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-6 resume-section" role="region" aria-labelledby={`section-${title.toLowerCase().replace(/\s+/g, '-')}`}>
    <h2 id={`section-${title.toLowerCase().replace(/\s+/g, '-')}`} className="text-lg font-bold mb-3 text-gray-900 text-center border-b border-gray-300 pb-2 section-title">
      {title}
    </h2>
    <div className="section-content">
      {children}
    </div>
  </section>
);

// Custom award item for kakuna template with centered layout
const KakunaAwardItem: React.FC<{
  award: any;
  locale: string;
}> = ({ award, locale }) => (
  <div className="text-center award-item mb-4">
    <h4 className="font-bold text-gray-900 text-sm">{award.title}</h4>
    <p className="text-gray-700 text-sm">{award.issuer}</p>
    {award.dateReceived && <p className="text-gray-600 text-sm">{formatDate(award.dateReceived, false, locale)}</p>}
    {award.description && (
      <div
        dangerouslySetInnerHTML={{ __html: award.description }}
        className="text-gray-700 text-sm leading-relaxed mt-1"
      />
    )}
  </div>
);

// Custom volunteering item for kakuna template with centered layout
const KakunaVolunteeringItem: React.FC<{
  volunteering: any;
  locale: string;
}> = ({ volunteering, locale }) => (
  <div className="text-center volunteering-item mb-4">
    <h4 className="font-bold text-gray-900 text-sm">{volunteering.role}</h4>
    <p className="text-gray-700 text-sm">{volunteering.organization}</p>
    {(volunteering.startDate || volunteering.endDate) && (
      <p className="text-gray-600 text-sm">
        {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
      </p>
    )}
    {volunteering.description && (
      <div
        dangerouslySetInnerHTML={{ __html: volunteering.description }}
        className="text-gray-700 text-sm leading-relaxed mt-1"
      />
    )}
  </div>
);

// Custom reference item for kakuna template with centered layout
const KakunaReferenceItem: React.FC<{
  reference: any;
}> = ({ reference }) => (
  <div className="text-center reference-item mb-4">
    <h4 className="font-bold text-gray-900 text-sm">{reference.name}</h4>
    <p className="text-gray-700 text-sm">
      {reference.position} at {reference.company}
    </p>
    <div className="text-gray-600 text-sm mt-1">
      {reference.email && <p>{reference.email}</p>}
      {reference.phone && <p>{reference.phone}</p>}
    </div>
    {reference.description && (
      <div
        dangerouslySetInnerHTML={{ __html: reference.description }}
        className="text-gray-700 text-sm leading-relaxed mt-1"
      />
    )}
  </div>
);

/**
 * Kakuna Resume Template - Clean Centered Layout
 * - Centered photo and contact info header
 * - Clean single-column layout with centered sections
 * - Side-by-side experience layout for multiple positions
 * - Simple typography and minimal styling
 * - Professional appearance matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
const KakunaTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`kakuna-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <main className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
        {/* Centered Header */}
        <header className="text-center mb-8 resume-header">
          {/* Photo */}
          {resume.showPhoto && resume.photo ? (
            <div className="mb-4">
              <Image
                alt={fullName}
                className="w-24 h-32 object-cover mx-auto border border-gray-300"
                height={128}
                src={resume.photo}
                width={96}
              />
            </div>
          ) : null}

          {/* Name and Title */}
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{fullName}</h1>
          <p className="text-lg text-gray-700 mb-4">{resume.jobTitle}</p>

          {/* Contact Information */}
          <ContactInfo
            className="mb-4 justify-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-700"
            email={resume.email}
            location={location}
            phone={resume.phone}
            variant="horizontal"
            website={resume.website}
          />

          {/* Social Media Links */}
          {resume.profiles && resume.profiles.length > 0 && (
            <SocialProfile
              className="justify-center space-x-4"
              layout="horizontal"
              profiles={resume.profiles}
              showNetworkLabel={false}
            />
          )}
        </header>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Summary */}
          {resume.bio && (
            <KakunaSection title={sectionTitles.summary}>
              <ProfessionalSummary bio={resume.bio} className="text-center max-w-4xl mx-auto" variant="paragraph" />
            </KakunaSection>
          )}

          {/* Experience */}
          {resume.experiences && resume.experiences.length > 0 && (
            <KakunaSection title={sectionTitles.experience}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {resume.experiences.map((exp) => (
                  <ExperienceItem
                    key={exp.id}
                    className="experience-item mb-4"
                    experience={exp}
                    locale={locale}
                    showWebsiteIcon={true}
                    variant="standard"
                  />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Education */}
          {resume.educations && resume.educations.length > 0 && (
            <KakunaSection title={sectionTitles.education}>
              <div className="text-center space-y-4">
                {resume.educations.map((edu) => (
                  <EducationItem
                    key={edu.id}
                    className="text-center"
                    education={edu}
                    locale={locale}
                    variant="standard"
                  />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <KakunaSection title={sectionTitles.projects}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {resume.projects.map((project) => (
                  <ProjectItem
                    key={project.id}
                    className="project-item mb-4"
                    project={project}
                    showClient={true}
                    showTechnologies={false}
                    variant="standard"
                  />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <KakunaSection title={sectionTitles.certifications}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.certifications.map((cert) => (
                  <CertificationItem
                    key={cert.id}
                    certification={cert}
                    className="text-center certification-item"
                    showCredentialId={false}
                  />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Skills */}
          {resume.skills && resume.skills.length > 0 && (
            <KakunaSection title={sectionTitles.skills}>
              <SkillsSection
                className="grid grid-cols-3 gap-6"
                layout="grid"
                showProficiency={false}
                skills={resume.skills}
              />
            </KakunaSection>
          )}

          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <KakunaSection title={sectionTitles.languages}>
              <div className="grid grid-cols-2 gap-4">
                {resume.languages.map((language) => (
                  <LanguageItem
                    key={language.id}
                    className="text-center"
                    language={language}
                    showBars={false}
                    showLevel={true}
                  />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Awards */}
          {resume.awards && resume.awards.length > 0 && (
            <KakunaSection title={sectionTitles.awards}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.awards.map((award) => (
                  <KakunaAwardItem key={award.id} award={award} locale={locale} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Volunteering */}
          {resume.volunteerings && resume.volunteerings.length > 0 && (
            <KakunaSection title={sectionTitles.volunteering}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.volunteerings.map((volunteering) => (
                  <KakunaVolunteeringItem key={volunteering.id} volunteering={volunteering} locale={locale} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Hobbies */}
          {resume.hobbies && resume.hobbies.length > 0 && (
            <KakunaSection title={sectionTitles.hobbies}>
              <div className="text-center">
                <p className="text-gray-700 text-sm leading-relaxed">
                  {resume.hobbies.map((hobby) => hobby.name).join(", ")}
                </p>
              </div>
            </KakunaSection>
          )}

          {/* References */}
          {resume.references && resume.references.length > 0 ? (
            <KakunaSection title={sectionTitles.references}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.references.map((reference) => (
                  <KakunaReferenceItem key={reference.id} reference={reference} />
                ))}
              </div>
            </KakunaSection>
          ) : (
            <KakunaSection title={sectionTitles.references}>
              <p className="text-gray-700 text-sm text-center">Available upon request</p>
            </KakunaSection>
          )}
        </div>
      </main>
    </div>
  );
};

export default KakunaTemplate;
