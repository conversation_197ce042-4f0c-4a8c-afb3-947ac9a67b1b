"use client";

import { Badge } from "@heroui/react";
import { Icon } from "@iconify/react";

interface PremiumBadgeProps {
  className?: string;
}

export function PremiumBadge({ className = "" }: PremiumBadgeProps) {
  return (
    <div className={`inline-flex items-center gap-1 ${className}`}>
      <Icon icon="lucide:crown" className="w-3 h-3 text-yellow-500" />
      <Badge content="PRO" color="warning" variant="flat" size="sm">
        <span className="sr-only">Premium User</span>
      </Badge>
    </div>
  );
}
