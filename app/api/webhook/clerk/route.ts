import { WebhookEvent } from "@clerk/nextjs/server";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { Webhook } from "svix";
import { type ClerkUserData, deleteUserFromDatabase, syncUser<PERSON>romClerk } from "@/lib/user-sync";

export async function POST(req: Request) {
  // You can find this in the Clerk Dashboard -> Webhooks -> choose the endpoint
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error("Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local");
  }

  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.text();
  const body = JSON.parse(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  console.log(`Clerk webhook received: ${eventType}`);

  try {
    switch (eventType) {
      case "user.created":
        await handleUserCreated(evt);
        break;
      case "user.updated":
        await handleUserUpdated(evt);
        break;
      case "user.deleted":
        await handleUserDeleted(evt);
        break;
      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error(`Error handling webhook ${eventType}:`, error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

async function handleUserCreated(evt: WebhookEvent) {
  if (evt.type !== "user.created") return;

  try {
    const userData = evt.data as ClerkUserData;
    const syncedUser = await syncUserFromClerk(userData);
    console.log(`User created in database: ${userData.id}`, syncedUser);
  } catch (error) {
    console.error("Error creating user in database:", error);
    throw error;
  }
}

async function handleUserUpdated(evt: WebhookEvent) {
  if (evt.type !== "user.updated") return;

  try {
    const userData = evt.data as ClerkUserData;
    const syncedUser = await syncUserFromClerk(userData);
    console.log(`User updated in database: ${userData.id}`, syncedUser);
  } catch (error) {
    console.error("Error updating user in database:", error);
    throw error;
  }
}

async function handleUserDeleted(evt: WebhookEvent) {
  if (evt.type !== "user.deleted") return;

  const { id } = evt.data;

  if (!id) {
    console.error("User deletion event missing user ID");
    return;
  }

  try {
    const deletedUser = await deleteUserFromDatabase(id);
    if (deletedUser) {
      console.log(`User deleted from database: ${id}`);
    } else {
      console.log(`User ${id} not found in database`);
    }
  } catch (error) {
    console.error("Error deleting user from database:", error);
    throw error;
  }
}
