# Browserless Docker Setup

This document explains how to set up <PERSON><PERSON><PERSON><PERSON> with <PERSON><PERSON> for PDF generation in QuickCV.

## Overview

QuickCV uses Browserless (a headless Chrome service) to generate PDFs from HTML content. The service runs in a Docker container while the main QuickCV application runs on the host machine.

## Setup

### 1. Run Browserless Container

```bash
docker run -d \
  --name quickcv-browserless-1 \
  -e token=local-dev-token-quickcv \
  -p 3001:3000 \
  --add-host=host.docker.internal:host-gateway \
  --restart unless-stopped \
  ghcr.io/browserless/chromium
```

### 2. Environment Configuration

Add to your `.env` file:

```env
BROWSER_WS_ENDPOINT=ws://localhost:3001?token=local-dev-token-quickcv
```

## Why This Configuration?

### Port Mapping (`-p 3001:3000`)
- Maps container's internal port 3000 to host port 3001
- Avoids conflict with QuickCV app running on host port 3000
- Allows QuickCV to connect to Browserless via `localhost:3001`

### Host Resolution (`--add-host=host.docker.internal:host-gateway`)
- **Critical for PDF generation**: Allows Browserless container to access QuickCV app
- Maps `host.docker.internal` hostname to the Docker host's gateway IP
- When Browserless renders PDFs, it needs to fetch HTML from `http://host.docker.internal:3000`
- Without this, you'll get `net::ERR_NAME_NOT_RESOLVED` errors

### Container Name (`--name quickcv-browserless-1`)
- Consistent naming for easy management
- Allows for easy stop/start/restart operations

### Auto-restart (`--restart unless-stopped`)
- Container automatically restarts after system reboot
- Ensures Browserless is always available for PDF generation

## Common Issues

### ERR_NAME_NOT_RESOLVED
**Symptom**: PDF generation fails with hostname resolution errors
```
net::ERR_NAME_NOT_RESOLVED at http://host.docker.internal:3000/pdf/...
```

**Solution**: Ensure the `--add-host=host.docker.internal:host-gateway` flag is present when running the container.

### Connection Refused
**Symptom**: Cannot connect to Browserless WebSocket
```
Error: connect ECONNREFUSED 127.0.0.1:3001
```

**Solutions**:
1. Check if container is running: `docker ps --filter name=quickcv-browserless-1`
2. Verify port mapping: Container should show `0.0.0.0:3001->3000/tcp`
3. Check `.env` file has correct `BROWSER_WS_ENDPOINT`

### Port Conflicts
**Symptom**: Container fails to start with port binding errors

**Solution**: Choose a different host port:
```bash
# Use port 3002 instead
docker run -d --name quickcv-browserless-1 -p 3002:3000 ...
# Update .env accordingly
BROWSER_WS_ENDPOINT=ws://localhost:3002?token=local-dev-token-quickcv
```

## Management Commands

```bash
# Check container status
docker ps --filter name=quickcv-browserless-1

# View container logs
docker logs quickcv-browserless-1

# Stop container
docker stop quickcv-browserless-1

# Start existing container
docker start quickcv-browserless-1

# Remove container (will need to recreate)
docker rm quickcv-browserless-1

# Restart with new configuration
docker stop quickcv-browserless-1
docker rm quickcv-browserless-1
# Run the docker run command again with desired changes
```

## Architecture

```
┌─────────────────┐     WebSocket     ┌─────────────────────┐
│   QuickCV App   │ ───────────────→  │  Browserless        │
│  (Host:3000)    │  localhost:3001   │  (Container:3000)   │
└─────────────────┘                   └─────────────────────┘
                                               │
                                               │ HTTP Request
                                               │ host.docker.internal:3000
                                               ▼
                                      ┌─────────────────┐
                                      │   QuickCV App   │
                                      │  (Host:3000)    │
                                      └─────────────────┘
```

1. QuickCV connects to Browserless via WebSocket (`localhost:3001`)
2. QuickCV sends PDF generation request with URL
3. Browserless fetches HTML content from `host.docker.internal:3000`
4. Browserless renders HTML to PDF and returns it

## Token Configuration

The token `local-dev-token-quickcv` is used for:
- Authentication with Browserless service
- Passed as query parameter in WebSocket connection
- Can be customized in `.env` file

For production, use a secure, randomly generated token.