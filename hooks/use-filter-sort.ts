import { useMemo, useState } from "react";

export type SortOption = "newest" | "oldest" | "name-asc" | "name-desc" | "updated";

interface UseFilterSortOptions<T> {
  defaultFilter?: string;
  defaultSort?: SortOption;
  filterFn?: (item: T, filter: string) => boolean;
  sortFn?: (a: T, b: T, sort: SortOption) => number;
  getItemDate?: (item: T) => Date;
  getItemName?: (item: T) => string;
  getItemUpdateDate?: (item: T) => Date;
}

export function useFilterSort<T>(items: T[] | undefined, options: UseFilterSortOptions<T> = {}) {
  const {
    defaultFilter = "all",
    defaultSort = "newest",
    filterFn,
    sortFn,
    getItemDate = (item: any) => new Date(item.createdAt),
    getItemName = (item: any) => item.title || "",
    getItemUpdateDate = (item: any) => new Date(item.updatedAt),
  } = options;

  const [filter, setFilter] = useState<string>(defaultFilter);
  const [sort, setSort] = useState<SortOption>(defaultSort);

  const filteredAndSortedItems = useMemo(() => {
    // Handle undefined or null items
    if (!items || !Array.isArray(items)) {
      return [];
    }

    let filtered = [...items];

    // Apply custom filter function if provided
    if (filterFn && filter !== "all") {
      filtered = filtered.filter((item) => filterFn(item, filter));
    }

    // Apply custom sort function if provided, otherwise use default
    if (sortFn) {
      filtered.sort((a, b) => sortFn(a, b, sort));
    } else {
      // Default sort logic
      switch (sort) {
        case "newest":
          filtered.sort((a, b) => getItemDate(b).getTime() - getItemDate(a).getTime());
          break;
        case "oldest":
          filtered.sort((a, b) => getItemDate(a).getTime() - getItemDate(b).getTime());
          break;
        case "name-asc":
          filtered.sort((a, b) => getItemName(a).localeCompare(getItemName(b)));
          break;
        case "name-desc":
          filtered.sort((a, b) => getItemName(b).localeCompare(getItemName(a)));
          break;
        case "updated":
          filtered.sort((a, b) => getItemUpdateDate(b).getTime() - getItemUpdateDate(a).getTime());
          break;
      }
    }

    return filtered;
  }, [items, filter, sort, filterFn, sortFn, getItemDate, getItemName, getItemUpdateDate]);

  const resetFilters = () => {
    setFilter(defaultFilter);
    setSort(defaultSort);
  };

  return {
    filter,
    setFilter,
    sort,
    setSort,
    filteredAndSortedItems,
    resetFilters,
  };
}
