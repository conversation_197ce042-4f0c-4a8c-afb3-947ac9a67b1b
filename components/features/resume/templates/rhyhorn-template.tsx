import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  ContactInfo,
  EducationItem,
  ExperienceItem,
  formatLocation,
  getFullName,
  getSectionTranslations,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom Section component with rhyhorn-specific styling
const RhyhornSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="resume-section mb-6">
    <h2 className="section-title text-base font-bold mb-3 text-gray-900 border-b border-gray-900 pb-1">{title}</h2>
    {children}
  </section>
);

/**
 * Rhyhorn Resume Template - Clean Professional Layout
 * - Simple single-column layout matching reference design
 * - Header with photo on left, contact info on right
 * - Clean typography and minimal styling
 * - Professional appearance with clear sections
 * - ATS-friendly structure with proper hierarchy
 */
const RhyhornTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`rhyhorn-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <main className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
        {/* Header Section */}
        <header className="resume-header mb-6">
          <div className="flex items-start gap-6 mb-4">
            {/* Left side: Photo */}
            {resume.showPhoto && resume.photo ? (
              <div className="flex-shrink-0">
                <Image alt={fullName} className="w-20 h-24 object-cover" height={96} src={resume.photo} width={80} />
              </div>
            ) : null}

            {/* Right side: Name, Title, Contact */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-1">{fullName}</h1>
              <p className="text-lg text-gray-700 mb-4">{resume.jobTitle}</p>

              {/* Contact Information */}
              <ContactInfo
                className="space-y-1 text-sm text-gray-700"
                email={resume.email}
                location={location}
                phone={resume.phone}
                variant="vertical"
                website={resume.website}
              />
            </div>
          </div>
        </header>

        {/* Profiles Section */}
        {resume.profiles && resume.profiles.length > 0 && (
          <RhyhornSection title={sectionTitles.profiles}>
            <SocialProfile className="" layout="grid" profiles={resume.profiles} showNetworkLabel={true} />
          </RhyhornSection>
        )}

        {/* Summary Section */}
        {resume.bio && (
          <RhyhornSection title={sectionTitles.summary}>
            <ProfessionalSummary
              bio={resume.bio}
              className="text-sm text-gray-700 leading-relaxed"
              variant="paragraph"
            />
          </RhyhornSection>
        )}

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <RhyhornSection title={sectionTitles.experience}>
            <div className="space-y-6">
              {resume.experiences.map((exp) => (
                <ExperienceItem
                  key={exp.id}
                  className=""
                  experience={exp}
                  locale={locale}
                  showWebsiteIcon={true}
                  variant="standard"
                />
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <RhyhornSection title={sectionTitles.education}>
            <div className="space-y-4">
              {resume.educations.map((edu) => (
                <EducationItem key={edu.id} className="" education={edu} locale={locale} variant="standard" />
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <RhyhornSection title={sectionTitles.projects}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {resume.projects.map((project) => (
                <ProjectItem
                  key={project.id}
                  className=""
                  project={project}
                  showClient={true}
                  showTechnologies={false}
                  variant="standard"
                />
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Certifications Section */}
        {resume.certifications && resume.certifications.length > 0 && (
          <RhyhornSection title={sectionTitles.certifications}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {resume.certifications.map((cert) => (
                <CertificationItem
                  key={cert.id}
                  certification={cert}
                  className="flex justify-between items-start"
                  showCredentialId={false}
                />
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <RhyhornSection title={sectionTitles.skills}>
            <SkillsSection
              className="grid grid-cols-3 gap-6"
              layout="grid"
              showProficiency={false}
              skills={resume.skills}
            />
          </RhyhornSection>
        )}

        {/* Languages Section */}
        {resume.languages && resume.languages.length > 0 && (
          <RhyhornSection title={sectionTitles.languages}>
            <div className="grid grid-cols-2 gap-4">
              {resume.languages.map((language) => (
                <LanguageItem key={language.id} className="" language={language} showBars={false} showLevel={true} />
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Awards Section */}
        {resume.awards && resume.awards.length > 0 && (
          <RhyhornSection title={sectionTitles.awards}>
            <div className="space-y-3">
              {resume.awards.map((award) => (
                <div key={award.id} className="award-item flex justify-between items-start">
                  <div>
                    <h4 className="font-bold text-gray-900 text-sm">{award.title}</h4>
                    <p className="text-gray-700 text-sm">{award.issuer}</p>
                    {award.description && (
                      <div
                        dangerouslySetInnerHTML={{ __html: award.description }}
                        className="text-gray-700 text-sm mt-1"
                      />
                    )}
                  </div>
                  {award.dateReceived && (
                    <span className="text-gray-600 text-sm">
                      {new Date(award.dateReceived).toLocaleDateString(locale, {
                        year: "numeric",
                        month: "short",
                      })}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Volunteering Section */}
        {resume.volunteerings && resume.volunteerings.length > 0 && (
          <RhyhornSection title={sectionTitles.volunteering}>
            <div className="space-y-4">
              {resume.volunteerings.map((volunteering, index) => (
                <div key={volunteering.id || index} className="volunteering-item">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
                      <p className="text-gray-700 text-sm">{volunteering.organization}</p>
                    </div>
                    {(volunteering.startDate || volunteering.endDate) && (
                      <span className="text-gray-600 text-sm">
                        {volunteering.startDate && volunteering.endDate
                          ? `${new Date(volunteering.startDate).toLocaleDateString(locale, { year: "numeric", month: "short" })} - ${new Date(volunteering.endDate).toLocaleDateString(locale, { year: "numeric", month: "short" })}`
                          : volunteering.startDate
                            ? new Date(volunteering.startDate).toLocaleDateString(locale, {
                                year: "numeric",
                                month: "short",
                              })
                            : volunteering.endDate
                              ? new Date(volunteering.endDate).toLocaleDateString(locale, {
                                  year: "numeric",
                                  month: "short",
                                })
                              : ""}
                      </span>
                    )}
                  </div>
                  {volunteering.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: volunteering.description,
                      }}
                      className="text-gray-700 text-sm mt-2"
                    />
                  )}
                </div>
              ))}
            </div>
          </RhyhornSection>
        )}

        {/* Hobbies Section */}
        {resume.hobbies && resume.hobbies.length > 0 && (
          <RhyhornSection title={sectionTitles.hobbies}>
            <p className="text-gray-700 text-sm leading-relaxed">
              {resume.hobbies.map((hobby) => hobby.name).join(", ")}
            </p>
          </RhyhornSection>
        )}

        {/* References Section */}
        {resume.references && resume.references.length > 0 && (
          <RhyhornSection title={sectionTitles.references}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {resume.references.map((reference) => (
                <div key={reference.id} className="reference-item">
                  <h4 className="font-bold text-gray-900">{reference.name}</h4>
                  <p className="text-gray-700 text-sm">
                    {reference.position} at {reference.company}
                  </p>
                  <div className="text-gray-600 text-sm mt-1">
                    {reference.email && (
                      <p className="flex items-center gap-1">
                        <span>✉️</span>
                        <span>{reference.email}</span>
                      </p>
                    )}
                    {reference.phone && (
                      <p className="flex items-center gap-1">
                        <span>📞</span>
                        <span>{reference.phone}</span>
                      </p>
                    )}
                  </div>
                  {reference.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: reference.description,
                      }}
                      className="text-gray-700 text-sm mt-2"
                    />
                  )}
                </div>
              ))}
            </div>
          </RhyhornSection>
        )}
      </main>
    </div>
  );
};

export default RhyhornTemplate;
