import Image from "next/image";
import React from "react";

import { getFullName } from "@/components/features/resume/templates/shared/utils";
import { getRTLFlexClasses, useRTL } from "../shared/utils";

// Website-specific section component
export interface WebsiteSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  id?: string;
}

export const WebsiteSection: React.FC<WebsiteSectionProps> = ({ title, children, className = "", id }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  return (
    <section className={`website-section mb-8 ${className}`} id={id}>
      <h2 className={`text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2 ${rtlClasses.textStart}`}>
        {title}
      </h2>
      <div className={rtlClasses.textStart}>{children}</div>
    </section>
  );
};

// Hero section component for websites
export interface WebsiteHeroProps {
  firstName: string;
  lastName: string;
  jobTitle: string;
  bio?: string;
  photo?: string;
  showPhoto?: boolean;
  className?: string;
}

export const WebsiteHero: React.FC<WebsiteHeroProps> = ({
  firstName,
  lastName,
  jobTitle,
  bio,
  photo,
  showPhoto = true,
  className = "",
}) => {
  const fullName = getFullName(firstName, lastName);
  const displayTitle = jobTitle;
  const displayBio = bio;

  return (
    <section className={`website-hero text-center py-16 px-4 ${className}`}>
      {showPhoto && photo && (
        <div className="mb-6">
          <Image
            alt={fullName}
            className="w-32 h-32 rounded-full object-cover mx-auto shadow-lg"
            height={128}
            src={photo}
            width={128}
          />
        </div>
      )}
      <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">{fullName}</h1>
      <p className="text-xl md:text-2xl text-gray-600 mb-6">{displayTitle}</p>
      {displayBio && (
        <div
          dangerouslySetInnerHTML={{ __html: displayBio }}
          className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed"
        />
      )}
    </section>
  );
};

// Contact section component for websites
export interface WebsiteContactProps {
  email?: string;
  website?: string;
  location?: string;
  className?: string;
}

export const WebsiteContact: React.FC<WebsiteContactProps> = ({ email, website, location, className = "" }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  const contactItems = [
    {
      label: "Email",
      value: email,
      href: email ? `mailto:${email}` : null,
      icon: "✉️",
    },
    { label: "Website", value: website, href: website, icon: "🌐" },
    { label: "Location", value: location, href: null, icon: "📍" },
  ].filter((item) => item.value);

  if (contactItems.length === 0) return null;

  return (
    <section className={`website-contact text-center py-8 ${className}`}>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Get In Touch</h2>
      <div className={`flex flex-wrap ${rtlClasses.justifyStart} gap-6 justify-center`}>
        {contactItems.map((item, index) => (
          <div key={index} className={`flex items-center text-lg ${rtlClasses.flexRow}`}>
            <span className={`${rtlClasses.marginEnd} text-xl`}>{item.icon}</span>
            {item.href ? (
              <a href={item.href} className="text-blue-600 hover:text-blue-800 transition-colors">
                {item.value}
              </a>
            ) : (
              <span className="text-gray-700">{item.value}</span>
            )}
          </div>
        ))}
      </div>
    </section>
  );
};
