"use client";

import { Card, CardBody } from "@heroui/card";
import { Icon } from "@iconify/react";

const technologies = [
  { name: "Next.js 15", desc: "Latest React framework", icon: "tabler:brand-nextjs" },
  { name: "TypeScript", desc: "Type-safe development", icon: "tabler:brand-typescript" },
  { name: "Tailwind CSS", desc: "Utility-first styling", icon: "tabler:brand-tailwind" },
  { name: "Drizzle ORM", desc: "Modern database toolkit", icon: "tabler:database" },
  { name: "<PERSON> Auth", desc: "Secure authentication", icon: "tabler:shield-lock" },
  { name: "HeroUI v2", desc: "Beautiful components", icon: "tabler:components" },
];

export default function TechStack() {
  return (
    <section className="py-20 bg-gradient-to-br from-purple-50/30 via-background to-pink-50/30">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-lg">
              <Icon icon="tabler:code" className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">
              Built with Modern Technology
            </h2>
          </div>
          <p className="text-gray-600 dark:text-gray-400 text-lg mb-8">
            We use cutting-edge technologies to deliver a fast, reliable, and secure experience for all our users.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {technologies.map((tech) => (
            <Card key={tech.name} className="hover:shadow-lg transition-all duration-300">
              <CardBody className="p-6 text-center">
                <Icon icon={tech.icon} className="w-8 h-8 mx-auto mb-3 text-purple-600 dark:text-purple-400" />
                <h3 className="font-semibold mb-2">{tech.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{tech.desc}</p>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
