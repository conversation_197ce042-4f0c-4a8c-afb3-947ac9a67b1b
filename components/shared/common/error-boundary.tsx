"use client";

import React from "react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

declare global {
  interface Window {
    posthog?: {
      captureException: (error: Error, properties?: Record<string, any>) => void;
    };
  }
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Send error to PostHog
    if (typeof window !== "undefined" && window.posthog) {
      window.posthog.captureException(error, {
        errorInfo,
        errorBoundary: true,
        componentStack: errorInfo.componentStack,
      });
    }

    console.error("Error caught by boundary:", error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="text-center max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-red-600">Something went wrong</h2>
            <p className="text-gray-600 mb-4">
              We're sorry, but something unexpected happened. The error has been reported.
            </p>
            <button
              onClick={this.resetError}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for capturing errors in functional components
export function useErrorCapture() {
  return React.useCallback((error: Error, context?: Record<string, any>) => {
    if (typeof window !== "undefined" && window.posthog) {
      window.posthog.captureException(error, {
        ...context,
        capturedByHook: true,
      });
    }
    console.error("Error captured:", error, context);
  }, []);
}
