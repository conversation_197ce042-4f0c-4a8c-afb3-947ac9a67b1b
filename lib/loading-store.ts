import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

export interface LoadingState {
  id: string;
  type: "mutation" | "navigation" | "file-upload";
  message?: string;
  progress?: number;
}

interface LoadingStore {
  loadingStates: LoadingState[];
  isGlobalLoading: boolean;
  startLoading: (id: string, type: LoadingState["type"], message?: string) => void;
  updateLoading: (id: string, updates: Partial<Pick<LoadingState, "message" | "progress">>) => void;
  stopLoading: (id: string) => void;
  stopAllLoading: () => void;
  getLoadingState: (id: string) => LoadingState | undefined;
  isLoading: (id: string) => boolean;
}

export const useLoadingStore = create<LoadingStore>()(
  subscribeWithSelector((set, get) => ({
    loadingStates: [],
    isGlobalLoading: false,

    startLoading: (id, type, message) => {
      set((state) => {
        const existing = state.loadingStates.findIndex((s) => s.id === id);
        const newState: LoadingState = { id, type, message };

        if (existing >= 0) {
          const updated = [...state.loadingStates];
          updated[existing] = newState;
          return {
            loadingStates: updated,
            isGlobalLoading: true,
          };
        }

        return {
          loadingStates: [...state.loadingStates, newState],
          isGlobalLoading: true,
        };
      });
    },

    updateLoading: (id, updates) => {
      set((state) => ({
        loadingStates: state.loadingStates.map((s) => (s.id === id ? { ...s, ...updates } : s)),
      }));
    },

    stopLoading: (id) => {
      set((state) => {
        const newLoadingStates = state.loadingStates.filter((s) => s.id !== id);
        return {
          loadingStates: newLoadingStates,
          isGlobalLoading: newLoadingStates.length > 0,
        };
      });
    },

    stopAllLoading: () => {
      set({
        loadingStates: [],
        isGlobalLoading: false,
      });
    },

    getLoadingState: (id) => {
      return get().loadingStates.find((s) => s.id === id);
    },

    isLoading: (id) => {
      return get().loadingStates.some((s) => s.id === id);
    },
  })),
);
