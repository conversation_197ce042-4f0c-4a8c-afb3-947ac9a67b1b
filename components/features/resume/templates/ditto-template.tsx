import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  ContactInfo,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
  getSectionTranslations,
  getA4PageClasses,
  getHeaderClasses,
  getPhotoClasses,
  getSectionSpacing,
  getTitleClasses,
  getTextClasses,
  getGridClasses,
  getPrintSafeClasses,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom section component with teal left border for ditto template
const DittoSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => {
  return (
    <section className={getSectionSpacing()}>
      <div className="border-l-2 md:border-l-4 border-teal-500 pl-3 md:pl-4">
        <h2 className={`${getTitleClasses(2)} text-gray-900 mb-2 md:mb-3`}>{title}</h2>
        {children}
      </div>
    </section>
  );
};

// Simple card component
const Card: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => <div className={`mb-3 md:mb-4 ${className}`}>{children}</div>;

/**
 * Ditto Resume Template
 * - Adaptive, flexible design that emphasizes versatility
 * - Clean modern layout with dynamic elements
 * - Responsive design that adapts to content
 * - ATS-friendly structure with modern appeal
 * - Inspired by adaptability and transformation
 */
const DittoTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  // Adaptive layout based on content availability
  const hasPhoto = resume.showPhoto && resume.photo;

  return (
    <div className={`ditto-template bg-white text-gray-800 font-sans ${className}`}>
      <div className="max-w-6xl mx-auto">
        {/* Teal Header */}
        <header className="resume-header bg-teal-500 text-white p-8 mb-0">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-2">{fullName}</h1>
            <h2 className="text-xl text-teal-100 font-medium">{resume.jobTitle}</h2>
          </div>
        </header>

        {/* Contact info outside header */}
        <div className="contact-section bg-white p-4">
          <ContactInfo
            className="justify-center"
            email={resume.email}
            location={location}
            phone={resume.phone}
            variant="horizontal"
            website={resume.website}
          />
        </div>

        {/* Two-column layout with sidebar */}
        <main className="grid grid-cols-1 lg:grid-cols-4 gap-0">
          {/* Left Sidebar */}
          <div className="sidebar-content lg:col-span-1 bg-gray-50 p-6 space-y-6">
            {/* Photo */}
            {hasPhoto && (
              <div className="bg-gray-300 p-4 rounded">
                <Image
                  alt={fullName}
                  className="w-full h-auto object-cover"
                  height={200}
                  src={resume.photo}
                  width={200}
                />
              </div>
            )}

            {/* Profiles */}
            {resume.profiles && resume.profiles.length > 0 && (
              <div className="sidebar-section">
                <h3 className="section-title font-bold text-gray-900 mb-3">{sectionTitles.profiles}</h3>
                <SocialProfile layout="vertical" profiles={resume.profiles} showNetworkLabel={true} />
              </div>
            )}

            {/* Skills */}
            {resume.skills && resume.skills.length > 0 && (
              <div className="sidebar-section">
                <h3 className="section-title font-bold text-gray-900 mb-3">{sectionTitles.skills}</h3>
                <SkillsSection layout="list" skills={resume.skills} />
              </div>
            )}

            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <div className="sidebar-section">
                <h3 className="section-title font-bold text-gray-900 mb-3">{sectionTitles.certifications}</h3>
                <div className="space-y-3">
                  {resume.certifications.map((cert) => (
                    <CertificationItem
                      key={cert.id}
                      certification={cert}
                      className="text-sm"
                      showCredentialId={false}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <div className="sidebar-section">
                <h3 className="section-title font-bold text-gray-900 mb-3">{sectionTitles.languages}</h3>
                <div className="space-y-2">
                  {resume.languages.map((lang) => (
                    <LanguageItem key={lang.id} className="text-sm" language={lang} showBars={false} showLevel={true} />
                  ))}
                </div>
              </div>
            )}

            {/* References */}
            <div className="sidebar-section">
              <h3 className="section-title font-bold text-gray-900 mb-3">{sectionTitles.references}</h3>
              <p className="text-gray-700 text-sm">Available upon request</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 p-6 space-y-6">
            {/* Summary */}
            {resume.bio && (
              <DittoSection title={sectionTitles.summary}>
                <ProfessionalSummary bio={resume.bio} variant="paragraph" />
              </DittoSection>
            )}

            {/* Experience */}
            {resume.experiences && resume.experiences.length > 0 && (
              <DittoSection title={sectionTitles.experience}>
                <div className="space-y-6">
                  {resume.experiences.map((exp) => (
                    <Card key={exp.id}>
                      <ExperienceItem experience={exp} locale={locale} showWebsiteIcon={true} variant="standard" />
                    </Card>
                  ))}
                </div>
              </DittoSection>
            )}

            {/* Education */}
            {resume.educations && resume.educations.length > 0 && (
              <DittoSection title={sectionTitles.education}>
                <div className="space-y-4">
                  {resume.educations.map((edu) => (
                    <Card key={edu.id}>
                      <EducationItem education={edu} locale={locale} variant="standard" />
                    </Card>
                  ))}
                </div>
              </DittoSection>
            )}

            {/* Projects */}
            {resume.projects && resume.projects.length > 0 && (
              <DittoSection title={sectionTitles.projects}>
                <div className="grid grid-cols-2 gap-6">
                  {resume.projects.map((project) => (
                    <Card key={project.id}>
                      <ProjectItem project={project} showClient={true} showTechnologies={false} variant="standard" />
                    </Card>
                  ))}
                </div>
              </DittoSection>
            )}

            {/* Awards */}
            {resume.awards && resume.awards.length > 0 && (
              <DittoSection title={sectionTitles.awards}>
                <div className="space-y-3">
                  {resume.awards.map((award) => (
                    <Card key={award.id}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-bold text-gray-900 text-sm">{award.title}</h4>
                          <p className="text-gray-700 text-sm">{award.issuer}</p>
                          {award.description && (
                            <div
                              dangerouslySetInnerHTML={{ __html: award.description }}
                              className="text-gray-700 text-sm mt-1"
                            />
                          )}
                        </div>
                        {award.dateReceived && (
                          <span className="text-gray-600 text-sm">{formatDate(award.dateReceived, false, locale)}</span>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              </DittoSection>
            )}

            {/* Volunteering */}
            {resume.volunteerings && resume.volunteerings.length > 0 && (
              <DittoSection title={sectionTitles.volunteering}>
                <div className="space-y-4">
                  {resume.volunteerings.map((volunteering) => (
                    <Card key={volunteering.id}>
                      <div className="flex justify-between items-start mb-1">
                        <div>
                          <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
                          <p className="text-gray-700 text-sm">{volunteering.organization}</p>
                        </div>
                        {(volunteering.startDate || volunteering.endDate) && (
                          <span className="text-gray-600 text-sm">
                            {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                          </span>
                        )}
                      </div>
                      {volunteering.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: volunteering.description,
                          }}
                          className="text-gray-700 text-sm mt-2"
                        />
                      )}
                    </Card>
                  ))}
                </div>
              </DittoSection>
            )}

            {/* Hobbies */}
            {resume.hobbies && resume.hobbies.length > 0 && (
              <DittoSection title={sectionTitles.hobbies}>
                <Card>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {resume.hobbies.map((hobby) => hobby.name).join(", ")}
                  </p>
                </Card>
              </DittoSection>
            )}

            {/* References */}
            {resume.references && resume.references.length > 0 ? (
              <DittoSection title={sectionTitles.references}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {resume.references.map((reference) => (
                    <Card key={reference.id}>
                      <h4 className="font-bold text-gray-900">{reference.name}</h4>
                      <p className="text-gray-700 text-sm">
                        {reference.position} at {reference.company}
                      </p>
                      <div className="text-gray-600 text-sm mt-1">
                        {reference.email && (
                          <p className="flex items-center gap-1">
                            <span>✉️</span>
                            <span>{reference.email}</span>
                          </p>
                        )}
                        {reference.phone && (
                          <p className="flex items-center gap-1">
                            <span>📞</span>
                            <span>{reference.phone}</span>
                          </p>
                        )}
                      </div>
                      {reference.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: reference.description,
                          }}
                          className="text-gray-700 text-sm mt-2"
                        />
                      )}
                    </Card>
                  ))}
                </div>
              </DittoSection>
            ) : (
              <DittoSection title={sectionTitles.references}>
                <Card>
                  <p className="text-gray-700 text-sm">Available upon request</p>
                </Card>
              </DittoSection>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DittoTemplate;
