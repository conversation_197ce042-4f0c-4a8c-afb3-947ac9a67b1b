"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter, useSearchParams } from "next/navigation";
import { PREMIUM_FEATURES } from "@/config/payment-features";

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const transactionId = searchParams.get("transaction");

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-success-50 via-primary-50/30 to-secondary-50/30 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 right-1/4 w-96 h-96 bg-success/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen py-12 px-4">
        <div className="max-w-2xl w-full">
          {/* Success Animation */}
          <div className="text-center mb-8">
            <div className="relative inline-block">
              <div className="w-24 h-24 bg-success/10 rounded-full flex items-center justify-center mb-6 mx-auto animate-pulse">
                <div className="w-20 h-20 bg-success/20 rounded-full flex items-center justify-center">
                  <Icon icon="heroicons:check-circle-20-solid" className="w-12 h-12 text-success animate-bounce" />
                </div>
              </div>
              {/* Confetti effect */}
              <div className="absolute -top-4 -left-4 w-32 h-32 pointer-events-none">
                <div className="absolute top-0 left-0 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
                <div className="absolute top-2 right-2 w-1 h-1 bg-pink-400 rounded-full animate-ping delay-75"></div>
                <div className="absolute bottom-4 left-2 w-1.5 h-1.5 bg-blue-400 rounded-full animate-ping delay-150"></div>
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-green-400 rounded-full animate-ping delay-300"></div>
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-success to-primary bg-clip-text text-transparent">
              Payment Successful!
            </h1>
            <p className="text-xl text-default-600 mb-2">Welcome to QuickCV Premium!</p>
            {transactionId && (
              <p className="text-sm text-default-500">
                Transaction ID: <span className="font-mono">{transactionId}</span>
              </p>
            )}
          </div>

          {/* Main Success Card */}
          <Card className="shadow-2xl border border-success/20 bg-gradient-to-b from-background to-success-50/20 backdrop-blur-sm mb-8">
            <CardHeader className="text-center pb-4">
              <div className="w-full">
                <h2 className="text-2xl font-bold mb-2">Your upgrade is now active!</h2>
              </div>
            </CardHeader>

            <CardBody className="pt-0">
              {/* Premium Features Grid */}
              <div className="grid md:grid-cols-2 gap-4 mb-8">
                {PREMIUM_FEATURES.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 rounded-lg bg-success/5 hover:bg-success/10 transition-colors"
                  >
                    <div className="bg-success/10 rounded-full p-1 mt-0.5">
                      <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-success" />
                    </div>
                    <div>
                      <span className="font-medium text-default-800">
                        {feature.titleKey
                          .replace("features.", "")
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                      <p className="text-sm text-default-600 mt-1">
                        {feature.descriptionKey.replace("features.", "").replace(/_/g, " ")}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <Button
                    color="primary"
                    size="lg"
                    className="h-14 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-secondary"
                    startContent={<Icon icon="heroicons:plus-20-solid" className="w-5 h-5" />}
                    onPress={() => handleNavigate("/resumes")}
                  >
                    Create New Resume
                  </Button>

                  <Button
                    color="success"
                    variant="bordered"
                    size="lg"
                    className="h-14 text-lg font-semibold shadow-sm hover:shadow-lg transition-all duration-300"
                    startContent={<Icon icon="heroicons:folder-20-solid" className="w-5 h-5" />}
                    onPress={() => handleNavigate("/resumes")}
                  >
                    Go to My Resumes
                  </Button>
                </div>

                <Button
                  variant="light"
                  size="lg"
                  className="w-full h-12 text-default-600 hover:text-primary transition-colors"
                  startContent={<Icon icon="heroicons:squares-2x2-20-solid" className="w-5 h-5" />}
                  onPress={() => handleNavigate("/templates")}
                >
                  View Templates
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="flex items-center justify-center gap-6 mt-8 pt-6 border-t border-default-200">
                <div className="flex items-center gap-2 text-sm text-default-500">
                  <Icon icon="heroicons:shield-check-20-solid" className="w-5 h-5 text-success" />
                  Lifetime Access Active
                </div>
                <div className="flex items-center gap-2 text-sm text-default-500">
                  <Icon icon="heroicons:clock-20-solid" className="w-5 h-5 text-primary" />
                  Ready to Use
                </div>
                <div className="flex items-center gap-2 text-sm text-default-500">
                  <Icon icon="heroicons:heart-20-solid" className="w-5 h-5 text-danger" />
                  Thank You!
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
}
