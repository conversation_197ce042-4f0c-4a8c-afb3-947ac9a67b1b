export const routes = {
  resumeEditPath: (id: number) => `/resumes/edit/${id}`,
  resumesPath: () => `/resumes`,
  resumePath: (id: number) => `/resumes/${id}`,
  fetchResumePath: (id: number) => `/resumes/${id}`,
  fetchResumesPath: () => `/resumes`,
};

// Client-side route helpers
export const useRoutes = () => {
  return {
    resumeEditPath: (id: number) => routes.resumeEditPath(id),
    resumesPath: () => routes.resumesPath(),
    resumePath: (id: number) => routes.resumePath(id),
    fetchResumePath: (id: number) => routes.fetchResumePath(id),
    fetchResumesPath: () => routes.fetchResumesPath(),
  };
};
