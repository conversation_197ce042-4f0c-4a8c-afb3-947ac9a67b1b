import { clerkClient } from "@clerk/nextjs/server";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  hobbies,
  languages,
  profiles,
  projects,
  references,
  resumes,
  skills,
  users,
  volunteerings,
  websites,
} from "@/db/schema";
import { paddleServer } from "@/lib/paddle-server";
import { protectedProcedure, router } from "../trpc";

export const userRouter = router({
  // Get current user with premium status
  getCurrentUser: protectedProcedure.query(async ({ ctx }) => {
    return {
      id: ctx.user.id,
      clerkId: ctx.user.clerkId,
      emailAddress: ctx.user.emailAddress,
      isPremium: ctx.user.isPremium,
      purchasedAt: ctx.user.purchasedAt,
    };
  }),

  // Delete user account
  deleteAccount: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const { clerkId } = ctx.user;

      // First, delete all user's resumes and their nested data
      // Get all user's resumes
      const userResumes = await db.query.resumes.findMany({
        where: eq(resumes.userId, clerkId),
      });

      // Delete all nested resume data
      for (const resume of userResumes) {
        // Delete all resume-related data in sequence
        await db.delete(educations).where(eq(educations.resumeId, resume.id));
        await db.delete(experiences).where(eq(experiences.resumeId, resume.id));
        await db.delete(projects).where(eq(projects.resumeId, resume.id));
        await db.delete(awards).where(eq(awards.resumeId, resume.id));
        await db.delete(certifications).where(eq(certifications.resumeId, resume.id));
        await db.delete(skills).where(eq(skills.resumeId, resume.id));
        await db.delete(languages).where(eq(languages.resumeId, resume.id));
        await db.delete(references).where(eq(references.resumeId, resume.id));
        await db.delete(hobbies).where(eq(hobbies.resumeId, resume.id));
        await db.delete(volunteerings).where(eq(volunteerings.resumeId, resume.id));
        await db.delete(profiles).where(eq(profiles.resumeId, resume.id));
      }

      // Delete all user's websites
      await db.delete(websites).where(eq(websites.userId, clerkId));

      // Delete all user's resumes
      await db.delete(resumes).where(eq(resumes.userId, clerkId));

      // Delete user from our database
      await db.delete(users).where(eq(users.clerkId, clerkId));

      // Finally, delete user from Clerk
      const client = await clerkClient();
      await client.users.deleteUser(clerkId);

      return { success: true };
    } catch (error) {
      console.error("Failed to delete user account:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete account. Please try again or contact support.",
      });
    }
  }),

  // Get user invoices from Paddle
  getInvoices: protectedProcedure.query(async ({ ctx }) => {
    try {
      const { paddleCustomerId } = ctx.user;

      // If user doesn't have a paddleCustomerId, return empty array
      if (!paddleCustomerId) {
        return [];
      }

      // Fetch invoices from Paddle using the user's paddleCustomerId
      const invoices = await paddleServer.getUserTransactions(paddleCustomerId);

      return invoices;
    } catch (error) {
      console.error("Failed to fetch user invoices:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch invoices. Please try again later.",
      });
    }
  }),

  // Revoke premium access
  revokePremiumAccess: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const { clerkId, emailAddress } = ctx.user;

      // Update user to revoke premium access - only set isPremium to false
      await db
        .update(users)
        .set({
          isPremium: false,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, clerkId));

      console.log(`✅ Premium access revoked for user: ${emailAddress} (${clerkId})`);

      return {
        success: true,
        message: "Premium access has been revoked successfully.",
      };
    } catch (error) {
      console.error("Failed to revoke premium access:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to revoke premium access. Please try again later.",
      });
    }
  }),
});

export type UserRouter = typeof userRouter;
