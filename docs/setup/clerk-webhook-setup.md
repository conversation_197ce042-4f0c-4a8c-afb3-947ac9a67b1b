# Clerk Webhook Setup Guide

This guide explains how to configure Clerk webhooks for real-time user synchronization with the QuickCV database.

## Overview

The webhook system automatically syncs user data between Clerk and our database when users are created, updated, or deleted. This ensures data consistency without manual intervention.

## 1. Environment Variables Setup

Add the webhook secret to your environment variables:

### `.env.local` (Development)
```bash
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_from_clerk_dashboard
```

### Production Environment
Ensure `CLERK_WEBHOOK_SECRET` is set in your production environment variables.

## 2. Clerk Dashboard Configuration

### Step 1: Access Clerk Dashboard
1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Select your QuickCV project

### Step 2: Navigate to Webhooks
1. In the sidebar, click **"Webhooks"**
2. Click **"+ Add Endpoint"**

### Step 3: Configure Webhook Endpoint

**Endpoint URL:**
- **Development**: `http://localhost:3000/api/webhook/clerk`
- **Production**: `https://yourdomain.com/api/webhook/clerk`

**Events to Subscribe:**
Select these specific events:
- ✅ `user.created`
- ✅ `user.updated` 
- ✅ `user.deleted`

**HTTP Method:** `POST`

### Step 4: Get Webhook Secret
1. After creating the endpoint, Clerk will generate a webhook secret
2. Copy the secret (starts with `whsec_`)
3. Add it to your environment variables

## 3. Development Testing

### Using ngrok for Local Testing
1. Install ngrok: `npm install -g ngrok`
2. Start your development server: `bun run dev`
3. Expose localhost in another terminal:
   ```bash
   ngrok http 3000
   ```
4. Update the webhook URL in Clerk Dashboard to your ngrok URL:
   ```
   https://your-ngrok-url.ngrok.io/api/webhook/clerk
   ```

### Test Webhook in Clerk Dashboard
1. Go to your webhook endpoint in Clerk Dashboard
2. Click **"Send test event"**
3. Select `user.created` event
4. Check your application logs for successful processing

## 4. Webhook Events Handled

The webhook endpoint (`/app/api/webhook/clerk/route.ts`) handles these events:

### `user.created`
- Creates new user in database with complete profile data
- Includes: clerkId, email, firstName, lastName, premium status

### `user.updated`
- Updates existing user data or creates if missing
- Syncs: email, firstName, lastName changes from Clerk

### `user.deleted`
- Removes user from database
- Hard delete (can be changed to soft delete if needed)

## 5. Monitoring Webhook Activity

### Success Log Messages
Check your application logs for these messages:
- ✅ `Clerk webhook received: user.created`
- ✅ `User created in database: clerk_user_id`
- ✅ `User updated in database: clerk_user_id`
- ✅ `User deleted from database: clerk_user_id`

### Error Log Messages
- ❌ `Error verifying webhook: [error details]`
- ❌ `Error handling webhook user.created: [error details]`
- ❌ `Error creating user in database: [error details]`

## 6. Troubleshooting

### Common Issues

**401 Unauthorized**
- Verify webhook secret matches between Clerk Dashboard and environment variables
- Check that `CLERK_WEBHOOK_SECRET` is properly set

**Webhook not triggered**
- Ensure correct events are selected in Clerk Dashboard
- Verify webhook URL is accessible and correct
- Check that HTTP method is set to POST

**Database errors**
- Ensure database connection is working
- Check database schema matches expected structure
- Verify database permissions

### Debug Steps

1. **Test webhook endpoint accessibility:**
   ```bash
   curl -X POST http://localhost:3000/api/webhook/clerk
   ```

2. **Verify environment variable:**
   ```bash
   echo $CLERK_WEBHOOK_SECRET
   ```

3. **Check application logs** for detailed error messages

4. **Test database connection** using other parts of the application

## 7. Architecture Details

### Webhook Handler (`/app/api/webhook/clerk/route.ts`)
- Verifies webhook signature using svix
- Routes events to appropriate handler functions
- Uses utility functions from `/lib/user-sync.ts`

### User Sync Utilities (`/lib/user-sync.ts`)
- `syncUserFromClerk()` - Handles create/update logic
- `deleteUserFromDatabase()` - Handles user deletion
- `getUserByClerkId()` - Retrieves user by Clerk ID
- `updateUserPremiumStatus()` - Manages premium subscriptions

### Fallback Sync (`/lib/auth-clerk.ts`)
- Provides reactive sync on user authentication
- Creates users if webhook was missed
- Updates profile data if changes detected
- Ensures data consistency on every auth check

## 8. Security Considerations

- Webhook signature verification prevents unauthorized requests
- Environment variables keep secrets secure
- Error handling prevents sensitive information leakage
- Database operations use prepared statements to prevent SQL injection

## 9. Production Deployment

1. Deploy application with webhook endpoint
2. Update webhook URL in Clerk Dashboard to production domain
3. Ensure `CLERK_WEBHOOK_SECRET` is set in production environment
4. Monitor logs for successful webhook processing
5. Test user registration/update flows end-to-end

## 10. Maintenance

### Regular Checks
- Monitor webhook delivery success rate in Clerk Dashboard
- Check application logs for recurring errors
- Verify user data synchronization accuracy

### Updates
- Keep webhook endpoint updated with schema changes
- Update event subscriptions if new Clerk events are needed
- Maintain fallback sync logic for reliability

---

## Quick Reference

**Webhook URL Format:** `https://yourdomain.com/api/webhook/clerk`

**Required Events:** `user.created`, `user.updated`, `user.deleted`

**Environment Variable:** `CLERK_WEBHOOK_SECRET=whsec_...`

**Test Command:** `curl -X POST http://localhost:3000/api/webhook/clerk`