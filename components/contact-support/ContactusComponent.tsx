import { useUser } from "@clerk/nextjs";
import { Textarea } from "@heroui/input";
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Input, Select, SelectItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import React, { useState } from "react";
import { submitSupportTicket } from "@/actions/support";

const ContactusComponent = (props: any) => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: "", content: "" });
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedPriority, setSelectedPriority] = useState("");
  const { user } = useUser();

  const handleSubmitTicket = async (formData: FormData) => {
    setIsLoading(true);
    setMessage({ type: "", content: "" });

    try {
      const result = await submitSupportTicket(formData);

      if (result.success) {
        setMessage({
          type: "success",
          content: `Thank you! Your support ticket has been submitted successfully. ${"ticketNumber" in result ? `Your ticket number is ${result.ticketNumber}.` : ""} We'll get back to you soon.`,
        });
        // Reset form
        const form = document.getElementById("support-form") as HTMLFormElement;
        form?.reset();
        setSelectedCategory("");
        setSelectedPriority("");
      } else {
        setMessage({
          type: "error",
          content:
            "error" in result
              ? result.error || "Sorry, there was an error submitting your ticket. Please try again."
              : "Sorry, there was an error submitting your ticket. Please try again.",
        });
      }
    } catch (error) {
      setMessage({ type: "error", content: "Sorry, there was an error submitting your ticket. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <h2 className="text-xl font-bold">Contact Support</h2>
      </CardHeader>
      <CardBody>
        <form id="support-form" action={handleSubmitTicket} className="space-y-6">
          <div className="grid md:grid-cols-2 gap-4">
            <Input
              name="name"
              label="Full Name"
              placeholder="Enter your full name"
              variant="bordered"
              isRequired
              defaultValue={user?.fullName || ""}
              startContent={<Icon icon="tabler:user" className="w-4 h-4 text-default-400" />}
            />
            <Input
              name="email"
              type="email"
              label="Email Address"
              placeholder="Enter your email address"
              variant="bordered"
              isRequired
              defaultValue={user?.primaryEmailAddress?.emailAddress || ""}
              startContent={<Icon icon="tabler:mail" className="w-4 h-4 text-default-400" />}
            />
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <Select
              name="category"
              label="Category"
              placeholder="Select a category"
              variant="bordered"
              isRequired
              defaultSelectedKeys={["general"]}
              startContent={<Icon icon="tabler:category" className="w-4 h-4 text-default-400" />}
            >
              <SelectItem key="general">General Question</SelectItem>
              <SelectItem key="technical">Technical Support</SelectItem>
              <SelectItem key="billing">Billing & Payment</SelectItem>
            </Select>

            <Select
              name="priority"
              label="Priority"
              placeholder="Select priority level"
              variant="bordered"
              isRequired
              defaultSelectedKeys={["normal"]}
              startContent={<Icon icon="tabler:flag" className="w-4 h-4 text-default-400" />}
            >
              <SelectItem key="low">Low Priority</SelectItem>
              <SelectItem key="normal">Normal Priority</SelectItem>
              <SelectItem key="high">High Priority</SelectItem>
              <SelectItem key="urgent">Urgent</SelectItem>
            </Select>
          </div>

          <Input
            name="subject"
            label="Subject"
            placeholder="Brief description of your issue"
            variant="bordered"
            isRequired
            startContent={<Icon icon="tabler:tag" className="w-4 h-4 text-default-400" />}
          />

          <Textarea
            name="message"
            label="Message"
            placeholder="Please describe your issue in detail. Include any error messages, steps to reproduce, or relevant information."
            variant="bordered"
            minRows={8}
            isRequired
            startContent={<Icon icon="tabler:message" className="w-4 h-4 text-default-400" />}
          />

          {message.content && (
            <div
              className={`p-4 rounded-lg ${message.type === "success" ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200" : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200"}`}
            >
              <div className="flex items-center gap-2">
                <Icon
                  icon={message.type === "success" ? "tabler:check-circle" : "tabler:alert-circle"}
                  className="w-5 h-5"
                />
                {message.content}
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <Button
              type="submit"
              color="primary"
              size="lg"
              isLoading={isLoading}
              startContent={!isLoading && <Icon icon="tabler:send" className="w-4 h-4" />}
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
            >
              {isLoading ? "Sending..." : "Submit Ticket"}
            </Button>
          </div>
        </form>
      </CardBody>
    </Card>
  );
};

export default ContactusComponent;
