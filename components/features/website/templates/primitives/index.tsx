import React from "react";

import { getRTLFlexClasses, useRTL } from "../shared/utils";

// Enhanced Card Component with RTL support
export interface WebsiteCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "elevated" | "outlined";
}

export const WebsiteCard: React.FC<WebsiteCardProps> = ({ children, className = "", variant = "default" }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  const variantClasses = {
    default: "bg-white p-6 rounded-lg shadow-sm border",
    elevated: "bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",
    outlined: "bg-transparent p-6 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-colors",
  };

  return <div className={`${variantClasses[variant]} ${rtlClasses.textStart} ${className}`}>{children}</div>;
};

// Flex Container with automatic RTL support
export interface WebsiteFlexProps {
  children: React.ReactNode;
  direction?: "row" | "col";
  justify?: "start" | "center" | "between" | "around" | "evenly";
  align?: "start" | "center" | "end";
  wrap?: boolean;
  gap?: number;
  className?: string;
}

export const WebsiteFlex: React.FC<WebsiteFlexProps> = ({
  children,
  direction = "row",
  justify = "start",
  align = "start",
  wrap = false,
  gap = 0,
  className = "",
}) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  const directionClass = direction === "row" ? rtlClasses.flexRow : "flex-col";
  const justifyClasses = {
    start: rtlClasses.justifyStart,
    center: "justify-center",
    between: "justify-between",
    around: "justify-around",
    evenly: "justify-evenly",
  };
  const alignClasses = {
    start: "items-start",
    center: "items-center",
    end: "items-end",
  };

  return (
    <div
      className={`flex ${directionClass} ${justifyClasses[justify]} ${alignClasses[align]} ${
        wrap ? "flex-wrap" : ""
      } ${gap > 0 ? `gap-${gap}` : ""} ${className}`}
    >
      {children}
    </div>
  );
};

// Grid Container with automatic RTL support
export interface WebsiteGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: number;
  className?: string;
}

export const WebsiteGrid: React.FC<WebsiteGridProps> = ({ children, cols = 1, gap = 6, className = "" }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  const colsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
    6: "grid-cols-1 md:grid-cols-3 lg:grid-cols-6",
    12: "grid-cols-12",
  };

  return <div className={`grid ${colsClasses[cols]} gap-${gap} ${rtlClasses.textStart} ${className}`}>{children}</div>;
};

// Text Component with automatic RTL alignment
export interface WebsiteTextProps {
  children: React.ReactNode;
  size?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
  weight?: "normal" | "medium" | "semibold" | "bold";
  color?: "gray-900" | "gray-700" | "gray-600" | "gray-500" | "blue-600" | "blue-700";
  align?: "start" | "center" | "end";
  className?: string;
}

export const WebsiteText: React.FC<WebsiteTextProps> = ({
  children,
  size = "base",
  weight = "normal",
  color = "gray-900",
  align = "start",
  className = "",
}) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  const sizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg",
    xl: "text-xl",
    "2xl": "text-2xl",
    "3xl": "text-3xl",
    "4xl": "text-4xl",
    "5xl": "text-5xl",
  };

  const weightClasses = {
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold",
  };

  const alignClasses = {
    start: rtlClasses.textStart,
    center: "text-center",
    end: rtlClasses.textEnd,
  };

  return (
    <span className={`${sizeClasses[size]} ${weightClasses[weight]} text-${color} ${alignClasses[align]} ${className}`}>
      {children}
    </span>
  );
};
