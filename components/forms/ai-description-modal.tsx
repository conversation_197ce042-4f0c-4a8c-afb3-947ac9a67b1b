"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Select,
  SelectItem,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useCallback, useEffect, useState } from "react";
import { type FeatureFlagKey, useFeatureFlagEnabled } from "@/hooks/use-feature-flags";
import { aiService, type ContextType } from "@/lib/ai-service";

interface AIDescriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (description: string) => void;
  contextType: ContextType;
  resumeContext: any;
  itemContext: any;
  currentDescription?: string;
  language?: "en" | "ar";
}

interface AIOptions {
  tone: "professional" | "casual" | "technical";
  length: "short" | "medium" | "detailed";
}

export function AIDescriptionModal({
  isOpen,
  onClose,
  onAccept,
  contextType,
  resumeContext,
  itemContext,
  currentDescription,
  language = "en",
}: AIDescriptionModalProps) {
  // PostHog feature flag integration
  const {
    isEnabled: aiEnabled,
    isLoading: flagLoading,
    trackUsage,
  } = useFeatureFlagEnabled("ai_generation_enhanced", false);

  // Simple AI access logic based on PostHog flags
  const canGenerate = aiEnabled;
  const isPremium = aiEnabled; // Assume premium access if AI is enabled
  const hasUsedFree = false; // Simplified for PostHog-only logic
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedDescriptions, setGeneratedDescriptions] = useState<string[]>([]);
  const [selectedDescription, setSelectedDescription] = useState<string>("");
  const [options, setOptions] = useState<AIOptions>({
    tone: "professional",
    length: "medium",
  });
  const [error, setError] = useState<string>("");

  // Track AI feature usage with PostHog when modal opens
  useEffect(() => {
    if (isOpen && canGenerate && !flagLoading) {
      trackUsage();
    }
  }, [isOpen, canGenerate, flagLoading, trackUsage]);

  const handleGenerate = useCallback(async () => {
    // Track PostHog feature usage for generation attempts
    if (!flagLoading) {
      trackUsage();
    }

    // Check if user can generate before proceeding
    if (!canGenerate) return;

    setIsGenerating(true);
    setError("");
    setGeneratedDescriptions([]);
    setSelectedDescription("");

    try {
      // Generate multiple variations (3 for premium, 1 for free trial)
      const variationCount = isPremium ? 3 : 1;
      const promises = [];

      for (let i = 0; i < variationCount; i++) {
        promises.push(
          aiService.generateDescription(
            contextType,
            resumeContext,
            { ...itemContext, currentDescription },
            { ...options, language },
          ),
        );
      }

      const variations = await Promise.all(promises);

      const successfulDescriptions = variations
        .filter((result) => result.success && result.description)
        .map((result) => result.description!);

      if (successfulDescriptions.length === 0) {
        setError("Failed to generate description. Please try again.");
        return;
      }

      setGeneratedDescriptions(successfulDescriptions);
      setSelectedDescription(successfulDescriptions[0]);
    } catch (err) {
      console.error("AI generation error:", err);
      setError("An error occurred while generating description.");
    } finally {
      setIsGenerating(false);
    }
  }, [
    contextType,
    resumeContext,
    itemContext,
    currentDescription,
    options,
    language,
    isPremium,
    flagLoading,
    trackUsage,
    canGenerate,
  ]);

  const handleAccept = useCallback(() => {
    if (selectedDescription) {
      onAccept(selectedDescription);
      onClose();
    }
  }, [selectedDescription, onAccept, onClose]);

  const handleClose = useCallback(() => {
    setGeneratedDescriptions([]);
    setSelectedDescription("");
    setError("");
    onClose();
  }, [onClose]);

  const toneOptions = [
    { key: "professional", label: "Professional" },
    { key: "casual", label: "Casual" },
    { key: "technical", label: "Technical" },
  ];

  const lengthOptions = [
    { key: "short", label: "Short" },
    { key: "medium", label: "Medium" },
    { key: "detailed", label: "Detailed" },
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="3xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
        footer: "flex-shrink-0",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="mdi:magic-staff" className="w-5 h-5 text-primary" />
          <span>Write with AI</span>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-6">
            {/* Options Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Tone"
                selectedKeys={[options.tone]}
                onSelectionChange={(keys) => {
                  const tone = Array.from(keys)[0] as AIOptions["tone"];
                  setOptions((prev) => ({ ...prev, tone }));
                }}
                variant="bordered"
              >
                {toneOptions.map((option) => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>

              <Select
                label="Length"
                selectedKeys={[options.length]}
                onSelectionChange={(keys) => {
                  const length = Array.from(keys)[0] as AIOptions["length"];
                  setOptions((prev) => ({ ...prev, length }));
                }}
                variant="bordered"
              >
                {lengthOptions.map((option) => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            {/* Generate Button */}
            <div className="flex flex-col items-center gap-2">
              <Button
                color="primary"
                variant="solid"
                startContent={isGenerating ? <Spinner size="sm" color="white" /> : <Icon icon="mdi:magic-staff" />}
                isDisabled={isGenerating || !canGenerate || flagLoading}
                onPress={handleGenerate}
                size="lg"
              >
                {flagLoading
                  ? "Loading..."
                  : isGenerating
                    ? "Generating..."
                    : !canGenerate
                      ? "AI Feature Disabled"
                      : "Generate Description"}
              </Button>
              {!canGenerate && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  AI feature is currently disabled. Please check your feature access.
                </p>
              )}
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-danger-700">
                  <Icon icon="mdi:alert-circle" />
                  <span className="font-medium">Error</span>
                </div>
                <p className="text-danger-600 mt-1">{error}</p>
              </div>
            )}

            {/* Generated Descriptions */}
            {generatedDescriptions.length > 0 && (
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">Generated Options</h4>
                <div className="space-y-3">
                  {generatedDescriptions.map((description, index) => (
                    <div
                      key={index}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedDescription === description
                          ? "border-primary bg-primary-50 dark:bg-primary-900/20"
                          : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                      }`}
                      onClick={() => setSelectedDescription(description)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-1">
                          <div
                            className={`w-4 h-4 rounded-full border-2 ${
                              selectedDescription === description
                                ? "border-primary bg-primary"
                                : "border-gray-300 dark:border-gray-600"
                            }`}
                          >
                            {selectedDescription === description && (
                              <div className="w-full h-full rounded-full bg-white scale-50"></div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div
                            className="prose prose-sm dark:prose-invert max-w-none"
                            dangerouslySetInnerHTML={{ __html: description }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Current Description (if exists) */}
            {currentDescription && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Current Description</h4>
                <div
                  className="prose prose-sm dark:prose-invert max-w-none p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  dangerouslySetInnerHTML={{ __html: currentDescription }}
                />
              </div>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="light" onPress={handleClose}>
            Cancel
          </Button>
          <Button
            color="primary"
            onPress={handleAccept}
            isDisabled={!selectedDescription}
            startContent={<Icon icon="mdi:check" />}
          >
            Use Description
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
