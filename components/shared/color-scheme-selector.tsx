"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";
import { COLOR_SCHEMES, ColorSchemeId } from "@/config/color-schemes";

interface ColorSchemeSelectorProps {
  selectedColorScheme: ColorSchemeId;
  onColorSchemeChange: (colorScheme: ColorSchemeId) => void;
  className?: string;
}

const ColorSchemeSelector: React.FC<ColorSchemeSelectorProps> = ({
  selectedColorScheme,
  onColorSchemeChange,
  className = "",
}) => {
  const renderButtonLayout = () => (
    <div className={`grid grid-cols-3 gap-3`}>
      {Object.entries(COLOR_SCHEMES).map(([key, scheme]) => (
        <Button
          key={key}
          className={`rounded-lg border-2 transition-all hover:shadow-sm ${
            selectedColorScheme === key
              ? "border-primary bg-primary/10"
              : "border-gray-200 dark:border-gray-600 hover:border-gray-300"
          }`}
          size="sm"
          onClick={() => onColorSchemeChange(key as ColorSchemeId)}
        >
          <div className="flex items-center gap-2">
            <div
              className={`w-4 h-4 rounded-full border border-gray-300`}
              style={{ backgroundColor: scheme.primary }}
            />
            {selectedColorScheme === key && <Icon className="text-primary w-3 h-3" icon="lucide:check" />}
          </div>
        </Button>
      ))}
    </div>
  );

  const content = <div className={className}>{renderButtonLayout()}</div>;

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Icon className="text-purple-600 dark:text-purple-400" icon="lucide:palette" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">Colors</h3>
        </div>
      </CardHeader>
      <CardBody className="pt-0">{content}</CardBody>
    </Card>
  );
};

export default ColorSchemeSelector;
