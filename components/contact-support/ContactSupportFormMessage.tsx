import { Icon } from "@iconify/react";

interface ContactSupportFormMessageProps {
  message: { type: string; content: string };
  showSuccessSteps?: boolean;
}

export function ContactSupportFormMessage({ message, showSuccessSteps = false }: ContactSupportFormMessageProps) {
  if (!message.content) return null;

  return (
    <div
      className={`p-4 rounded-lg border animate-fade-in ${
        message.type === "success"
          ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200"
          : message.type === "warning"
            ? "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 text-orange-800 dark:text-orange-200"
            : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200"
      }`}
    >
      <div className="flex items-start gap-3">
        <Icon
          icon={
            message.type === "success"
              ? "tabler:check-circle"
              : message.type === "warning"
                ? "tabler:alert-triangle"
                : "tabler:alert-circle"
          }
          className="w-5 h-5 mt-0.5 flex-shrink-0"
        />
        <div className="flex-1">
          <p className={message.type === "success" ? "font-medium" : ""}>{message.content}</p>
          {message.type === "success" && showSuccessSteps && (
            <p className="text-sm mt-1 opacity-80">We'll get back to you within 24 hours.</p>
          )}
        </div>
      </div>
    </div>
  );
}
