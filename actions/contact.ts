"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db";
import { contactSubmissions } from "@/db/schema";
import {
  getUserFromAuth,
  processContactSupportAttachments,
  validateAndExtractContactSupportFormData,
} from "./contact-support";

export async function submitContactForm(formData: FormData) {
  try {
    // Use shared validation
    const validation = await validateAndExtractContactSupportFormData(formData, "contact");

    if (!validation.success) {
      return validation;
    }

    const { name, email, subject, message, category, priority } = validation.data!;

    // Get authenticated user info
    const { userIdValue } = await getUserFromAuth();

    // Handle file attachments using shared logic
    const attachmentResult = await processContactSupportAttachments(formData);
    if (!attachmentResult.success) {
      return attachmentResult;
    }

    // Insert contact submission
    await db.insert(contactSubmissions).values({
      name,
      email,
      subject,
      message,
      category,
      priority,
      userId: userIdValue,
      attachments:
        attachmentResult.attachmentUrls && attachmentResult.attachmentUrls.length > 0
          ? JSON.stringify(attachmentResult.attachmentUrls)
          : null,
    });

    revalidatePath("/contact");
    return { success: true };
  } catch (error) {
    console.error("Contact form submission error:", error);
    return { success: false, error: "Failed to submit contact form. Please try again." };
  }
}
