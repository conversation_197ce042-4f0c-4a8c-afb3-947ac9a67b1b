"use client";

// Import statement for the hook - must be at the top
import { type FeatureFlagKey, useFeatureFlagEnabled } from "./use-posthog-feature-flags";

/**
 * Simplified feature flags hook using PostHog-only
 * This provides a clean, simple interface for feature flag checking
 */
export {
  type FeatureFlagKey,
  useAllFeatureFlags,
  useFeatureExperiment,
  useFeatureFlagEnabled,
  useFeatureFlagPayload,
  useFeatureFlagVariantKey,
  useFeatureRollout,
} from "./use-posthog-feature-flags";

/**
 * Simple wrapper for common feature flag use cases
 * Returns basic access information based on PostHog flags
 */
export function useFeatureFlag(flagKey: FeatureFlagKey, defaultValue: boolean = false) {
  const { isEnabled, isLoading, trackUsage } = useFeatureFlagEnabled(flagKey, defaultValue);

  return {
    isEnabled,
    isLoading,
    canAccess: isEnabled,
    requiresUpgrade: !isEnabled,
    trackUsage,
  };
}

// Re-export the main hook for backward compatibility
export { useFeatureFlagEnabled as useFeatureFlags };
