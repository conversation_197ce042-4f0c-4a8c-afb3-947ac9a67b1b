"use client";

import { Button, ButtonProps } from "@heroui/react";
import { useLoadingStore } from "@/lib/loading-store";

interface LoadingButtonProps extends ButtonProps {
  loadingId?: string;
  loadingText?: string;
  globalLoading?: boolean;
}

export function LoadingButton({
  loadingId,
  loadingText,
  globalLoading = false,
  children,
  isLoading: externalLoading,
  ...props
}: LoadingButtonProps) {
  const { isLoading: isGlobalLoading, isGlobalLoading: hasAnyLoading } = useLoadingStore();

  const isLoading =
    externalLoading || (loadingId ? isGlobalLoading(loadingId) : false) || (globalLoading ? hasAnyLoading : false);

  return (
    <Button {...props} isLoading={isLoading} disabled={isLoading || props.disabled}>
      {isLoading && loadingText ? loadingText : children}
    </Button>
  );
}
