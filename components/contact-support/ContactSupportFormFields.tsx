import { Chip } from "@heroui/chip";
import { Input, Textarea } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { Icon } from "@iconify/react";
import { FormCategory, FormPriority } from "@/config/contact-support";

interface BaseFieldProps {
  name: string;
  label: string;
  placeholder: string;
  isRequired?: boolean;
  icon?: string;
  description?: string;
  onChange?: () => void;
  defaultValue?: string;
}

interface InputFieldProps extends BaseFieldProps {
  type?: "text" | "email";
}

interface TextareaFieldProps extends BaseFieldProps {
  minRows?: number;
}

interface SelectFieldProps {
  name: string;
  label: string;
  placeholder: string;
  icon?: string;
  description?: string;
  selectedKeys: string[];
  onSelectionChange: (keys: any) => void;
  options: FormCategory[] | FormPriority[];
  showDescription?: boolean;
}

export function ContactSupportInputField({
  name,
  label,
  placeholder,
  type = "text",
  isRequired = false,
  icon,
  description,
  onChange,
  defaultValue,
}: InputFieldProps) {
  return (
    <Input
      name={name}
      type={type}
      label={label}
      placeholder={placeholder}
      variant="bordered"
      isRequired={isRequired}
      defaultValue={defaultValue}
      startContent={icon && <Icon icon={icon} className="w-4 h-4 text-default-400" />}
      description={description}
      onChange={onChange}
    />
  );
}

export function ContactSupportTextareaField({
  name,
  label,
  placeholder,
  isRequired = false,
  icon,
  description,
  onChange,
  minRows = 6,
}: TextareaFieldProps) {
  return (
    <Textarea
      name={name}
      label={label}
      placeholder={placeholder}
      variant="bordered"
      minRows={minRows}
      isRequired={isRequired}
      startContent={icon && <Icon icon={icon} className="w-4 h-4 text-default-400" />}
      description={description}
      onChange={onChange}
    />
  );
}

export function ContactSupportSelectField({
  name,
  label,
  placeholder,
  icon,
  description,
  selectedKeys,
  onSelectionChange,
  options,
  showDescription = false,
}: SelectFieldProps) {
  const isPrioritySelect = name === "priority";

  return (
    <Select
      name={name}
      label={label}
      placeholder={placeholder}
      variant="bordered"
      startContent={icon && <Icon icon={icon} className="w-4 h-4 text-default-400" />}
      selectedKeys={selectedKeys}
      onSelectionChange={onSelectionChange}
      description={description}
    >
      {options.map((option) => (
        <SelectItem
          key={option.key}
          startContent={!isPrioritySelect && "icon" in option && <Icon icon={option.icon} className="w-4 h-4" />}
          className={isPrioritySelect ? `text-${(option as FormPriority).color}` : ""}
          textValue={option.label}
        >
          {isPrioritySelect && showDescription ? (
            <div className="flex items-center justify-between w-full">
              <span>{option.label}</span>
              <Chip size="sm" color={(option as FormPriority).color} variant="flat">
                {(option as FormPriority).description}
              </Chip>
            </div>
          ) : (
            option.label
          )}
        </SelectItem>
      ))}
    </Select>
  );
}

// Personal Information Section Component for Contact/Support Forms
export function ContactSupportPersonalInfoSection({
  defaultName = "",
  defaultEmail = "",
  onChange,
}: {
  defaultName?: string;
  defaultEmail?: string;
  onChange?: () => void;
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Icon icon="tabler:user-circle" className="w-5 h-5 text-blue-500" />
        Personal Information
      </h3>
      <div className="grid md:grid-cols-2 gap-4">
        <ContactSupportInputField
          name="name"
          label="Full Name"
          placeholder="Enter your full name"
          isRequired
          icon="tabler:user"
          onChange={onChange}
          defaultValue={defaultName}
        />
        <ContactSupportInputField
          name="email"
          type="email"
          label="Email Address"
          placeholder="Enter your email address"
          isRequired
          icon="tabler:mail"
          description="We'll use this to respond to your message"
          onChange={onChange}
          defaultValue={defaultEmail}
        />
      </div>
    </div>
  );
}

// Classification Section Component for Contact/Support Forms
export function ContactSupportClassificationSection({
  categories,
  priorities,
  selectedCategory,
  selectedPriority,
  onCategoryChange,
  onPriorityChange,
  showPriorityDescription = false,
}: {
  categories: FormCategory[];
  priorities: FormPriority[];
  selectedCategory: string;
  selectedPriority: string;
  onCategoryChange: (keys: any) => void;
  onPriorityChange: (keys: any) => void;
  showPriorityDescription?: boolean;
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Icon icon="tabler:category" className="w-5 h-5 text-purple-500" />
        Message Classification
      </h3>
      <div className="grid md:grid-cols-2 gap-4">
        <ContactSupportSelectField
          name="category"
          label="Category"
          placeholder="Select a category"
          icon="tabler:folder"
          description="Choose the category that best fits your inquiry"
          selectedKeys={selectedCategory ? [selectedCategory] : []}
          onSelectionChange={onCategoryChange}
          options={categories}
        />
        <ContactSupportSelectField
          name="priority"
          label="Priority"
          placeholder="Select priority level"
          icon="tabler:flag"
          description="How urgent is your request?"
          selectedKeys={selectedPriority ? [selectedPriority] : []}
          onSelectionChange={onPriorityChange}
          options={priorities}
          showDescription={showPriorityDescription}
        />
      </div>
    </div>
  );
}

// Message Details Section Component for Contact/Support Forms
export function ContactSupportMessageDetailsSection({ onChange }: { onChange?: () => void }) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Icon icon="tabler:message-dots" className="w-5 h-5 text-green-500" />
        Message Details
      </h3>
      <ContactSupportInputField
        name="subject"
        label="Subject"
        placeholder="Brief description of your inquiry"
        isRequired
        icon="tabler:tag"
        description="A concise summary of your message"
        onChange={onChange}
      />
      <ContactSupportTextareaField
        name="message"
        label="Message"
        placeholder="Please provide detailed information about your inquiry..."
        isRequired
        icon="tabler:message"
        description="Include as much detail as possible to help us assist you better"
        onChange={onChange}
        minRows={6}
      />
    </div>
  );
}
