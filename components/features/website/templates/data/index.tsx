import React from "react";

import { formatDateRange, formatLocation } from "@/components/features/resume/templates/shared/utils";
import { Skill } from "@/db/schema";
import { getRTLFlexClasses, useRTL } from "../shared/utils";

// Experience timeline component for websites
export interface WebsiteExperienceProps {
  experiences: any[];
  className?: string;
}

export const WebsiteExperience: React.FC<WebsiteExperienceProps> = ({ experiences, className = "" }) => {
  const isRTL = useRTL();
  const rtlClasses = getRTLFlexClasses(isRTL);

  if (!experiences || experiences.length === 0) return null;

  return (
    <div className={`website-experience ${className}`}>
      <div className="space-y-6">
        {experiences.map((experience) => {
          const location = formatLocation(experience.city || "", experience.country || "");
          const dateRange = formatDateRange(experience.startDate, experience.endDate, experience.isCurrent);

          return (
            <div key={experience.id} className="bg-white p-6 rounded-lg shadow-sm border">
              <div
                className={`flex flex-col md:${rtlClasses.flexRow} md:${rtlClasses.justifyStart} md:justify-between md:items-start mb-4`}
              >
                <div className={rtlClasses.textStart}>
                  <h3 className={`text-xl font-bold text-gray-900 mb-1 ${rtlClasses.textStart}`}>{experience.title}</h3>
                  <p className={`text-lg text-blue-600 font-semibold mb-1 ${rtlClasses.textStart}`}>
                    {experience.company}
                  </p>
                  {location && <p className={`text-gray-600 ${rtlClasses.textStart}`}>{location}</p>}
                </div>
                <div className={`text-gray-600 mt-2 md:mt-0 ${rtlClasses.textEnd}`}>
                  <p className="font-medium">{dateRange}</p>
                </div>
              </div>
              {experience.description && (
                <div
                  dangerouslySetInnerHTML={{ __html: experience.description }}
                  className={`text-gray-700 leading-relaxed ${rtlClasses.textStart}`}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Skills grid component for websites
export interface WebsiteSkillsProps {
  skills: Skill[];
  className?: string;
}

export const WebsiteSkills: React.FC<WebsiteSkillsProps> = ({ skills, className = "" }) => {
  if (!skills || skills.length === 0) return null;

  // Group skills by category
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      const category = skill.category || "";
      if (!acc[category]) acc[category] = [];
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, typeof skills>,
  );

  return (
    <div className={`website-skills ${className}`}>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
          <div key={category} className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-bold text-gray-900 mb-3">{category}</h3>
            <div className="flex flex-wrap gap-2">
              {categorySkills.map((skill) => (
                <span key={skill.id} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {skill.name}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Projects showcase component for websites
export interface WebsiteProjectsProps {
  projects: any[];
  className?: string;
}

export const WebsiteProjects: React.FC<WebsiteProjectsProps> = ({ projects, className = "" }) => {
  if (!projects || projects.length === 0) return null;

  return (
    <div className={`website-projects ${className}`}>
      <div className="grid md:grid-cols-2 gap-6">
        {projects.map((project) => {
          const dateRange =
            project.startDate && project.endDate
              ? formatDateRange(project.startDate, project.endDate, 0, "en-US")
              : null;

          return (
            <div key={project.id} className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900">{project.title}</h3>
                {dateRange && <span className="text-gray-600 text-sm">{dateRange}</span>}
              </div>
              {project.client && <p className="text-blue-600 font-semibold mb-2">{project.client}</p>}
              {project.description && (
                <div
                  dangerouslySetInnerHTML={{ __html: project.description }}
                  className="text-gray-700 mb-4 leading-relaxed"
                />
              )}
              {project.url && (
                <a
                  href={project.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  🔗 View Project
                </a>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Education component for websites
export interface WebsiteEducationProps {
  educations: any[];
  className?: string;
}

export const WebsiteEducation: React.FC<WebsiteEducationProps> = ({ educations, className = "" }) => {
  if (!educations || educations.length === 0) return null;

  return (
    <div className={`website-education ${className}`}>
      <div className="space-y-4">
        {educations.map((education) => {
          const location = formatLocation(education.city || "", education.country || "");
          const dateRange = formatDateRange(education.startDate, education.endDate, education.isCurrent);
          const degreeText = education.field_of_study
            ? `${education.degree} in ${education.field_of_study}`
            : education.degree;

          return (
            <div key={education.id} className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-1">{education.institution}</h3>
                  <p className="text-blue-600 font-semibold mb-1">{degreeText}</p>
                  {location && <p className="text-gray-600">{location}</p>}
                </div>
                <div className="text-gray-600 mt-2 md:mt-0">
                  <p className="font-medium">{dateRange}</p>
                </div>
              </div>
              {education.description && (
                <div
                  dangerouslySetInnerHTML={{ __html: education.description }}
                  className="text-gray-700 mt-3 leading-relaxed"
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
