import { and, eq } from "drizzle-orm";
import { WebsiteBuilderPage } from "@/components/features/website/website-builder-client";
import { db } from "@/db";
import { resumes, websites, websiteTemplates } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

interface PageProps {
  params: Promise<{
    locale: string;
    websiteId: string;
  }>;
}

export default async function WebsiteBuilderEditPage({ params }: PageProps) {
  const { websiteId: websiteIdParam } = await params;
  const user = await requireAuth();
  const websiteId = parseInt(websiteIdParam);

  const [website] = await db
    .select()
    .from(websites)
    .where(and(eq(websites.id, websiteId), eq(websites.userId, user.clerkId)))
    .limit(1);

  return <WebsiteBuilderPage website={website} />;
}
