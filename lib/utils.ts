import { type ClassValue, clsx } from "clsx";

// Re-export utilities from shared modules
export * from "./shared";

export const createEmptyResume = (title: string) => {
  return {
    title: title || "",
    firstName: "",
    lastName: "",
    jobTitle: "",
    address: "",
    email: "",
    website: "",
    bio: "",
    birthDate: "",
    city: "",
    street: "",
    country: "",
    showPhoto: 0,
    photo: "",
  };
};

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}
