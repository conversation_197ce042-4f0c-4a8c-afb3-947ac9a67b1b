# QuickCV Logo System

## Overview

The QuickCV logo system provides multiple variants designed for different use cases while maintaining brand consistency. The logo combines document and lightning elements to represent both the resume-building purpose and the "quick" nature of the application.

## Design Elements

### Core Concepts
- **Document**: Represents resumes and CVs with clean lines and professional appearance
- **Lightning Bolt**: Symbolizes speed and efficiency ("Quick" in QuickCV)
- **Modern Typography**: Clean, professional font with gradient treatment
- **Responsive Design**: Scalable for all screen sizes and use cases

### Visual Hierarchy
1. **Primary Element**: Document outline with content lines
2. **Secondary Element**: Lightning bolt for speed indication
3. **Supporting Elements**: Corner fold and subtle opacity variations

## Logo Variants

### 1. Full Logo (`QuickCVLogo`)
**Use Cases**: Main application headers, desktop sidebar, large displays
- Complete document with content lines
- Prominent lightning bolt
- Corner fold detail
- Optimal size: 32px+

```tsx
import { QuickCVLogo } from "@/components/logo";

<QuickCVLogo size={40} className="text-blue-600" />
```

### 2. Mini Logo (`QuickCVLogoMini`)
**Use Cases**: Mobile headers, small UI elements, compact layouts
- Simplified document outline
- Reduced detail while maintaining recognition
- Optimal size: 16px-24px

```tsx
import { QuickCVLogoMini } from "@/components/logo";

<QuickCVLogoMini size={20} className="text-blue-600" />
```

### 3. Icon Only (`QuickCVIcon`)
**Use Cases**: Favicons, app icons, minimal layouts
- Lightning bolt only
- Maximum impact in minimal space
- Optimal size: 16px-32px

```tsx
import { QuickCVIcon } from "@/components/logo";

<QuickCVIcon size={24} className="text-blue-600" />
```

### 4. Brand Logo (`QuickCVBrandLogo`)
**Use Cases**: Marketing materials, landing pages, promotional content
- Logo with "QuickCV" text
- Gradient text treatment
- Configurable text display

```tsx
import { QuickCVBrandLogo } from "@/components/logo";

<QuickCVBrandLogo 
  size={40} 
  showText={true}
  textClassName="text-2xl"
/>
```

### 5. Unified Variants (`LogoVariants`)
**Use Cases**: Component that needs to switch between variants
- Single component with variant prop
- Consistent API across all variants

```tsx
import LogoVariants from "@/components/shared/logo-variants";

<LogoVariants variant="full" size={32} />
<LogoVariants variant="mini" size={20} />
<LogoVariants variant="icon" size={16} />
<LogoVariants variant="brand" showText={true} />
```

## Color Guidelines

### Primary Colors (from color-schemes.ts)
- **Blue**: `#3B82F6` (Professional Blue)
- **Purple**: `#8B5CF6` (Creative Purple)
- **Green**: `#10B981` (Nature Green)

### Usage Examples
```tsx
// Blue theme
<QuickCVLogo className="text-blue-500" />

// Purple theme
<QuickCVLogo className="text-purple-500" />

// Gradient (for brand usage)
<QuickCVLogo className="text-transparent bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text" />
```

## Dark Mode Support

All logo variants work automatically with dark mode through Tailwind's `currentColor` system:

```tsx
// Automatically adapts to light/dark mode
<QuickCVLogo className="text-gray-900 dark:text-white" />

// With opacity for subtle effects
<QuickCVLogo className="text-gray-700 dark:text-gray-300" />
```

## Responsive Usage

### Desktop Sidebar
```tsx
{!isCollapsed && (
  <Link href="/" className="flex items-center gap-2 group">
    <div className="relative">
      <QuickCVLogo className="w-8 h-8 transition-transform group-hover:scale-110" />
      <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30 group-hover:opacity-50 transition-opacity" />
    </div>
    <p className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
      QuickCV
    </p>
  </Link>
)}
```

### Mobile Header
```tsx
<Link href="/" className="flex items-center gap-2">
  <div className="relative">
    <QuickCVLogoMini className="w-6 h-6" />
    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-30" />
  </div>
  <p className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
    QuickCV
  </p>
</Link>
```

## Animation Guidelines

### Hover Effects
- **Scale**: `hover:scale-110` for interactive elements
- **Glow**: Gradient blur background for premium feel
- **Transition**: `transition-all duration-200` for smooth animations

### Loading States
```tsx
<QuickCVLogo className="animate-pulse opacity-50" />
```

## Accessibility

### Alt Text Guidelines
- Logo: "QuickCV logo"
- Icon: "QuickCV"
- Brand: "QuickCV - Professional Resume Builder"

### ARIA Labels
```tsx
<QuickCVLogo 
  role="img" 
  aria-label="QuickCV logo"
  className="w-8 h-8"
/>
```

## File Export Guidelines

### Favicon Sizes
- 16x16px: Use `QuickCVIcon`
- 32x32px: Use `QuickCVLogoMini`
- 48x48px: Use `QuickCVLogo`

### App Icons
- iOS: Use `QuickCVLogo` with proper padding
- Android: Use `QuickCVIcon` for adaptive icons

## Best Practices

### Do's
✅ Use appropriate variant for context
✅ Maintain consistent sizing within components
✅ Apply hover effects for interactive elements
✅ Use gradient backgrounds for premium feel
✅ Test in both light and dark modes

### Don'ts
❌ Don't use full logo in spaces smaller than 24px
❌ Don't modify the SVG paths manually
❌ Don't use raster versions when vector is available
❌ Don't apply conflicting colors to gradients
❌ Don't stretch or distort proportions

## Integration with Existing Code

The new logo system is backward compatible. The existing `Logo` component in `components/shared/common/icons.tsx` has been updated to use the new design while maintaining the same API.

For new implementations, prefer using the specific variant components or the unified `LogoVariants` component for better type safety and flexibility.