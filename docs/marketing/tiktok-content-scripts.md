# TikTok Content Scripts - QuickCV

## Content Pillars & Series

### Pillar 1: Job Search Reality Check
**Series: "Things Nobody Tells You About Job Hunting"**

#### Script 1: ATS Horror Story (Problem Agitation)
```
HOOK (0-3 seconds):
"POV: You've applied to 200 jobs and heard nothing back"

SETUP (3-8 seconds):
Shows person at computer, frustrated, with rejection emails
"You think it's your qualifications, but plot twist..."

REVELATION (8-15 seconds):
"77% of resumes never reach human eyes. They're killed by ATS software that can't read your beautiful Canva template."
*Shows ATS interface rejecting resumes*

SOLUTION TEASE (15-20 seconds):
"Here's how to beat the robots that are ghosting you"
*Quick preview of ATS-optimized resume*

CTA (20-30 seconds):
"Comment 'ATS' for the resume hack that got me hired"

Trending Sound: "Oh No" or "Somehow I manage"
Text Overlay: "When you realize why you're not getting interviews"
```

#### Script 2: Entry-Level Job Requirements (Relatable Pain)
```
HOOK (0-3 seconds):
"Entry-level job requirements be like..."

BUILDUP (3-15 seconds):
*Text overlay showing ridiculous requirements*
- "Entry Level Position"
- "5+ years experience required"
- "Expert in 47 different software programs"
- "Must have built a unicorn startup in college"
- "Salary: Exposure and pizza parties"

TWIST (15-25 seconds):
"Meanwhile, the people getting hired know this one secret..."
*Shows someone easily applying with QuickCV*

PAYOFF (25-30 seconds):
"It's not about meeting every requirement. It's about ATS optimization."

Trending Sound: "Material Girl" (sped up)
Text Overlay: "Gen Z job hunting in 2025"
```

### Pillar 2: Behind-the-Scenes Resume Building
**Series: "Resume Glow-Up"**

#### Script 3: Before/After Resume Transformation
```
HOOK (0-3 seconds):
"Turning my cringe resume into interview gold"

BEFORE (3-8 seconds):
*Shows old resume with classic mistakes*
- Comic Sans font
- Paragraph descriptions
- "References available upon request"
- Objective statement from 2010

PROCESS (8-20 seconds):
*Quick cuts of using QuickCV*
- Selecting ATS-optimized template
- AI enhancing bullet points
- Real-time ATS score improving
- Keywords being optimized

AFTER (20-28 seconds):
*Shows polished, professional resume*
- Clean design
- Quantified achievements
- ATS score: 98%
- Multiple format exports

RESULT (28-30 seconds):
"Update: Got 4 interviews this week"

Trending Sound: "Glow Up" or transformation trend audio
Text Overlay: "ATS score went from 12% to 98%"
```

#### Script 4: AI Enhancement in Action
```
HOOK (0-3 seconds):
"Turning 'I worked at Starbucks' into something hire-worthy"

ORIGINAL (3-8 seconds):
*Types boring description*
"Made coffee and cleaned tables"

AI MAGIC (8-20 seconds):
*Shows AI enhancement in real-time*
"Delivered exceptional customer service in high-volume environment, managing multiple orders simultaneously while maintaining quality standards"

REVEAL (20-25 seconds):
"Same job, better words = better interviews"

SOCIAL PROOF (25-30 seconds):
"Sarah used this and got hired at Google"

Trending Sound: "Upgrade" or "Level Up" audio
Text Overlay: "POV: AI makes your experience sound impressive"
```

### Pillar 3: Education & Tips
**Series: "Resume Hacks That Actually Work"**

#### Script 5: ATS Keywords Explanation
```
HOOK (0-3 seconds):
"Why your resume keeps getting rejected"

PROBLEM (3-10 seconds):
*Shows person typing resume with wrong keywords*
"You're using human words. ATS speaks robot."
*Shows ATS scanning and rejecting*

EDUCATION (10-22 seconds):
*Split screen: job posting vs resume*
"Job posting says 'Project Management'"
"Your resume says 'Led team projects'"
"ATS doesn't make connections. You have to be exact."

SOLUTION (22-28 seconds):
*Shows QuickCV keyword optimization*
"Our AI finds the exact keywords from job postings"

PROOF (28-30 seconds):
"Marcus went from 0 to 4 interviews in one week"

Trending Sound: "Now I know" revelation audio
Text Overlay: "ATS keywords = job interviews"
```

#### Script 6: Common Resume Mistakes
```
HOOK (0-3 seconds):
"Resume red flags that instantly disqualify you"

MISTAKE 1 (3-8 seconds):
*Shows resume with photo*
"Including your photo (unless you're an actor)"
*Red X animation*

MISTAKE 2 (8-13 seconds):
*Shows creative template*
"Using templates with graphics ATS can't read"
*Error message overlay*

MISTAKE 3 (13-18 seconds):
*Shows paragraph descriptions*
"Writing essays instead of bullet points"
*Yawn emoji*

MISTAKE 4 (18-23 seconds):
*Shows old email address*
"Email address from middle school"
*Cringe face*

SOLUTION (23-28 seconds):
"Or just use QuickCV and avoid all these mistakes"

CTA (28-30 seconds):
"Link in bio to fix your resume"

Trending Sound: "Red flags" audio or "Mistakes were made"
Text Overlay: "HR managers hate these resume mistakes"
```

### Pillar 4: Success Stories & Social Proof
**Series: "QuickCV Success Stories"**

#### Script 7: User Testimonial Recreation
```
HOOK (0-3 seconds):
"How Zoe went from unemployed to UX designer in 30 days"

BACKSTORY (3-10 seconds):
*Person looking stressed at computer*
"6 months unemployed, 500+ applications, 0 interviews"
*Shows rejection emails*

DISCOVERY (10-18 seconds):
*Light bulb moment*
"Found out about ATS optimization"
*Shows building resume on QuickCV*

TRANSFORMATION (18-25 seconds):
*Shows interview notifications*
"New resume = 3 interviews in first week"
*Happy person celebrating*

RESULT (25-30 seconds):
"Now she's a UX designer at a startup"
*Shows LinkedIn update*

Trending Sound: "Success story" or "Glow up" audio
Text Overlay: "Real user, real results"
```

#### Script 8: Stats That Shock
```
HOOK (0-3 seconds):
"Gen Z job hunting stats that will blow your mind"

STAT 1 (3-8 seconds):
*Dramatic zoom on text*
"25% of Gen Z is unemployed"
*Shocked emoji*

STAT 2 (8-13 seconds):
"77% of resumes get auto-rejected by robots"
*Robot rejecting papers*

STAT 3 (13-18 seconds):
"Average job posting gets 250+ applications"
*Stack of papers growing*

STAT 4 (18-23 seconds):
"But QuickCV users get 3x more interviews"
*Graph going up*

SOLUTION (23-28 seconds):
"The difference? ATS optimization"

CTA (28-30 seconds):
"Don't be a statistic. Get QuickCV."

Trending Sound: "Mind blown" or dramatic reveal audio
Text Overlay: "Job hunting in 2025 is brutal"
```

### Pillar 5: Trending Format Adaptations
**Series: "Resume Builder POV"**

#### Script 9: "Get Ready With Me" - Resume Edition
```
HOOK (0-3 seconds):
"GRWM to apply for my dream job"

STEP 1 (3-8 seconds):
*Morning routine vibes*
"First, coffee because job hunting is exhausting"

STEP 2 (8-15 seconds):
"Opening QuickCV to build my interview-winning resume"
*Shows clean interface*

STEP 3 (15-22 seconds):
"Choosing a template that ATS can actually read"
*Template selection*

STEP 4 (22-27 seconds):
"AI enhancing my bullet points because I'm not a copywriter"
*AI enhancement in action*

FINAL LOOK (27-30 seconds):
"Ready to get hired"
*Shows final resume + confidence*

Trending Sound: GRWM trending audio
Text Overlay: "POV: You're about to get your dream job"
```

#### Script 10: Duet Bait - Resume Review
```
ORIGINAL SETUP (0-3 seconds):
"Rating your resume based on these ATS red flags"

CRITERIA (3-15 seconds):
*Shows checklist*
- "Can ATS read the font?"
- "Are keywords optimized?"
- "Is contact info scannable?"
- "Are bullet points quantified?"

INVITATION (15-25 seconds):
"Duet this with your resume and I'll rate it"

HOOK FOR DUETS (25-30 seconds):
"Bonus points if you guess your ATS score"

Text Overlay: "ATS compatibility test"
Comment Strategy: Engage with every duet submission
```

## Content Calendar Framework

### Week 1: Problem Awareness
- Monday: ATS Horror Story (#1)
- Wednesday: Entry-Level Requirements (#2)
- Friday: Common Mistakes (#6)
- Sunday: Shocking Stats (#8)

### Week 2: Solution Introduction
- Monday: Resume Transformation (#3)
- Wednesday: AI Enhancement (#4)
- Friday: ATS Keywords Education (#5)
- Sunday: Success Story (#7)

### Week 3: Social Proof & Engagement
- Monday: User Testimonial (#7)
- Wednesday: GRWM Resume Edition (#9)
- Friday: Duet Bait Review (#10)
- Sunday: Trending Format Adaptation

### Week 4: Conversion Focus
- Monday: Success Story
- Wednesday: Limited Time Offer
- Friday: Comparison Content
- Sunday: Behind-the-Scenes

## Advanced Scripts

### Script 11: Day in the Life - Successful Job Hunter
```
HOOK (0-3 seconds):
"Day in my life: Actually getting job interviews"

MORNING (3-8 seconds):
*Waking up to interview request notifications*
"Woke up to 3 interview requests"

PROCESS (8-20 seconds):
*Shows organized job hunting setup*
- QuickCV dashboard open
- Multiple interview confirmations
- Calendar filling up with meetings

CONTRAST (20-25 seconds):
"POV: Your friend with the Canva resume"
*Shows person still waiting for responses*

OUTRO (25-30 seconds):
"The difference? I optimized for ATS, not aesthetics"

Trending Sound: "That girl" or productivity audio
Text Overlay: "POV: You cracked the job hunting code"
```

### Script 12: Reaction to Bad Resume Advice
```
SETUP (0-3 seconds):
*Reacting to bad resume advice video*
"Not them telling you to use Comic Sans"

REACTION 1 (3-8 seconds):
*Watching advice to include photo*
"Chile... that's not it"

REACTION 2 (8-15 seconds):
*Advice about creative templates*
"The ATS literally cannot read that"

CORRECTION (15-25 seconds):
"Here's what actually gets you hired"
*Shows ATS-optimized resume*

PROOF (25-30 seconds):
"This approach got me hired in 2 weeks"

Trending Sound: "Not the..." or reaction audio
Text Overlay: "Bad resume advice vs reality"
```

### Script 13: Storytime - Job Search Journey
```
HOOK (0-3 seconds):
"Storytime: How I went from 200 rejections to multiple offers"

STRUGGLE (3-10 seconds):
"Was unemployed for 8 months, applied everywhere"
*Shows rejection montage*

REVELATION (10-18 seconds):
"Then I learned about ATS optimization"
*Light bulb moment*

TRANSFORMATION (18-25 seconds):
"Rebuilt my resume, same experience, better format"
*Shows before/after*

RESULT (25-30 seconds):
"Got 3 offers in 30 days. ATS optimization is real."

Trending Sound: Storytime or inspirational audio
Text Overlay: "Your resume format matters more than your experience"
```

## Viral Mechanics Integration

### Duet Opportunities
1. **Resume Reviews**: Users submit resumes for rating
2. **Success Celebrations**: Users share job offers
3. **Before/After**: Users show resume transformations
4. **Mistake Corrections**: React to bad resume advice

### Stitch Setups
1. **"Here's what actually works..."** (Leave open for user additions)
2. **"Rate my ATS optimization..."** (Users show their improvements)
3. **"Things HR managers never tell you..."** (Educational series continuation)

### Challenge Creation
1. **#ATSOptimizedChallenge**: Users show resume improvements
2. **#JobHuntingReality**: Share honest job search experiences
3. **#ResumeGlowUp**: Before/after transformations
4. **#FirstJobStory**: Success story sharing

### Comment Bait Strategies
1. **"Comment your industry for custom tips"**
2. **"Drop your biggest resume mistake below"**
3. **"Tell me your job hunting horror story"**
4. **"What's your ATS score? Guess in comments"**

## Performance Optimization

### Hook Formulas
1. **Problem + Stat**: "77% of resumes get rejected because..."
2. **POV + Revelation**: "POV: You just learned why you're not getting hired"
3. **Question + Shock**: "Want to know why you're unemployed? It's not your qualifications"
4. **Story Setup**: "How I went from 200 rejections to 3 offers"

### Retention Strategies
- **3-Second Hook**: Problem statement or shocking stat
- **8-Second Payoff**: Quick win or revelation
- **15-Second Education**: Tactical advice or explanation
- **25-Second Social Proof**: Success story or testimonial
- **30-Second CTA**: Clear next step

### Algorithm Signals
- **Comments**: Ask specific questions, respond to all
- **Shares**: Create shareable moments and quotes
- **Saves**: Include actionable tips worth saving
- **Completion**: Strong payoff keeps viewers watching
- **Engagement Rate**: Aim for >15% engagement rate

## Content Repurposing Strategy

### Cross-Platform Adaptation
- **Instagram Reels**: Same script, square format
- **YouTube Shorts**: Extended educational versions
- **LinkedIn**: Professional-focused variations
- **Twitter**: Thread versions of tips

### Long-Form Extensions
- **YouTube**: 3-5 minute deep dives
- **Podcast**: Interview with successful users
- **Blog**: Written versions with more detail
- **Email**: Newsletter series with scripts

### User-Generated Content Amplification
- **Repost**: User success stories with permission
- **Collaborate**: Co-create with successful users
- **Feature**: Highlight user transformations
- **Testimonials**: Turn comments into content

## Success Metrics

### Engagement Metrics
- **View-through rate**: >60%
- **Engagement rate**: >15%
- **Share rate**: >3%
- **Comment rate**: >5%
- **Save rate**: >8%

### Conversion Metrics
- **Profile visits**: Track from TikTok analytics
- **Link clicks**: Bio link performance
- **Website traffic**: TikTok referral traffic
- **Sign-ups**: Attribute to TikTok campaigns
- **Purchases**: Track with UTM parameters

### Viral Indicators
- **Exponential growth**: Views doubling daily
- **Cross-platform spread**: Content appearing elsewhere
- **Organic mentions**: Users talking about QuickCV
- **Influencer pickup**: Other creators making similar content
- **Media coverage**: Press picking up viral content