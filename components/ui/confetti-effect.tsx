"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface ConfettiParticle {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
  rotation: number;
  velocityX: number;
  velocityY: number;
}

interface ConfettiEffectProps {
  duration?: number;
  particleCount?: number;
  onComplete?: () => void;
  colors?: string[];
  className?: string;
}

const DEFAULT_COLORS = [
  "#FFD700", // Gold
  "#FF6B6B", // Red
  "#4ECDC4", // Teal
  "#45B7D1", // Blue
  "#96CEB4", // Green
  "#FFEAA7", // Yellow
  "#DDA0DD", // Plum
  "#98D8C8", // Mint
];

export function ConfettiEffect({
  duration = 3000,
  particleCount = 50,
  onComplete,
  colors = DEFAULT_COLORS,
  className = "",
}: ConfettiEffectProps) {
  const [particles, setParticles] = useState<ConfettiParticle[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Generate confetti particles
    const newParticles: ConfettiParticle[] = [];
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: `confetti-${i}`,
        x: centerX + (Math.random() - 0.5) * 200,
        y: centerY + (Math.random() - 0.5) * 100,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 8 + 4,
        rotation: Math.random() * 360,
        velocityX: (Math.random() - 0.5) * 400,
        velocityY: -Math.random() * 300 - 100,
      });
    }

    setParticles(newParticles);

    // Auto cleanup
    const timer = setTimeout(() => {
      setIsVisible(false);
      onComplete?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [particleCount, colors, duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 pointer-events-none z-50 overflow-hidden ${className}`}>
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            initial={{
              x: particle.x,
              y: particle.y,
              opacity: 1,
              scale: 1,
              rotate: particle.rotation,
            }}
            animate={{
              x: particle.x + particle.velocityX,
              y: particle.y + particle.velocityY + 500, // Gravity effect
              opacity: [1, 1, 0],
              scale: [1, 0.8, 0.2],
              rotate: particle.rotation + 720, // Multiple rotations
            }}
            transition={{
              duration: duration / 1000,
              ease: [0.25, 0.46, 0.45, 0.94], // Custom easing
            }}
            className="absolute"
            style={{
              width: particle.size,
              height: particle.size,
              backgroundColor: particle.color,
              borderRadius: "2px",
            }}
          />
        ))}
      </AnimatePresence>

      {/* Additional sparkle effects */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 1, 0] }}
        transition={{ duration: 1, repeat: 3 }}
        className="absolute inset-0 bg-gradient-radial from-primary/5 to-transparent"
      />
    </div>
  );
}

export default ConfettiEffect;
