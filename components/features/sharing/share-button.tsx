"use client";

import { useDisclosure } from "@heroui/react";
import { Icon } from "@iconify/react";
import { FeatureButton } from "@/components/premium";
import { ShareModal } from "./share-modal";

interface ShareButtonProps {
  resumeId: number;
  variant?: "solid" | "bordered" | "light" | "flat" | "faded" | "shadow" | "ghost";
  size?: "sm" | "md" | "lg";
  isIconOnly?: boolean;
  className?: string;
}

export function ShareButton({
  resumeId,
  variant = "flat",
  size = "md",
  isIconOnly = false,
  className = "",
}: ShareButtonProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <>
      <FeatureButton
        featureId="social_sharing"
        onClick={onOpen}
        variant={variant}
        size={size}
        icon={!isIconOnly ? <Icon icon="tabler:share" className="w-4 h-4" /> : undefined}
        className={className}
      >
        {isIconOnly ? <Icon icon="tabler:share" className="w-4 h-4" /> : "Share"}
      </FeatureButton>

      <ShareModal isOpen={isOpen} onClose={onClose} resumeId={resumeId} />
    </>
  );
}
