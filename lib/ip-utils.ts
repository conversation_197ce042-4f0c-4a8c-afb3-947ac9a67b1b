import { headers } from "next/headers";

/**
 * Extract client IP address from request headers
 * Handles various proxy scenarios and forwarded headers
 */
export async function getClientIP(): Promise<string> {
  try {
    const headersList = await headers();

    // Check common forwarded IP headers in order of preference
    const forwardedFor = headersList.get("x-forwarded-for");
    if (forwardedFor) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ips = forwardedFor.split(",").map((ip) => ip.trim());
      const firstIP = ips[0];
      if (firstIP && isValidIP(firstIP)) {
        return firstIP;
      }
    }

    // Check other common headers
    const realIP = headersList.get("x-real-ip");
    if (realIP && isValidIP(realIP)) {
      return realIP;
    }

    const clientIP = headersList.get("x-client-ip");
    if (clientIP && isValidIP(clientIP)) {
      return clientIP;
    }

    const forwarded = headersList.get("forwarded");
    if (forwarded) {
      // Parse forwarded header format: for=**********;proto=http;by=************
      const forMatch = forwarded.match(/for=([^;,\s]+)/);
      if (forMatch && forMatch[1]) {
        const ip = forMatch[1].replace(/"/g, "");
        if (isValidIP(ip)) {
          return ip;
        }
      }
    }

    // Fallback to a default IP if none found
    return "127.0.0.1";
  } catch (error) {
    console.error("Error extracting client IP:", error);
    return "127.0.0.1";
  }
}

/**
 * Basic IP address validation
 */
function isValidIP(ip: string): boolean {
  // Remove any port number if present
  const cleanIP = ip.split(":")[0];

  // IPv4 validation
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  if (ipv4Regex.test(cleanIP)) {
    return true;
  }

  // IPv6 validation (basic)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  if (ipv6Regex.test(cleanIP)) {
    return true;
  }

  // IPv6 compressed format
  const ipv6CompressedRegex = /^::1$|^::$|^([0-9a-fA-F]{1,4}:){1,7}:$|^:([0-9a-fA-F]{1,4}:){1,6}[0-9a-fA-F]{1,4}$/;
  if (ipv6CompressedRegex.test(cleanIP)) {
    return true;
  }

  return false;
}

/**
 * Hash IP address for privacy-conscious rate limiting
 * This allows rate limiting while not storing actual IP addresses
 */
export function hashIP(ip: string): string {
  // Simple hash for rate limiting keys (not cryptographically secure)
  let hash = 0;
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}
