"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { Icon } from "@iconify/react";
import Link from "next/link";

export default function TeamSection() {
  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-3xl font-bold mb-8">Our Team</h2>
        <Card className="bg-gradient-to-br from-gray-50/50 to-blue-50/50 dark:from-gray-900/50 dark:to-blue-900/20 border-0">
          <CardBody className="p-10">
            <div className="mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon="tabler:users" className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed mb-6">
              We're a passionate team of developers, designers, and career experts dedicated to helping you succeed.
            </p>
            <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed mb-8">
              Our diverse backgrounds in technology, design, and human resources give us unique insights into what makes
              resumes effective in today's competitive job market.
            </p>
            <Link href="/contact">
              <Button variant="flat" color="primary" size="lg" className="font-semibold">
                <Icon icon="tabler:mail" className="w-5 h-5 mr-2" />
                Get in Touch
              </Button>
            </Link>
          </CardBody>
        </Card>
      </div>
    </section>
  );
}
