"use client";

import { useUser } from "@clerk/nextjs";
import { useCallback, useEffect, useRef } from "react";
import {
  identifyUser,
  setUserProperties,
  trackConversion,
  trackEvent,
  trackPageView as trackPageViewPostHog,
} from "@/lib/posthog-analytics";
import type { UseAnalyticsReturn } from "@/types/analytics";

/**
 * PostHog analytics hook for tracking
 */
export function useAnalytics(): UseAnalyticsReturn {
  const { user } = useUser();
  const sessionId = useRef(crypto.randomUUID());
  const pageLoadTime = useRef(Date.now());

  // Identify user in PostHog when user changes
  useEffect(() => {
    if (user) {
      identifyUser(user.id, {
        email: user.emailAddresses[0]?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: user.createdAt?.toISOString(),
        // Add user metadata for better segmentation
        clerk_id: user.id,
        has_image: !!user.imageUrl,
      });
    }
  }, [user]);

  // Track page views automatically
  useEffect(() => {
    if (typeof window !== "undefined") {
      trackPageView(window.location.pathname, {
        referrer: document.referrer,
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
        deviceType: getDeviceType(),
        loadTime: Date.now() - pageLoadTime.current,
      });
    }
  }, []);

  const trackEventWrapper = useCallback(
    async (eventData: { eventName: string; eventType: string; properties?: Record<string, any> }) => {
      try {
        trackEvent(eventData.eventName, {
          ...eventData.properties,
          eventType: eventData.eventType,
          sessionId: sessionId.current,
          userAgent: typeof window !== "undefined" ? navigator.userAgent : undefined,
          screenResolution: typeof window !== "undefined" ? `${screen.width}x${screen.height}` : undefined,
          deviceType: getDeviceType(),
          referrer: typeof document !== "undefined" ? document.referrer : undefined,
        });
      } catch (error) {
        console.error("Error tracking event:", error);
      }
    },
    [],
  );

  const trackPageView = useCallback(
    async (page: string, properties?: Record<string, any>) => {
      trackPageViewPostHog(page, {
        ...properties,
        userId: user?.id,
      });
    },
    [user],
  );

  const trackConversionWrapper = useCallback(
    async (type: string, value?: number, properties?: Record<string, any>) => {
      trackConversion(type, value, {
        ...properties,
        userId: user?.id,
      });
    },
    [user],
  );

  const trackMicroInteractionEvent = useCallback(
    async (type: string, properties?: Record<string, any>) => {
      try {
        trackEvent(`micro_interaction_${type}`, {
          type,
          category: "micro_interactions",
          userId: user?.id,
          ...properties,
        });
      } catch (error) {
        console.error("Error tracking micro-interaction:", error);
      }
    },
    [user],
  );

  const trackMarketingEventWrapper = useCallback(
    async (eventName: string, properties: Record<string, any>) => {
      try {
        trackEvent(`marketing_${eventName}`, {
          category: "marketing",
          userId: user?.id,
          ...properties,
        });
      } catch (error) {
        console.error("Error tracking marketing event:", error);
      }
    },
    [user],
  );

  return {
    trackEvent: trackEventWrapper,
    trackPageView,
    trackConversion: trackConversionWrapper,
    trackMicroInteraction: trackMicroInteractionEvent,
    trackMarketingEvent: trackMarketingEventWrapper,
  };
}

/**
 * Hook for tracking feature demo interactions
 */
export function useFeatureDemoAnalytics() {
  const { user } = useUser();

  const trackDemoViewed = useCallback(
    async (demoId: string, properties?: Record<string, any>) => {
      trackEvent("demo_viewed", { demoId, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackDemoPlayed = useCallback(
    async (demoId: string, properties?: Record<string, any>) => {
      trackEvent("demo_played", { demoId, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackDemoCompleted = useCallback(
    async (demoId: string, properties?: Record<string, any>) => {
      trackEvent("demo_completed", { demoId, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackTryClicked = useCallback(
    async (demoId: string, properties?: Record<string, any>) => {
      trackEvent("demo_try_clicked", { demoId, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackUpgradeClicked = useCallback(
    async (demoId: string, properties?: Record<string, any>) => {
      trackEvent("demo_upgrade_clicked", { demoId, userId: user?.id, ...properties });
    },
    [user],
  );

  return {
    trackDemoViewed,
    trackDemoPlayed,
    trackDemoCompleted,
    trackTryClicked,
    trackUpgradeClicked,
  };
}

/**
 * Hook for tracking showcase carousel interactions
 */
export function useShowcaseAnalytics() {
  const { user } = useUser();

  const trackShowcaseViewed = useCallback(
    async (showcase: string, properties?: Record<string, any>) => {
      trackEvent("showcase_viewed", { showcase, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackShowcaseNavigation = useCallback(
    async (showcase: string, direction: "next" | "prev", properties?: Record<string, any>) => {
      trackEvent("showcase_navigated", { showcase, direction, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackFeatureClick = useCallback(
    async (showcase: string, featureId: string, properties?: Record<string, any>) => {
      trackEvent("showcase_feature_clicked", { showcase, featureId, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackCTAClick = useCallback(
    async (showcase: string, ctaType: string, properties?: Record<string, any>) => {
      trackEvent("showcase_cta_clicked", { showcase, ctaType, userId: user?.id, ...properties });
    },
    [user],
  );

  return {
    trackShowcaseViewed,
    trackShowcaseNavigation,
    trackFeatureClick,
    trackCTAClick,
  };
}

/**
 * Hook for tracking premium conversion funnel
 */
export function usePremiumConversionAnalytics() {
  const { user } = useUser();

  const trackInterest = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("premium_interest", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  const trackComparison = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("premium_comparison", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  const trackPricingViewed = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("pricing_viewed", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  const trackCheckoutStarted = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("checkout_started", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  const trackPaymentCompleted = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("payment_completed", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  const trackUpgradeCompleted = useCallback(
    async (properties?: Record<string, any>) => {
      trackConversion("upgrade_completed", undefined, { userId: user?.id, ...properties });
    },
    [user],
  );

  return {
    trackInterest,
    trackComparison,
    trackPricingViewed,
    trackCheckoutStarted,
    trackPaymentCompleted,
    trackUpgradeCompleted,
  };
}

/**
 * Hook for tracking viral marketing events
 */
export function useViralAnalytics() {
  const { user } = useUser();

  const trackShare = useCallback(
    async (platform: string, content: string, properties?: Record<string, any>) => {
      trackEvent("viral_share", { platform, content, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackReferral = useCallback(
    async (referralCode: string, properties?: Record<string, any>) => {
      trackEvent("viral_referral", { referralCode, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackSocialPost = useCallback(
    async (platform: string, postType: string, properties?: Record<string, any>) => {
      trackEvent("viral_social_post", { platform, postType, userId: user?.id, ...properties });
    },
    [user],
  );

  const trackEmbed = useCallback(
    async (embedType: string, properties?: Record<string, any>) => {
      trackEvent("viral_embed", { embedType, userId: user?.id, ...properties });
    },
    [user],
  );

  return {
    trackShare,
    trackReferral,
    trackSocialPost,
    trackEmbed,
  };
}

/**
 * Hook for component-level analytics tracking
 */
export function useComponentAnalytics(componentName: string) {
  const startTime = useRef(Date.now());
  const { user } = useUser();

  const trackInteraction = useCallback(
    async (action: string, properties?: Record<string, any>) => {
      trackEvent(`${componentName}_${action}`, {
        component: componentName,
        action,
        userId: user?.id,
        ...properties,
      });
    },
    [componentName, user],
  );

  const trackView = useCallback(
    async (properties?: Record<string, any>) => {
      trackEvent(`${componentName}_viewed`, {
        component: componentName,
        viewDuration: Date.now() - startTime.current,
        userId: user?.id,
        ...properties,
      });
    },
    [componentName, user],
  );

  const trackConversionEvent = useCallback(
    async (conversionType: string, value?: number) => {
      trackConversion(`${componentName}_${conversionType}`, value, {
        component: componentName,
        userId: user?.id,
      });
    },
    [componentName, user],
  );

  const trackError = useCallback(
    async (error: Error, properties?: Record<string, any>) => {
      trackEvent(`${componentName}_error`, {
        component: componentName,
        errorMessage: error.message,
        errorStack: error.stack,
        userId: user?.id,
        ...properties,
      });
    },
    [componentName, user],
  );

  const trackPerformance = useCallback(
    async (metrics: { loadTime?: number; renderTime?: number; interactionDelay?: number }) => {
      trackEvent(`${componentName}_performance`, {
        component: componentName,
        ...metrics,
        userId: user?.id,
      });
    },
    [componentName, user],
  );

  return {
    trackInteraction,
    trackView,
    trackConversion: trackConversionEvent,
    trackError,
    trackPerformance,
  };
}

/**
 * Performance monitoring hook
 */
export function usePerformanceMonitoring(componentName: string) {
  const renderStart = useRef(Date.now());
  const interactionStart = useRef<number | null>(null);
  const { user } = useUser();

  useEffect(() => {
    // Track render time
    const renderTime = Date.now() - renderStart.current;
    trackEvent(`${componentName}_render_performance`, {
      renderTime,
      component: componentName,
      userId: user?.id,
    });
  }, [componentName, user]);

  const trackInteractionStart = useCallback(() => {
    interactionStart.current = Date.now();
  }, []);

  const trackInteractionEnd = useCallback(
    async (interactionType: string) => {
      if (interactionStart.current) {
        const delay = Date.now() - interactionStart.current;
        trackEvent(`${componentName}_interaction_performance`, {
          interactionDelay: delay,
          interactionType,
          component: componentName,
          userId: user?.id,
        });
        interactionStart.current = null;
      }
    },
    [componentName, user],
  );

  const trackError = useCallback(
    async (error: Error) => {
      trackEvent(`${componentName}_performance_error`, {
        errorMessage: error.message,
        component: componentName,
        userId: user?.id,
      });
    },
    [componentName, user],
  );

  return {
    trackInteractionStart,
    trackInteractionEnd,
    trackError,
  };
}

// Helper functions
function getDeviceType(): "desktop" | "mobile" | "tablet" {
  if (typeof window === "undefined") return "desktop";

  const width = window.innerWidth;
  if (width < 768) return "mobile";
  if (width < 1024) return "tablet";
  return "desktop";
}
