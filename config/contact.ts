// Contact configuration and static data
export interface ContactChannel {
  id: string;
  icon: string;
  title: string;
  description: string;
  value: string;
  link: string | null;
  responseTime: string;
  availability: string;
  primary?: boolean;
  online?: boolean;
  action?: () => void;
}

export interface ContactCategory {
  key: string;
  label: string;
  icon: string;
}

export interface Priority {
  key: string;
  label: string;
  color: "success" | "primary" | "warning" | "danger";
  description: string;
}

// Static content configuration
export const contactConfig = {
  // Form validation rules
  validation: {
    maxFileSize: 8 * 1024 * 1024, // 8MB (UploadThing limit)
    maxFiles: 3,
    allowedFileTypes: [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
    minFormProgress: 60, // Minimum progress required to submit
  },

  // Business hours and availability
  businessHours: {
    start: 9, // 9 AM
    end: 18, // 6 PM
    timezone: "EST",
    workDays: [1, 2, 3, 4, 5], // Monday to Friday
  },

  // Response time mapping
  responseTimes: {
    urgent: "1-2",
    high: "2-4",
    normal: "4-8",
    low: "8-24",
  } as Record<string, string>,

  // Upload progress simulation
  uploadSettings: {
    progressInterval: 100, // milliseconds
    progressIncrement: 30, // max random increment
    checkInterval: 30000, // chat availability check interval
  },
};

// Contact categories configuration
export const getContactCategories = (): ContactCategory[] => [
  { key: "general", label: "General", icon: "tabler:help" },
  { key: "technical", label: "Technical", icon: "tabler:bug" },
  { key: "billing", label: "Billing", icon: "tabler:credit-card" },
  { key: "feature", label: "Feature Request", icon: "tabler:bulb" },
  { key: "account", label: "Account", icon: "tabler:user" },
];

// Priority levels configuration
export const getPriorities = (): Priority[] => [
  { key: "low", label: "Low", color: "success", description: "Response within 8-24 hours" },
  { key: "normal", label: "Normal", color: "primary", description: "Response within 4-8 hours" },
  { key: "high", label: "High", color: "warning", description: "Response within 2-4 hours" },
  { key: "urgent", label: "Urgent", color: "danger", description: "Response within 1-2 hours" },
];

// Contact channels configuration
export const getContactChannels = (chatOnline: boolean, onChatOpen: () => void): ContactChannel[] => [
  {
    id: "email",
    icon: "tabler:mail",
    title: "Email Support",
    description: "Send us an email for detailed support",
    value: "<EMAIL>",
    link: "mailto:<EMAIL>",
    responseTime: "Within 4-8 hours",
    availability: "24/7",
    primary: true,
  },
  {
    id: "chat",
    icon: "tabler:message-circle",
    title: "Live Chat",
    description: "Chat with our support team",
    value: chatOnline ? "Online" : "Offline",
    link: null,
    responseTime: "Instant",
    availability: "Mon-Fri 9AM-6PM EST",
    online: chatOnline,
    action: onChatOpen,
  },
  {
    id: "support",
    icon: "tabler:help-circle",
    title: "Support Center",
    description: "Browse our help articles and FAQs",
    value: "Help Center",
    link: "/support",
    responseTime: "Instant",
    availability: "24/7",
  },
];

// Utility functions
export const checkBusinessHours = (): boolean => {
  const now = new Date();
  const hours = now.getHours();
  const day = now.getDay();

  return (
    contactConfig.businessHours.workDays.includes(day) &&
    hours >= contactConfig.businessHours.start &&
    hours <= contactConfig.businessHours.end
  );
};

export const calculateFormProgress = (selectedCategory: string, selectedPriority: string): number => {
  const form = document.getElementById("contact-form") as HTMLFormElement;
  if (!form) return 0;

  const formData = new FormData(form);
  const fields = ["name", "email", "subject", "message"];
  const filledFields = fields.filter((field) => {
    const value = formData.get(field)?.toString().trim();
    return value && value.length > 0;
  });

  const categorySelected = selectedCategory ? 1 : 0;
  const prioritySelected = selectedPriority ? 1 : 0;

  return ((filledFields.length + categorySelected + prioritySelected) / (fields.length + 2)) * 100;
};
