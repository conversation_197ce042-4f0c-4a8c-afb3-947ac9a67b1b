export type ContextType =
  | "experience"
  | "project"
  | "education"
  | "award"
  | "certification"
  | "hobby"
  | "volunteering"
  | "reference"
  | "bio";

export type AITone = "professional" | "casual" | "technical";

export type AILength = "short" | "medium" | "detailed";

export type AILanguage = "en" | "ar";

export interface ResumeContext {
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  bio?: string;
  skills?: Array<{ name: string; category?: string }>;
}

export interface ItemContext {
  title?: string;
  company?: string;
  organization?: string;
  institution?: string;
  startDate?: string;
  endDate?: string;
  isCurrent?: boolean;
  role?: string;
  degree?: string;
  fieldOfStudy?: string;
  issuer?: string;
  currentDescription?: string;
}

export interface AIOptions {
  tone: AITone;
  length: AILength;
  language: AILanguage;
}

export interface GenerateDescriptionRequest {
  contextType: ContextType;
  resumeContext: ResumeContext;
  itemContext: ItemContext;
  options: AIOptions;
}

export interface GenerateDescriptionResponse {
  success: boolean;
  description?: string;
  error?: string;
  contextType?: ContextType;
  prompt?: string; // Only in development
}

export interface AIProviderConfig {
  enabled: boolean;
  apiUrl?: string;
  model?: string;
  apiKey?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AIServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    model?: string;
  };
}

// Extended types for future features
export interface AIAnalyticsData {
  userId: string;
  contextType: ContextType;
  requestTimestamp: Date;
  options: AIOptions;
  success: boolean;
  errorType?: string;
  processingTime: number;
}

export interface AIUsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageProcessingTime: number;
  mostUsedContextType: ContextType;
  mostUsedTone: AITone;
  mostUsedLength: AILength;
}

// Validation schemas for AI requests
export interface AIRequestValidation {
  contextType: {
    required: true;
    type: "string";
    enum: ContextType[];
  };
  resumeContext: {
    required: true;
    type: "object";
  };
  itemContext: {
    required: false;
    type: "object";
  };
  options: {
    required: true;
    type: "object";
    properties: {
      tone: { type: "string"; enum: AITone[] };
      length: { type: "string"; enum: AILength[] };
      language: { type: "string"; enum: AILanguage[] };
    };
  };
}
