"use client";

import { useUser } from "@clerk/nextjs";
import { Accordion, AccordionItem } from "@heroui/accordion";
import { Button } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Input, Textarea } from "@heroui/input";
import { Select, SelectItem } from "@heroui/react";
import { Tab, Tabs } from "@heroui/tabs";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { getSupportTicketByNumber, getUserSupportTickets, submitSupportTicket } from "@/actions/support";
import SubmitTicket from "@/app/support/components/SubmitTicket";
import { subtitle, title } from "@/components/primitives";
import { SupportTicket } from "@/db/schema";

export default function SupportPage() {
  const { user } = useUser();
  const [message, setMessage] = useState({ type: "", content: "" });
  const [userTickets, setUserTickets] = useState<SupportTicket[]>([]);

  const [ticketLookup, setTicketLookup] = useState<{ number: string; email: string; result: SupportTicket | null }>({
    number: "",
    email: "",
    result: null,
  });

  // Load user tickets if authenticated
  useEffect(() => {
    if (user?.id) {
      loadUserTickets();
    }
  }, [user]);

  const loadUserTickets = async () => {
    if (!user?.id) return;

    try {
      const result = await getUserSupportTickets(user.id);
      if (result.success) {
        setUserTickets(result.tickets || []);
      }
    } catch (error) {
      console.error("Failed to load user tickets:", error);
    }
  };

  const handleTicketLookup = async () => {
    if (!ticketLookup.number || !ticketLookup.email) {
      setMessage({ type: "error", content: "Please fill in both ticket number and email" });
      return;
    }

    try {
      const result = await getSupportTicketByNumber(ticketLookup.number, ticketLookup.email);
      if (result.success) {
        setTicketLookup({ ...ticketLookup, result: result.ticket || null });
        setMessage({ type: "success", content: "Ticket found successfully" });
      } else {
        setMessage({ type: "error", content: result.error || "Ticket not found" });
        setTicketLookup({ ...ticketLookup, result: null });
      }
    } catch (error) {
      setMessage({ type: "error", content: "Failed to lookup ticket" });
    }
  };

  const faqItems = [
    {
      title: "How do I create an account?",
      content:
        "You can create an account by clicking the Sign Up button and following the registration process. We support email registration and social login options.",
    },
    {
      title: "How do I create a resume?",
      content:
        "After logging in, click 'Create Resume' to start building your resume. Choose from our professional templates and fill in your information section by section.",
    },
    {
      title: "Can I customize the templates?",
      content:
        "Yes! All our templates are fully customizable. You can change colors, fonts, spacing, and layout to match your personal style and industry requirements.",
    },
    {
      title: "How do I export my resume?",
      content:
        "You can export your resume as a PDF by clicking the 'Download PDF' button. We also offer other export formats for premium users.",
    },
    {
      title: "What are the pricing options?",
      content:
        "We offer both free and premium plans. The free plan includes basic templates and features, while premium unlocks all templates, customization options, and advanced features.",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "warning";
      case "in_progress":
        return "primary";
      case "resolved":
        return "success";
      case "closed":
        return "default";
      default:
        return "default";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "danger";
      case "high":
        return "warning";
      case "normal":
        return "primary";
      case "low":
        return "default";
      default:
        return "default";
    }
  };

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className={title({ size: "lg", class: "mb-4" })}>Support Center</h1>
        <p className={subtitle({ class: "mb-8 max-w-3xl mx-auto" })}>
          Get help with your account, resumes, and any questions you might have. Our support team is here to help you
          succeed.
        </p>
      </div>

      <Tabs aria-label="Support options" className="w-full">
        {/* FAQ Tab */}
        <Tab
          key="faq"
          title={
            <div className="flex items-center gap-2">
              <Icon icon="tabler:help" className="w-4 h-4" />
              FAQ
            </div>
          }
        >
          <Card>
            <CardHeader>
              <h2 className="text-xl font-bold">Frequently Asked Questions</h2>
            </CardHeader>
            <CardBody>
              <Accordion>
                {faqItems.map((item, index) => (
                  <AccordionItem
                    key={index}
                    aria-label={item.title}
                    title={item.title}
                    startContent={<Icon icon="tabler:help-circle" className="w-5 h-5 text-blue-500" />}
                  >
                    <div className="text-gray-600 dark:text-gray-400 leading-relaxed">{item.content}</div>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardBody>
          </Card>
        </Tab>

        {/* Submit Ticket Tab */}
        <Tab
          key="submit"
          title={
            <div className="flex items-center gap-2">
              <Icon icon="tabler:ticket" className="w-4 h-4" />
              Submit Ticket
            </div>
          }
        >
          <SubmitTicket />
        </Tab>

        {/* My Tickets Tab (for authenticated users) */}
        {user && (
          <Tab
            key="tickets"
            title={
              <div className="flex items-center gap-2">
                <Icon icon="tabler:list" className="w-4 h-4" />
                My Tickets
              </div>
            }
          >
            <Card>
              <CardHeader>
                <h2 className="text-xl font-bold">My Support Tickets</h2>
              </CardHeader>
              <CardBody>
                {userTickets.length === 0 ? (
                  <div className="text-center py-8">
                    <Icon icon="tabler:ticket-off" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">
                      No support tickets found. Submit a ticket if you need help.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {userTickets.map((ticket) => (
                      <Card key={ticket.id} className="border border-gray-200 dark:border-gray-700">
                        <CardBody className="p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-lg">{ticket.subject}</h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Ticket #: {ticket.ticketNumber}
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status) === "success" ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200" : getStatusColor(ticket.status) === "warning" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200" : getStatusColor(ticket.status) === "primary" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200" : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`}
                              >
                                {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1).replace("_", " ")}
                              </span>
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority) === "danger" ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200" : getPriorityColor(ticket.priority) === "warning" ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-200" : getPriorityColor(ticket.priority) === "primary" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200" : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`}
                              >
                                {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)}
                              </span>
                            </div>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">{ticket.message}</p>
                          <div className="flex justify-between items-center text-xs text-gray-500">
                            <span>Created: {new Date(ticket.createdAt).toLocaleDateString()}</span>
                            <span>
                              Category:{" "}
                              {ticket.category.charAt(0).toUpperCase() + ticket.category.slice(1).replace("_", " ")}
                            </span>
                          </div>
                        </CardBody>
                      </Card>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
          </Tab>
        )}

        {/* Ticket Lookup Tab */}
        <Tab
          key="lookup"
          title={
            <div className="flex items-center gap-2">
              <Icon icon="tabler:search" className="w-4 h-4" />
              Lookup Ticket
            </div>
          }
        >
          <Card>
            <CardHeader>
              <h2 className="text-xl font-bold">Lookup Support Ticket</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <Input
                    label="Ticket Number"
                    placeholder="QCV-123456-ABC"
                    variant="bordered"
                    value={ticketLookup.number}
                    onChange={(e) => setTicketLookup({ ...ticketLookup, number: e.target.value })}
                    startContent={<Icon icon="tabler:ticket" className="w-4 h-4 text-default-400" />}
                  />
                  <Input
                    type="email"
                    label="Email Address"
                    placeholder="Enter your email address"
                    variant="bordered"
                    value={ticketLookup.email}
                    onChange={(e) => setTicketLookup({ ...ticketLookup, email: e.target.value })}
                    startContent={<Icon icon="tabler:mail" className="w-4 h-4 text-default-400" />}
                  />
                </div>

                <Button
                  color="primary"
                  onClick={handleTicketLookup}
                  startContent={<Icon icon="tabler:search" className="w-4 h-4" />}
                >
                  Search Ticket
                </Button>

                {ticketLookup.result && (
                  <Card className="border border-gray-200 dark:border-gray-700">
                    <CardBody className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold text-xl">{ticketLookup.result.subject}</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Ticket #: {ticketLookup.result.ticketNumber}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(ticketLookup.result.status) === "success" ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200" : getStatusColor(ticketLookup.result.status) === "warning" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200" : getStatusColor(ticketLookup.result.status) === "primary" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200" : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`}
                          >
                            {ticketLookup.result.status.charAt(0).toUpperCase() +
                              ticketLookup.result.status.slice(1).replace("_", " ")}
                          </span>
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(ticketLookup.result.priority) === "danger" ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200" : getPriorityColor(ticketLookup.result.priority) === "warning" ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-200" : getPriorityColor(ticketLookup.result.priority) === "primary" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200" : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`}
                          >
                            {ticketLookup.result.priority.charAt(0).toUpperCase() +
                              ticketLookup.result.priority.slice(1)}
                          </span>
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
                        <h4 className="font-medium mb-2">Message</h4>
                        <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                          {ticketLookup.result.message}
                        </p>
                      </div>

                      {ticketLookup.result.resolutionMessage && (
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-4">
                          <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">Resolution</h4>
                          <p className="text-green-700 dark:text-green-300 whitespace-pre-wrap">
                            {ticketLookup.result.resolutionMessage}
                          </p>
                        </div>
                      )}

                      <div className="flex justify-between items-center text-sm text-gray-500">
                        <span>Created: {new Date(ticketLookup.result.createdAt).toLocaleDateString()}</span>
                        <span>
                          Category:{" "}
                          {ticketLookup.result.category.charAt(0).toUpperCase() +
                            ticketLookup.result.category.slice(1).replace("_", " ")}
                        </span>
                      </div>
                    </CardBody>
                  </Card>
                )}
              </div>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>
    </div>
  );
}
