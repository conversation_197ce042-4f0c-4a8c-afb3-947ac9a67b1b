import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

const isPublicRoute = createRouteMatcher([
  "/",
  "/about",
  "/docs",
  "/blog",
  "/templates",
  "/templates/(.*)",
  "/share/(.*)",
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/api/uploadthing(.*)",
]);

export default clerkMiddleware((auth, req) => {
  // Skip auth for API routes and PDF routes
  if (req.nextUrl.pathname.startsWith("/api/") || req.nextUrl.pathname.startsWith("/pdf/")) {
    // Only apply auth protection for non-public routes
    if (!isPublicRoute(req)) {
      auth.protect();
    }
    return;
  }

  // Add custom header for share routes to help with layout detection
  if (req.nextUrl.pathname.includes("/share/")) {
    const response = new Response();
    response.headers.set("x-is-share-route", "true");
    return response;
  }

  // Protect private routes
  if (!isPublicRoute(req)) {
    auth.protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
