import toast from "react-hot-toast";
import {
  EXPORT_CONFIG,
  ExportType,
  generateExportFilename,
  getExportEndpoint,
  getMaxExportLimit,
} from "@/config/export";

export interface ExportItem {
  id: number | string;
}

export interface ExportOptions {
  locale?: string;
  timeout?: number;
}

export interface ExportResult {
  success: boolean;
  error?: string;
}

/**
 * Download a blob as a file
 */
function downloadBlob(blob: Blob, filename: string): void {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}

/**
 * Export multiple items to a ZIP file
 */
export async function exportItems<T extends ExportItem>(
  items: T[],
  type: ExportType,
  options: ExportOptions = {},
): Promise<ExportResult> {
  const toastId = toast.loading(EXPORT_CONFIG.MESSAGES.PREPARING);

  try {
    // Get item IDs
    const itemIds = items.map((item) => item.id);

    // Check if there are items to export
    if (itemIds.length === 0) {
      toast.dismiss(toastId);
      toast.error(EXPORT_CONFIG.MESSAGES.NO_ITEMS);
      return { success: false, error: "No items to export" };
    }

    // Check export limit
    const maxLimit = getMaxExportLimit(type);
    if (itemIds.length > maxLimit) {
      toast.dismiss(toastId);
      const errorMessage = EXPORT_CONFIG.MESSAGES.LIMIT_EXCEEDED(maxLimit);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }

    // Update toast message
    const generatingMessage = EXPORT_CONFIG.MESSAGES.GENERATING(itemIds.length, type.slice(0, -1));
    toast.loading(generatingMessage, { id: toastId });

    // Prepare request payload
    const payload = {
      [type === "resumes" ? "resumeIds" : "websiteIds"]: itemIds,
      locale: options.locale || document?.documentElement?.lang || "en",
    };

    // Make API request
    const endpoint = getExportEndpoint(type);
    const controller = new AbortController();
    const timeout = options.timeout || EXPORT_CONFIG.TIMEOUTS.DEFAULT;

    // Set timeout
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to export items");
    }

    // Get the zip file blob
    const blob = await response.blob();

    // Download the file
    const filename = generateExportFilename(type);
    downloadBlob(blob, filename);

    // Show success message
    toast.dismiss(toastId);
    const successMessage = EXPORT_CONFIG.MESSAGES.SUCCESS(itemIds.length, type.slice(0, -1));
    toast.success(successMessage);

    return { success: true };
  } catch (error) {
    toast.dismiss(toastId);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === "AbortError") {
        toast.error("Export timeout - please try with fewer items");
        return { success: false, error: "Export timeout" };
      }

      if (error.message.includes("Premium subscription required")) {
        toast.error(EXPORT_CONFIG.MESSAGES.PREMIUM_REQUIRED);
        return { success: false, error: "Premium subscription required" };
      }

      toast.error(error.message);
      return { success: false, error: error.message };
    }

    toast.error(EXPORT_CONFIG.MESSAGES.GENERIC_ERROR);
    console.error("Export error:", error);
    return { success: false, error: "Unknown error occurred" };
  }
}

/**
 * Export resumes convenience function
 */
export async function exportResumes<T extends ExportItem>(
  resumes: T[],
  options?: ExportOptions,
): Promise<ExportResult> {
  return exportItems(resumes, "resumes", options);
}

/**
 * Export websites convenience function
 */
export async function exportWebsites<T extends ExportItem>(
  websites: T[],
  options?: ExportOptions,
): Promise<ExportResult> {
  return exportItems(websites, "websites", options);
}
