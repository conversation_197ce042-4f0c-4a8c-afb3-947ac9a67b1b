"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>B<PERSON>, <PERSON>, Image } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import NextImage from "next/image";
import Link from "next/link";
import { useState } from "react";
import { subtitle, title } from "@/components/primitives";
import { featuredTemplates } from "@/lib/constants";

export default function TemplatesShowcase() {
  const [activeIndex, setActiveIndex] = useState(1);

  const handleNext = () => {
    setActiveIndex((prev) => (prev + 1) % featuredTemplates.length);
  };

  const handlePrev = () => {
    setActiveIndex((prev) => (prev - 1 + featuredTemplates.length) % featuredTemplates.length);
  };

  return (
    <section
      className="py-24 md:py-32 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-primary-950/20 dark:via-background dark:to-secondary-950/20 relative overflow-hidden"
      id="templates"
    >
      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className={title({ size: "lg" })}>Designed to get you hired</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>
            Choose from a collection of beautiful, professional, and ATS-friendly resume templates.
          </p>
        </motion.div>

        <div className="relative h-[500px] flex items-center justify-center">
          <div className="absolute w-full h-full [perspective:1000px]">
            {featuredTemplates.map((template, index) => {
              const distance = index - activeIndex;
              const rotateY = distance * 20;
              const translateX = distance * 300;
              const scale = distance === 0 ? 1 : 0.8;
              const zIndex = featuredTemplates.length - Math.abs(distance);
              const opacity = Math.abs(distance) > 1 ? 0 : 1;

              return (
                <motion.div
                  key={template.id}
                  className="absolute top-0 left-0 w-full h-full flex items-center justify-center"
                  initial={false}
                  animate={{
                    transform: `translateX(${translateX}px) rotateY(${rotateY}deg) scale(${scale})`,
                    zIndex,
                    opacity,
                  }}
                  transition={{ type: "spring", stiffness: 100, damping: 20 }}
                >
                  <Card className="w-[350px] h-[480px] shadow-2xl bg-white/80 dark:bg-default-100/80 backdrop-blur-md border border-default-200 dark:border-default-800">
                    <CardBody className="p-0">
                      <Image
                        as={NextImage}
                        alt={`${template.name} resume template preview`}
                        className="object-cover w-full h-full"
                        height={480}
                        src={`/assets/images/templates/${template.id}.jpg`}
                        width={350}
                      />
                    </CardBody>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>

        <div className="flex items-center justify-center gap-6 mt-12">
          <Button isIconOnly size="lg" variant="flat" onPress={handlePrev}>
            <Icon icon="heroicons:chevron-left-20-solid" className="w-6 h-6" />
          </Button>
          <Button
            as={Link}
            href="/templates"
            color="primary"
            size="lg"
            variant="shadow"
            className="font-semibold px-8"
            startContent={<Icon icon="heroicons:swatch-20-solid" />}
          >
            View All Templates
          </Button>
          <Button isIconOnly size="lg" variant="flat" onPress={handleNext}>
            <Icon icon="heroicons:chevron-right-20-solid" className="w-6 h-6" />
          </Button>
        </div>
      </div>
    </section>
  );
}
