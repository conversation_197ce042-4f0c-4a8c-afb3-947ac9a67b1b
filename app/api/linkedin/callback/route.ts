import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const state = searchParams.get("state");
  const error = searchParams.get("error");

  // Handle OAuth errors
  if (error) {
    const errorDescription = searchParams.get("error_description");
    return NextResponse.redirect(`/resumes?error=linkedin_auth_failed&description=${errorDescription}`);
  }

  if (!code || !state) {
    return NextResponse.redirect("/resumes?error=invalid_callback");
  }

  try {
    // Verify state parameter
    const stateData = JSON.parse(Buffer.from(state, "base64").toString());
    const { userId } = await auth();

    if (stateData.userId !== userId) {
      return NextResponse.redirect("/resumes?error=invalid_state");
    }

    // Exchange code for access token
    const clientId = process.env.LINKEDIN_CLIENT_ID;
    const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
    const redirectUri = process.env.LINKEDIN_REDIRECT_URI;

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error("LinkedIn OAuth not configured");
    }

    const tokenResponse = await fetch("https://www.linkedin.com/oauth/v2/accessToken", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        code,
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error("Failed to exchange code for token");
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Store access token temporarily in session/cookie for import
    const response = NextResponse.redirect("/resumes?linkedin_connected=true");
    response.cookies.set("linkedin_token", accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 3600, // 1 hour
      sameSite: "lax",
    });

    return response;
  } catch (error) {
    console.error("LinkedIn OAuth error:", error);
    return NextResponse.redirect("/resumes?error=auth_failed");
  }
}
