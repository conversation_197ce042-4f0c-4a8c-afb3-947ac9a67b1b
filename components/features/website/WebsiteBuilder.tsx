"use client";
import { useRouter } from "@bprogress/next/app";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Link,
  Select,
  SelectItem,
  Switch,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import React, { useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { UpgradeModal } from "@/components/payment";
import { Resume, Website, WebsiteTemplate } from "@/db/schema";
import { type FeatureFlagKey, useFeatureFlagEnabled } from "@/hooks/use-feature-flags";
import EmptyResumesState from "./EmptyResumesState";
import { WebsiteTemplateSelector } from "./templates/template-registry";
import { WebsitePreview } from "./website-preview";

interface WebsiteBuilderProps {
  resumes: Resume[];
  website?: Website;
  templates: WebsiteTemplate[];
}

export const WebsiteBuilder: React.FC<WebsiteBuilderProps> = ({ resumes, website, templates }) => {
  const router = useRouter();
  const { data: currentUser } = trpc.user.getCurrentUser.useQuery();
  const { isOpen: isUpgradeOpen, onOpen: onUpgradeOpen, onClose: onUpgradeClose } = useDisclosure();
  // PostHog feature flag integration for website builder
  const {
    isEnabled: websiteBuilderEnabled,
    isLoading: websiteBuilderLoading,
    trackUsage: trackWebsiteUsage,
  } = useFeatureFlagEnabled("website_builder", false);

  // Simple access control based on PostHog flags
  const canPublishWebsite = websiteBuilderEnabled;
  const showUpgrade = () => onUpgradeOpen();

  const resumesSelectItems = useMemo(() => {
    return resumes.map((r) => ({ key: r.id, label: r.title }));
  }, [resumes]);

  // Form state - start with first available resume if no resume is passed
  const [selectedResumeId, setSelectedResumeId] = useState<string>("");

  const getFullResume = trpc.resumes.getFullResume.useQuery(
    { id: Number(selectedResumeId) },
    { enabled: !!website?.resumeId },
  );

  const { isLoading: isLoadingFullResume, data: resume } = getFullResume;

  // TRPC mutations and queries for websites
  const utils = trpc.useUtils();
  const createWebsiteMutation = trpc.websites.createWebsite.useMutation();
  const updateWebsiteMutation = trpc.websites.updateWebsite.useMutation();
  const togglePublicMutation = trpc.websites.toggleWebsitePublic.useMutation();
  const generateSlugQuery = trpc.websites.generateSlug.useQuery(
    {
      firstName: resume?.firstName || "",
      lastName: resume?.lastName || "",
    },
    { enabled: false },
  );

  const [isLoading, setIsLoading] = useState(false);

  // Track website builder usage when component mounts
  useEffect(() => {
    if (canPublishWebsite && !websiteBuilderLoading) {
      trackWebsiteUsage();
    }
  }, [canPublishWebsite, websiteBuilderLoading, trackWebsiteUsage]);
  const [selectedTemplate, setSelectedTemplate] = useState<number>(website?.websiteTemplateId || 0);
  const [slug, setSlug] = useState(website?.slug || "");
  const [isPublic, setIsPublic] = useState(website?.isPublic || false);

  // Validation state
  const [slugError, setSlugError] = useState<string | null>(null);
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);

  // Set default template when templates are loaded
  useEffect(() => {
    if (templates.length > 0 && selectedTemplate === 0 && !website) {
      setSelectedTemplate(templates[0].id);
    }
  }, [templates, selectedTemplate, website]);

  // Generate initial slug from resume data
  useEffect(() => {
    if (!website && resume && !slug) {
      const generateInitialSlug = async () => {
        const result = await generateSlugQuery.refetch();
        if (result.data?.slug) {
          setSlug(result.data.slug);
        }
      };
      generateInitialSlug();
    }
  }, [resume, website, slug]);

  // Validate slug availability
  const validateSlug = async (slugValue: string) => {
    if (!slugValue) {
      setSlugError("URL slug is required");
      return false;
    }

    if (slugValue.length < 3) {
      setSlugError("URL slug must be at least 3 characters long");
      return false;
    }

    if (!/^[a-z0-9-]+$/.test(slugValue)) {
      setSlugError("URL slug can only contain lowercase letters, numbers, and hyphens");
      return false;
    }

    setIsCheckingSlug(true);
    try {
      const result = await utils.websites.checkSlugAvailability.fetch({
        slug: slugValue,
        excludeWebsiteId: website?.id,
      });
      if (!result.available) {
        setSlugError("This URL slug is already taken");
        return false;
      }
      setSlugError(null);
      return true;
    } catch (_error) {
      setSlugError("Error checking slug availability");
      return false;
    } finally {
      setIsCheckingSlug(false);
    }
  };

  // Handle slug change with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (slug) {
        validateSlug(slug);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [slug, website?.id]);

  const handleSave = async () => {
    setIsLoading(true);

    try {
      // Validate template selection
      if (!selectedTemplate || selectedTemplate === 0) {
        toast.error("Please select a template");
        setIsLoading(false);
        return;
      }

      // Validate slug first
      const isSlugValid = await validateSlug(slug);
      if (!isSlugValid) {
        setIsLoading(false);
        return;
      }

      const websiteData = {
        slug,
      };

      let result;

      if (website) {
        // Update existing website
        result = await updateWebsiteMutation.mutateAsync({
          websiteId: website.id,
          slug: websiteData.slug,
          websiteTemplateId: selectedTemplate,
        });
      } else {
        // Create new website
        result = await createWebsiteMutation.mutateAsync({
          resumeId: Number(selectedResumeId),
          websiteTemplateId: selectedTemplate,
          slug: websiteData.slug,
        });
      }

      if (result.success) {
        toast.success(website ? "Website updated successfully" : "Website created successfully");
        // Refresh the page to get updated data
        router.refresh();
      } else {
        toast.error("Failed to save website");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Save website error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePublic = async () => {
    if (!website) return;

    // Check if user is trying to publish and doesn't have premium
    if (!isPublic && !canPublishWebsite) {
      // Track attempt when showing upgrade
      if (!websiteBuilderLoading) {
        trackWebsiteUsage();
      }
      showUpgrade();
      return;
    }

    setIsLoading(true);
    try {
      const result = await togglePublicMutation.mutateAsync({
        websiteId: website.id,
      });
      if (result.success) {
        setIsPublic(!isPublic);
        toast.success(!isPublic ? "Website published successfully!" : "Website unpublished");
        // Track successful publish
        if (!isPublic && !websiteBuilderLoading) {
          trackWebsiteUsage();
        }
        router.refresh();
      } else {
        toast.error("Failed to toggle website visibility");
      }
    } catch (error: any) {
      // Handle FORBIDDEN error from server
      if (error?.data?.code === "FORBIDDEN") {
        onUpgradeOpen();
      } else {
        toast.error("An unexpected error occurred");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const selectedTemplateData = templates.find((t) => t.id === selectedTemplate);
  const websitePrefix = `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/`;
  const websiteUrl = `${websitePrefix}${slug}`;

  if (resumes?.length === 0) {
    return <EmptyResumesState />;
  }

  return (
    <div className="grid lg:grid-cols-12 gap-8 min-h-screen">
      {/* Settings Panel */}
      <div className="lg:col-span-4 space-y-6 max-h-screen overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-transparent">
        {/* Resume Selection */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Select Resume</h3>
          </CardHeader>
          <CardBody>
            <Select
              label="Resume"
              placeholder="Choose a resume"
              selectedKeys={[selectedResumeId]}
              onChange={(e) => setSelectedResumeId(e.target.value)}
              className="w-full"
              startContent={<Icon icon="heroicons:document-text" className="w-4 h-4" />}
            >
              {resumesSelectItems.map((resume) => (
                <SelectItem key={resume.key}>{resume?.label}</SelectItem>
              ))}
            </Select>
          </CardBody>
        </Card>

        {/* Website Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Website Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <Input
                label="Website URL"
                placeholder="your-name-developer"
                value={slug}
                onChange={(e) => setSlug(e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ""))}
                startContent={<span className="text-sm text-gray-500">{websitePrefix}</span>}
                errorMessage={slugError}
                isInvalid={!!slugError}
                isDisabled={isLoading}
                description="This will be your public website URL"
              />
              {isCheckingSlug && <p className="text-sm text-gray-500 mt-1">Checking availability...</p>}
            </div>

            {/* Create/Update Website Button */}
            <Button
              color="primary"
              onPress={handleSave}
              isLoading={isLoading || websiteBuilderLoading}
              isDisabled={!!slugError || isCheckingSlug || !websiteBuilderEnabled}
              className="w-full"
            >
              {websiteBuilderLoading
                ? "Loading..."
                : !websiteBuilderEnabled
                  ? "Website Builder Disabled"
                  : website
                    ? "Update Website"
                    : "Create Website"}
            </Button>
          </CardBody>
        </Card>

        {/* Status and Actions */}
        {website && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Website Status</h3>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  Status:{" "}
                  {isPublic ? (
                    <span className="text-green-600 font-medium">Published</span>
                  ) : (
                    <span className="text-orange-600 font-medium">Draft</span>
                  )}
                </span>
                <Switch
                  isSelected={!!isPublic}
                  onValueChange={handleTogglePublic}
                  isDisabled={isLoading || websiteBuilderLoading || !websiteBuilderEnabled}
                  size="sm"
                >
                  {isPublic ? "Public" : "Private"}
                </Switch>
              </div>

              {isPublic && (
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-700 mb-2">Your website is live at:</p>
                    <a
                      href={websiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800 break-all"
                    >
                      {websiteUrl}
                    </a>
                  </div>

                  <Button
                    as={Link}
                    href={websiteUrl}
                    target="_blank"
                    color="primary"
                    variant="bordered"
                    size="sm"
                    startContent={<Icon icon="lucide:external-link" />}
                    className="w-full"
                  >
                    Visit Site
                  </Button>
                </div>
              )}

              {!isPublic && (
                <Button
                  color="success"
                  variant="bordered"
                  onPress={handleTogglePublic}
                  isLoading={isLoading}
                  className="w-full"
                >
                  Publish Website
                </Button>
              )}
            </CardBody>
          </Card>
        )}

        {/* Template Selection */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Choose Template</h3>
          </CardHeader>
          <CardBody className="p-4">
            <WebsiteTemplateSelector
              selectedTemplate={selectedTemplateData?.slug}
              websiteTemplates={templates}
              onTemplateSelect={(templateSlug) => {
                const template = templates.find((t) => t.slug === templateSlug);
                if (template) {
                  setSelectedTemplate(template.id);
                }
              }}
            />
          </CardBody>
        </Card>

        {/* Messages are now handled by toast notifications */}
      </div>

      {/* Preview Panel */}
      <div className="lg:col-span-8 h-screen">
        <Card className="h-full">
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Preview</h3>
              {selectedTemplateData && (
                <span className="text-sm text-gray-600">Template: {selectedTemplateData.name}</span>
              )}
            </div>
          </CardHeader>
          <CardBody className="p-0 h-full">
            <div className="h-full border rounded-lg overflow-hidden bg-white">
              <div className="w-full h-full overflow-auto">
                {resume ? (
                  <WebsitePreview
                    resume={resume}
                    website={{
                      id: website?.id || 0,
                      slug: slug || "preview",
                      isPublic: isPublic,
                      analytics: false,
                      userId: resume?.userId || "",
                      resumeId: resume?.id || 0,
                      websiteTemplateId: selectedTemplate,
                      createdAt: new Date().toISOString(),
                      updatedAt: new Date().toISOString(),
                    }}
                    templateSlug={selectedTemplateData?.slug || "elegant"}
                    className="w-full min-h-full"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Icon icon="heroicons:document-text" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Select a resume from the sidebar to preview your website</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Upgrade Modal */}
      <UpgradeModal isOpen={isUpgradeOpen} onClose={onUpgradeClose} feature="website" />
    </div>
  );
};

// Page wrapper component that uses TRPC for resume data
interface WebsiteBuilderPageProps {
  website: Website;
}
