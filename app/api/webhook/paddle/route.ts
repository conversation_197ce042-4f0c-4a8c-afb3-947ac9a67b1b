import crypto from "node:crypto";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { users } from "@/db/schema";
import { PaddleEventType, type PaddleWebhookPayload } from "@/lib/paddle";

// Verify Paddle webhook signature
function verifyWebhookSignature(rawBody: string, signatureHeader: string, secret: string): boolean {
  // Paddle sends the signature in the format: "ts=timestamp;h1=signature"
  const parts = signatureHeader.split(";");
  let timestamp = "";
  const signatures: string[] = [];

  for (const part of parts) {
    const [key, value] = part.split("=");
    if (key === "ts") {
      timestamp = value;
    } else if (key === "h1") {
      signatures.push(value);
    }
  }

  if (!timestamp || signatures.length === 0) {
    console.error("Invalid signature format");
    return false;
  }

  // Construct the signed payload
  const signedPayload = `${timestamp}:${rawBody}`;

  // Calculate the expected signature
  const hmac = crypto.createHmac("sha256", secret);
  hmac.update(signedPayload);
  const expectedSignature = hmac.digest("hex");

  // Check if any of the signatures match
  const isValid = signatures.some((sig) => sig === expectedSignature);

  return isValid;
}

export async function POST(request: NextRequest) {
  try {
    // Get the raw body and signature
    const rawBody = await request.text();
    const signature = request.headers.get("paddle-signature");

    console.log("🔔 Paddle webhook received:", {
      hasSignature: !!signature,
      bodyLength: rawBody.length,
      timestamp: new Date().toISOString(),
    });

    if (!signature) {
      console.error("❌ No signature provided");
      return NextResponse.json({ error: "No signature provided" }, { status: 401 });
    }

    // Verify webhook signature
    const webhookSecret = process.env.PADDLE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error("❌ Paddle webhook secret not configured");
      return NextResponse.json({ error: "Webhook secret not configured" }, { status: 500 });
    }

    // Enable signature verification for security
    const SKIP_SIGNATURE_VERIFICATION = false; // IMPORTANT: Keep this false in production!

    if (!SKIP_SIGNATURE_VERIFICATION && !verifyWebhookSignature(rawBody, signature, webhookSecret)) {
      console.error("❌ Invalid webhook signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    // Parse the webhook payload
    const payload: PaddleWebhookPayload = JSON.parse(rawBody);

    console.log("📦 Webhook payload:", {
      eventType: payload.event_type,
      transactionId: payload.data?.id,
      customData: payload.data?.custom_data,
      status: payload.data?.status,
    });

    // Handle different event types
    switch (payload.event_type) {
      case PaddleEventType.TransactionCompleted: {
        console.log("💳 Processing transaction completed event");

        // Get the user ID and plan from custom data
        const userId = payload.data.custom_data?.userId;
        const planId = (payload.data.custom_data as any)?.planId;

        if (!userId) {
          console.error("❌ No user ID provided in custom data");
          return NextResponse.json({ error: "No user ID provided" }, { status: 400 });
        }

        // Validate plan ID
        const validPlanId = planId && ["pro_monthly", "pro_yearly"].includes(planId) ? planId : "pro_monthly";

        console.log("👤 Updating user to premium:", { userId, planId: validPlanId });

        // Update user to premium
        const result = await db
          .update(users)
          .set({
            planId: validPlanId,
            paymentId: payload.data.id,
            paymentGateway: "paddle",
            paddleCustomerId: payload.data.customer_id,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(users.clerkId, userId));

        console.log("✅ User premium update completed:", {
          userId,
          transactionId: payload.data.id,
          updateResult: result,
        });

        break;
      }

      case PaddleEventType.TransactionUpdated: {
        console.log("🔄 Processing transaction updated event");

        const userId = payload.data.custom_data?.userId;
        const planId = (payload.data.custom_data as any)?.planId;

        if (!userId) {
          console.error("❌ No user ID provided in custom data");
          return NextResponse.json({ error: "No user ID provided" }, { status: 400 });
        }

        // Handle transaction completion via update event
        if (payload.data.status === "completed") {
          // Validate plan ID
          const validPlanId = planId && ["pro_monthly", "pro_yearly"].includes(planId) ? planId : "pro_monthly";

          console.log("👤 Updating user to premium via transaction.updated:", { userId, planId: validPlanId });

          // Update user to premium
          const result = await db
            .update(users)
            .set({
              planId: validPlanId,
              paymentId: payload.data.id,
              paymentGateway: "paddle",
              paddleCustomerId: payload.data.customer_id,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(users.clerkId, userId));

          console.log("✅ User premium update completed via transaction.updated:", {
            userId,
            transactionId: payload.data.id,
            updateResult: result,
          });
        }
        // Handle refunds
        else if (payload.data.status === "refunded") {
          console.log("💰 Processing refund for user:", { userId });

          // Revoke premium access on refund
          await db
            .update(users)
            .set({
              planId: "free",
              updatedAt: new Date().toISOString(),
            })
            .where(eq(users.clerkId, userId));

          console.log("✅ Premium access revoked due to refund:", { userId });
        }

        break;
      }

      default:
      // Silently ignore other event types
    }

    // Always return 200 OK for webhook endpoints
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Paddle webhook error:", error);

    // Return 200 OK even on error to prevent Paddle from retrying
    return NextResponse.json({
      received: true,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
