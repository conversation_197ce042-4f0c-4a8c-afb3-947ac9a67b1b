/* Enhanced Contact Page Styles */

.contactContainer {
  min-height: 100vh;
  background: linear-gradient(to bottom right, rgb(239 246 255), rgb(255 255 255), rgb(250 245 255));
  transition: background 0.3s ease;
}

:global(.dark) .contactContainer {
  background: linear-gradient(to bottom right, rgb(17 24 39), rgb(31 41 55), rgb(17 24 39));
}

.pageContainer {
  max-width: 72rem;
  margin: 0 auto;
  padding: 2rem 1rem;
}

@media (max-width: 768px) {
  .pageContainer {
    padding: 1rem 0.75rem;
  }
}

.gridLayout {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .gridLayout {
    grid-template-columns: 1fr 2fr;
    align-items: start;
  }
}

.channelsColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-width: 0; /* Prevent flex item overflow */
}

.formColumn {
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevent flex item overflow */
}

/* Ensure cards don't overlap and have proper spacing */
.channelsColumn > * {
  flex-shrink: 0;
}

.formColumn > * {
  flex-shrink: 0;
}

.heroSection {
  position: relative;
  overflow: hidden;
  margin-bottom: 3rem;
  padding: 2rem 0;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .heroSection {
    margin-bottom: 2rem;
    padding: 1.5rem 0;
  }
}

.heroSection::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(168, 85, 247, 0.05));
  transform: skewY(-1deg);
  transform-origin: top left;
  z-index: -1;
  pointer-events: none;
  transition: background 0.3s ease;
}

:global(.dark) .heroSection::before {
  background: linear-gradient(to right, rgba(96, 165, 250, 0.08), rgba(196, 181, 253, 0.08));
}

.contactChannel {
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.contactChannel:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.2);
}

:global(.dark) .contactChannel:hover {
  box-shadow: 0 10px 15px -3px rgba(96, 165, 250, 0.25);
}

.formSection {
  position: relative;
  z-index: 1;
}

.formSection::before {
  content: '';
  position: absolute;
  inset: -1rem;
  border-radius: 1rem;
  opacity: 0;
  transition: opacity 0.5s ease;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(168, 85, 247, 0.05));
  z-index: -1;
  pointer-events: none;
}

:global(.dark) .formSection::before {
  background: linear-gradient(to right, rgba(96, 165, 250, 0.08), rgba(196, 181, 253, 0.08));
}

.formSection:focus-within::before {
  opacity: 1;
}

.progressBar {
  transition: all 0.5s ease-out;
}

.attachmentZone {
  transition: all 0.3s ease;
}

.attachmentZone:hover {
  background: linear-gradient(to bottom right, rgb(239 246 255), rgb(250 245 255));
  border-color: rgb(96 165 250);
}

:global(.dark) .attachmentZone:hover {
  background: linear-gradient(to bottom right, rgba(30, 58, 138, 0.25), rgba(88, 28, 135, 0.25));
  border-color: rgb(59 130 246);
}

.priorityChip {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.expectedResponse {
  animation: slide-up 0.3s ease-out;
}

.submitButton {
  transition: all 0.3s ease;
}

.submitButton:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
}

:global(.dark) .submitButton:hover {
  box-shadow: 0 10px 15px -3px rgba(96, 165, 250, 0.35);
}

.submitButton:active {
  transform: scale(0.95);
}

/* Animation keyframes */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .gridLayout {
    gap: 1.5rem;
  }
  
  .channelsColumn {
    gap: 1rem;
  }
  
  .contactChannel {
    padding: 0.75rem;
  }
  
  .formSection {
    padding: 1rem;
  }
  
  .submitButton {
    width: 100%;
  }
  
  .statsContainer {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .statItem {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .supportBadge {
    padding: 0.4rem 0.8rem;
    margin-bottom: 1rem;
  }
  
  .heroTitle {
    font-size: 2rem !important;
    /* Maintain gradient effects on mobile */
    background-size: 150% 150%;
  }
}

/* Additional responsive breakpoints */
@media (max-width: 640px) {
  .gridLayout {
    gap: 1rem;
  }
  
  .pageContainer {
    padding: 1rem 0.5rem;
  }
  
  .statsContainer {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .statItem {
    width: 100%;
    max-width: 16rem;
    justify-content: center;
  }
  
  .supportBadge {
    font-size: 0.8rem;
  }
  
  .heroTitle {
    font-size: 1.75rem !important;
    line-height: 1.2;
    /* Optimize gradient for small screens */
    background-size: 120% 120%;
    letter-spacing: -0.015em;
  }
}

/* Focus styles for accessibility */
.contactChannel:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

:global(.dark) .contactChannel:focus {
  box-shadow: 0 0 0 2px rgb(96 165 250);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .contactChannel {
    border: 2px solid rgb(107 114 128);
  }
  
  .submitButton {
    border: 2px solid rgb(59 130 246);
  }
}

:global(.dark) .contactChannel {
  border: 1px solid rgba(75, 85, 99, 0.3);
}

:global(.dark) .submitButton {
  border-color: rgb(96 165 250);
}

@media (prefers-contrast: high) {
  :global(.dark) .contactChannel {
    border: 2px solid rgb(156 163 175);
  }
  
  :global(.dark) .submitButton {
    border: 2px solid rgb(96 165 250);
  }
}

/* Enhanced Hero Section Components */
.supportBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(59, 130, 246, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

:global(.dark) .supportBadge {
  background: rgba(96, 165, 250, 0.15);
  border-color: rgba(96, 165, 250, 0.3);
  backdrop-filter: blur(8px);
}

.supportIcon {
  width: 1rem;
  height: 1rem;
  color: rgb(37, 99, 235);
  transition: color 0.3s ease;
}

:global(.dark) .supportIcon {
  color: rgb(96, 165, 250);
}

.supportText {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(30, 64, 175);
  transition: color 0.3s ease;
}

:global(.dark) .supportText {
  color: rgb(147, 197, 253);
}

.heroTitle {
  /* Enhanced gradient text styling */
  background: linear-gradient(
    135deg,
    rgb(59, 130, 246) 0%,
    rgb(37, 99, 235) 25%,
    rgb(168, 85, 247) 75%,
    rgb(147, 51, 234) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 4s ease-in-out infinite;
  
  /* Enhanced typography */
  font-weight: 800;
  letter-spacing: -0.025em;
  line-height: 1.1;
  
  /* Subtle glow effect */
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.1));
  
  /* Smooth transitions */
  transition: all 0.3s ease;
  
  /* Text rendering optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:global(.dark) .heroTitle {
  /* Enhanced dark mode gradient */
  background: linear-gradient(
    135deg,
    rgb(96, 165, 250) 0%,
    rgb(59, 130, 246) 25%,
    rgb(196, 181, 253) 75%,
    rgb(168, 85, 247) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  
  /* Enhanced glow for dark mode */
  filter: drop-shadow(0 2px 8px rgba(96, 165, 250, 0.15))
          drop-shadow(0 4px 16px rgba(168, 85, 247, 0.1));
}

/* Gradient animation keyframe */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Hover effect for enhanced interactivity */
.heroTitle:hover {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2))
          drop-shadow(0 2px 4px rgba(168, 85, 247, 0.1));
}

:global(.dark) .heroTitle:hover {
  filter: drop-shadow(0 4px 12px rgba(96, 165, 250, 0.25))
          drop-shadow(0 6px 20px rgba(168, 85, 247, 0.15));
}

.heroDescription {
  transition: color 0.3s ease;
  color: rgb(75, 85, 99);
  line-height: 1.7;
}

:global(.dark) .heroDescription {
  color: rgb(156, 163, 175);
}

.statsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
}

:global(.dark) .statItem {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.3);
}

.statItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:global(.dark) .statItem:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.statIcon {
  width: 1rem;
  height: 1rem;
  transition: all 0.3s ease;
}

.statIconGreen {
  color: rgb(34, 197, 94);
}

:global(.dark) .statIconGreen {
  color: rgb(74, 222, 128);
}

.statIconBlue {
  color: rgb(59, 130, 246);
}

:global(.dark) .statIconBlue {
  color: rgb(96, 165, 250);
}

.statIconYellow {
  color: rgb(245, 158, 11);
}

:global(.dark) .statIconYellow {
  color: rgb(251, 191, 36);
}

.statText {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
  transition: color 0.3s ease;
}

:global(.dark) .statText {
  color: rgb(209, 213, 219);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .contactChannel,
  .formSection,
  .submitButton,
  .supportBadge,
  .heroTitle,
  .heroDescription,
  .statItem,
  .statIcon,
  .statText,
  .supportIcon,
  .supportText {
    transition: none;
  }
  
  .expectedResponse,
  .animate-fade-in,
  .gradient-shift {
    animation: none;
  }
  
  .heroTitle {
    /* Static gradient for reduced motion */
    background: linear-gradient(
      135deg,
      rgb(59, 130, 246) 0%,
      rgb(168, 85, 247) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: none;
  }
  
  :global(.dark) .heroTitle {
    background: linear-gradient(
      135deg,
      rgb(96, 165, 250) 0%,
      rgb(196, 181, 253) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .statItem:hover,
  .heroTitle:hover {
    transform: none;
  }
}