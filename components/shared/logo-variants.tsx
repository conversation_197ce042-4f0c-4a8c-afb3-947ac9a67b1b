"use client";

import { QuickCVLogo, QuickCVLogoMini, QuickCVIcon, QuickCVBrandLogo } from "@/components/logo";
import { IconSvgProps } from "@/types";

interface LogoProps extends IconSvgProps {
  variant?: "full" | "mini" | "icon" | "brand";
  showText?: boolean;
  textClassName?: string;
}

/**
 * Unified Logo component that provides different variants of the QuickCV logo
 * 
 * @param variant - The logo variant to display:
 *   - "full": Complete logo with document and lightning (default)
 *   - "mini": Simplified version for small sizes
 *   - "icon": Lightning bolt only
 *   - "brand": Logo with text for marketing
 * @param showText - Whether to show text (only applies to "brand" variant)
 * @param textClassName - Additional classes for the text
 */
export const LogoVariants: React.FC<LogoProps> = ({
  variant = "full",
  showText = true,
  textClassName = "",
  ...props
}) => {
  switch (variant) {
    case "mini":
      return <QuickCVLogoMini {...props} />;
    case "icon":
      return <QuickCVIcon {...props} />;
    case "brand":
      return (
        <QuickCVBrandLogo 
          {...props} 
          showText={showText} 
          textClassName={textClassName} 
        />
      );
    case "full":
    default:
      return <QuickCVLogo {...props} />;
  }
};

// Export individual components for direct use
export { QuickCVLogo, QuickCVLogoMini, QuickCVIcon, QuickCVBrandLogo };

// Export default for common usage
export default LogoVariants;