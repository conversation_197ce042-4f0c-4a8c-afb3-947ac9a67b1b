# Features & Systems

This directory contains documentation for all features and systems in QuickCV.

## Files

- **[PAYMENT_SYSTEM.md](PAYMENT_SYSTEM.md)** - Payment integration and premium features
- **[AI_FEATURE_DOCUMENTATION.md](AI_FEATURE_DOCUMENTATION.md)** - AI-powered content generation system
- **[linkedin-import-guide.md](linkedin-import-guide.md)** - LinkedIn profile import functionality
- **[resume-import-system.md](resume-import-system.md)** - Resume data import and processing
- **[email-sharing-and-tokens-system.md](email-sharing-and-tokens-system.md)** - Email sharing and secure token management

## Feature Categories

### 🎯 Core Features
- Resume creation and editing
- Template system with 12 professional designs
- Real-time preview and customization

### 💎 Premium Features
- PDF export and download
- Resume sharing with public links
- Website publishing with custom domains
- Unlimited AI content generation

### 🤖 AI Integration
- Content generation for all resume sections
- Multiple tone and length options
- Free trial (1 use) for non-premium users

### 📤 Import/Export
- LinkedIn profile import
- Resume data import from various formats
- Email sharing with secure tokens

### 💳 Payment System
- Lifetime premium subscription model
- One-time payment for all features
- Secure payment processing with Paddle

## Feature Flag System

QuickCV uses a comprehensive feature flag system to manage premium vs. free features:

- **Exploration Mode**: Full access to creation and editing
- **Premium Gates**: PDF export, sharing, and publishing require premium
- **AI Limits**: 1 free generation, unlimited for premium users

See individual feature documentation for implementation details.