import React from 'react';
import { FullResume } from '@/db/schema';
import SkillsDisplay from '@/components/resume/skills-display';

interface KakunaTemplateProps {
  resume: FullResume;
}

export default function KakunaTemplate({ resume }: KakunaTemplateProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-600 border-blue-600',
    green: 'text-green-600 bg-green-600 border-green-600',
    purple: 'text-purple-600 bg-purple-600 border-purple-600',
    red: 'text-red-600 bg-red-600 border-red-600',
    orange: 'text-orange-600 bg-orange-600 border-orange-600',
    teal: 'text-teal-600 bg-teal-600 border-teal-600',
    pink: 'text-pink-600 bg-pink-600 border-pink-600',
    indigo: 'text-indigo-600 bg-indigo-600 border-indigo-600',
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div className={`max-w-4xl mx-auto bg-white shadow-sm font-${resume.fontFamily || 'inter'} text-gray-900 print:shadow-none`}>
      {/* Clean centered header */}
      <header className="text-center px-8 py-10 border-b border-gray-200">
        {/* Photo - Clean and centered */}
        {resume.showPhoto && resume.photo && (
          <div className="mb-6">
            <img
              src={resume.photo}
              alt={`${resume.firstName} ${resume.lastName}`}
              className="w-32 h-32 rounded-full object-cover mx-auto border-3 border-gray-300 shadow-lg"
            />
          </div>
        )}
        
        {/* Name - Clean and prominent */}
        <h1 className="text-4xl font-bold text-gray-900 mb-3 leading-tight">
          {resume.firstName} {resume.lastName}
        </h1>
        
        {/* Job Title - Clear accent */}
        <h2 className={`text-xl font-semibold ${selectedColor.split(' ')[0]} mb-6 leading-relaxed`}>
          {resume.jobTitle}
        </h2>
        
        {/* Contact Information - Clean horizontal layout */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm font-medium text-gray-700 mb-6">
          {resume.email && (
            <span>{resume.email}</span>
          )}
          {resume.phone && (
            <span>{resume.phone}</span>
          )}
          {resume.website && (
            <span>{resume.website}</span>
          )}
          {(resume.city || resume.country) && (
            <span>
              {[resume.city, resume.country].filter(Boolean).join(', ')}
            </span>
          )}
        </div>
        
        {/* Bio - Centered and readable */}
        {resume.bio && (
          <div className="max-w-3xl mx-auto">
            <p className="text-base text-gray-700 leading-relaxed">
              {resume.bio}
            </p>
          </div>
        )}
      </header>

      {/* Content - Single column, well-structured */}
      <div className="px-8 py-8">
        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-12">
            <h3 className={`text-2xl font-bold ${selectedColor.split(' ')[0]} mb-8 pb-3 border-b-2 ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
              Professional Experience
            </h3>
            <div className="space-y-10">
              {resume.experiences.map((experience) => (
                <div key={experience.id} className="relative">
                  {/* Job Title - Most prominent */}
                  <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight text-center">
                    {experience.title}
                  </h4>
                  
                  {/* Company and Date - Clean layout */}
                  <div className="text-center mb-4">
                    <h5 className={`text-lg font-semibold ${selectedColor.split(' ')[0]} leading-relaxed mb-1`}>
                      {experience.company}
                    </h5>
                    <div className="flex justify-center items-center gap-4 text-sm text-gray-600">
                      <span className="font-medium">
                        {experience.startDate} - {experience.isCurrent ? 'Present' : experience.endDate}
                      </span>
                      {(experience.city || experience.country) && (
                        <span>
                          {[experience.city, experience.country].filter(Boolean).join(', ')}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Description - Well-formatted */}
                  {experience.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none text-center"
                      dangerouslySetInnerHTML={{ __html: experience.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-12">
            <h3 className={`text-2xl font-bold ${selectedColor.split(' ')[0]} mb-8 pb-3 border-b-2 ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
              Education
            </h3>
            <div className="space-y-8">
              {resume.educations.map((education) => (
                <div key={education.id} className="text-center">
                  {/* Degree - Prominent */}
                  <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">
                    {education.degree}
                  </h4>
                  
                  {/* Field of Study */}
                  {education.fieldOfStudy && (
                    <p className="text-lg font-semibold text-gray-700 mb-2">
                      {education.fieldOfStudy}
                    </p>
                  )}
                  
                  {/* Institution */}
                  <h5 className={`text-base font-semibold ${selectedColor.split(' ')[0]} mb-2`}>
                    {education.institution}
                  </h5>
                  
                  {/* Date and Location */}
                  <div className="text-sm text-gray-600 space-y-1">
                    <p className="font-medium">
                      {education.startDate} - {education.isCurrent ? 'Present' : education.endDate}
                    </p>
                    {(education.city || education.country) && (
                      <p>{[education.city, education.country].filter(Boolean).join(', ')}</p>
                    )}
                  </div>
                  
                  {/* Description */}
                  {education.description && (
                    <div
                      className="text-sm text-gray-700 leading-relaxed mt-3"
                      dangerouslySetInnerHTML={{ __html: education.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section - Grid layout */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-12">
            <h3 className={`text-2xl font-bold ${selectedColor.split(' ')[0]} mb-8 pb-3 border-b-2 ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
              Skills
            </h3>
            <SkillsDisplay
              skills={resume.skills}
              colorScheme={resume.colorScheme}
              variant="badges"
              groupByCategory={true}
              maxColumns={3}
              showProficiency={true}
            />
          </section>
        )}

        {/* Additional sections in grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-2 border-b ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
                Projects
              </h3>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div key={project.id} className="text-center">
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">
                      {project.title}
                    </h4>
                    
                    {/* Client and Date */}
                    <div className="mb-3">
                      {project.client && (
                        <h5 className={`text-base font-semibold ${selectedColor.split(' ')[0]} mb-1`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm font-medium text-gray-700">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>
                    
                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a href={project.url} className={`${selectedColor.split(' ')[0]} hover:underline font-medium`}>
                          {project.url}
                        </a>
                      </p>
                    )}
                    
                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-2 border-b ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
                Languages
              </h3>
              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className="text-center bg-gray-50 p-3 rounded-lg">
                    <div className="mb-2">
                      <span className="text-base font-semibold text-gray-800">{language.name}</span>
                      <span className="block text-sm text-gray-600 mt-1">{language.proficiency}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${selectedColor.split(' ')[1]}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Final row for certifications and awards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-8">
          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-2 border-b ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
                Certifications
              </h3>
              <div className="space-y-4">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="text-center bg-gray-50 p-3 rounded-lg">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">
                      {cert.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-medium">{cert.issuer}</p>
                    {cert.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(' ')[0]} mb-6 pb-2 border-b ${selectedColor.split(' ')[2]} text-center uppercase tracking-wide`}>
                Awards
              </h3>
              <div className="space-y-4">
                {resume.awards.map((award) => (
                  <div key={award.id} className="text-center bg-gray-50 p-3 rounded-lg">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">
                      {award.title}
                    </h4>
                    <p className="text-xs text-gray-600 font-medium">{award.issuer}</p>
                    {award.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1">{award.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}