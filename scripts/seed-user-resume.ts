#!/usr/bin/env bun
import { eq } from "drizzle-orm";
import { db } from "@/db";
import {
  awards,
  certifications,
  educations,
  experiences,
  hobbies,
  languages,
  profiles,
  projects,
  references,
  resumes,
  skills,
  users,
  volunteerings,
} from "@/db/schema";
import { createSampleResumeData } from "@/lib/sample-resume-data";

async function main() {
  console.log("🌱 Starting resume seeding...");

  // For CLI usage, we need to either:
  // 1. Get user ID from command line args, or
  // 2. Create a test user, or
  // 3. Use the first user in the database

  const userEmail = process.argv[2]; // Optional: get user email from command line

  let user: any;

  if (userEmail) {
    console.log(`👤 Looking for user with email: ${userEmail}`);
    const existingUsers = await db.select().from(users).where(eq(users.emailAddress, userEmail)).limit(1);
    console.log(existingUsers);
    if (existingUsers.length === 0) {
      console.error(`❌ No user found with email: ${userEmail}`);
      process.exit(1);
    }
    user = existingUsers[0];
  }

  console.log(`📝 Creating resume for user: ${user.emailAddress}`);

  try {
    // Get sample resume data
    const sampleData = createSampleResumeData();

    // Create the main resume record
    const [newResume] = await db
      .insert(resumes)
      .values({
        title: sampleData.title,
        firstName: sampleData.firstName,
        lastName: sampleData.lastName,
        jobTitle: sampleData.jobTitle,
        address: sampleData.address,
        phone: sampleData.phone,
        email: sampleData.email,
        website: sampleData.website,
        bio: sampleData.bio,
        birthDate: sampleData.birthDate,
        city: sampleData.city,
        street: sampleData.street,
        country: sampleData.country,
        showPhoto: sampleData.showPhoto,
        photo: sampleData.photo,
        templateId: sampleData.templateId,
        colorScheme: sampleData.colorScheme,
        fontFamily: sampleData.fontFamily,
        spacing: sampleData.spacing,
        margins: sampleData.margins,
        thumbnail: sampleData.thumbnail,
        userId: user.clerkId,
      })
      .returning();

    console.log(`✅ Created resume with ID: ${newResume.id}`);

    // Seed related data with the new resume ID
    const resumeId = newResume.id;

    // Insert educations
    if (sampleData.educations.length > 0) {
      await db.insert(educations).values(
        sampleData.educations.map((edu) => ({
          city: edu.city,
          country: edu.country,
          institution: edu.institution,
          degree: edu.degree,
          fieldOfStudy: edu.fieldOfStudy,
          website: edu.website,
          isCurrent: edu.isCurrent,
          startDate: edu.startDate,
          endDate: edu.endDate,
          description: edu.description,
          sort: edu.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`📚 Seeded ${sampleData.educations.length} education records`);
    }

    // Insert experiences
    if (sampleData.experiences.length > 0) {
      await db.insert(experiences).values(
        sampleData.experiences.map((exp) => ({
          title: exp.title,
          company: exp.company,
          startDate: exp.startDate,
          endDate: exp.endDate,
          description: exp.description,
          city: exp.city,
          country: exp.country,
          isCurrent: exp.isCurrent,
          sort: exp.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`💼 Seeded ${sampleData.experiences.length} experience records`);
    }

    // Insert projects
    if (sampleData.projects.length > 0) {
      await db.insert(projects).values(
        sampleData.projects.map((proj) => ({
          title: proj.title,
          client: proj.client,
          startDate: proj.startDate,
          endDate: proj.endDate,
          description: proj.description,
          url: proj.url,
          sort: proj.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🚀 Seeded ${sampleData.projects.length} project records`);
    }

    // Insert skills
    if (sampleData.skills.length > 0) {
      await db.insert(skills).values(
        sampleData.skills.map((skill) => ({
          name: skill.name,
          proficiency: skill.proficiency,
          category: skill.category,
          sort: skill.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🛠️ Seeded ${sampleData.skills.length} skill records`);
    }

    // Insert certifications
    if (sampleData.certifications.length > 0) {
      await db.insert(certifications).values(
        sampleData.certifications.map((cert) => ({
          title: cert.title,
          issuer: cert.issuer,
          dateReceived: cert.dateReceived,
          description: cert.description,
          url: cert.url,
          sort: cert.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🏆 Seeded ${sampleData.certifications.length} certification records`);
    }

    // Insert awards
    if (sampleData.awards.length > 0) {
      await db.insert(awards).values(
        sampleData.awards.map((award) => ({
          title: award.title,
          issuer: award.issuer,
          dateReceived: award.dateReceived,
          description: award.description,
          url: award.url,
          sort: award.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🥇 Seeded ${sampleData.awards.length} award records`);
    }

    // Insert languages
    if (sampleData.languages.length > 0) {
      await db.insert(languages).values(
        sampleData.languages.map((lang) => ({
          name: lang.name,
          proficiency: lang.proficiency,
          sort: lang.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🌐 Seeded ${sampleData.languages.length} language records`);
    }

    // Insert references
    if (sampleData.references.length > 0) {
      await db.insert(references).values(
        sampleData.references.map((ref) => ({
          name: ref.name,
          company: ref.company,
          position: ref.position,
          email: ref.email,
          phone: ref.phone,
          description: ref.description,
          sort: ref.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`👥 Seeded ${sampleData.references.length} reference records`);
    }

    // Insert hobbies
    if (sampleData.hobbies.length > 0) {
      await db.insert(hobbies).values(
        sampleData.hobbies.map((hobby) => ({
          name: hobby.name,
          description: hobby.description,
          sort: hobby.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🎯 Seeded ${sampleData.hobbies.length} hobby records`);
    }

    // Insert volunteering
    if (sampleData.volunteerings.length > 0) {
      await db.insert(volunteerings).values(
        sampleData.volunteerings.map((vol) => ({
          organization: vol.organization,
          role: vol.role,
          startDate: vol.startDate,
          endDate: vol.endDate,
          description: vol.description,
          sort: vol.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`❤️ Seeded ${sampleData.volunteerings.length} volunteering records`);
    }

    // Insert profiles
    if (sampleData.profiles.length > 0) {
      await db.insert(profiles).values(
        sampleData.profiles.map((profile) => ({
          url: profile.url,
          username: profile.username,
          network: profile.network,
          icon: profile.icon,
          sort: profile.sort,
          resumeId: resumeId,
        })),
      );
      console.log(`🔗 Seeded ${sampleData.profiles.length} profile records`);
    }

    console.log("✨ Resume seeding completed successfully!");
    console.log(`🎉 Resume "${sampleData.title}" created for ${user.emailAddress}`);
  } catch (error) {
    console.error("❌ Error during resume seeding:", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("🏁 Script finished successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Script failed:", error);
    process.exit(1);
  });
