# Multi-stage Dockerfile for Next.js + Prisma production deployment
FROM node:20-alpine AS base

# Set Prisma binary target for Alpine Linux
ENV PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x"

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat curl
WORKDIR /app

# Copy Prisma schema first for postinstall script
COPY db ./db
COPY drizzle.config.ts ./drizzle.config.ts

# Copy Biome configuration for linting during build
COPY biome.json ./

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Drizzle migrations
RUN bun run db:generate

# Build Next.js app
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
RUN apk add --no-cache curl
WORKDIR /app

ENV NODE_ENV production

# Create nextjs user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy necessary files
COPY --from=builder /app/public ./public

# Set the correct permission for standalone mode
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy Drizzle files for runtime
COPY --from=builder /app/db ./db
COPY --from=builder /app/drizzle ./drizzle
COPY --from=builder /app/drizzle.config.ts ./drizzle.config.ts

# Create data directory for SQLite
RUN mkdir -p /app/data && chown nextjs:nodejs /app/data

# Copy startup script
COPY start.sh ./start.sh
RUN chmod +x ./start.sh && chown nextjs:nodejs ./start.sh

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Allow dynamic port configuration
ARG PORT=3000
ENV PORT=${PORT}

# Health check - disabled temporarily for debugging
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["./start.sh"]
