"use client";

import { trpc } from "@/app/_trpc/client";
import { WebsiteBuilder } from "@/components/features/website/WebsiteBuilder";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import SpinnerComponent from "@/components/shared/common/spinner";
import { Website } from "@/db/schema";

export function WebsiteBuilderPage({ website }: { website: Website }) {
  const getResumes = trpc.resumes.getResumes.useQuery();

  const { isLoading: isLoadingResumes, data: resumes = [] } = getResumes;

  // Get website templates using TRPC if not provided via SSR
  const getTemplates = trpc.websites.getWebsiteTemplates.useQuery();

  const { isLoading: isLoadingTemplates, data: templates = [] } = getTemplates;

  // Show loading spinner while fetching data (only if no SSR data provided)
  if (isLoadingResumes || isLoadingTemplates) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-8 px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Website Builder</h1>
            <p className="text-gray-600">Create a professional website from your resume</p>
          </div>
          <div className="flex justify-center">
            <LoaderComponent />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {website.resumeId ? "Website Builder" : "Create Your Website"}
          </h1>
          <p className="text-gray-600">
            {website.resumeId
              ? "Create a professional website from your resume"
              : "Build your professional website to showcase your resume online"}
          </p>
        </div>

        <WebsiteBuilder resumes={resumes} website={website} templates={templates} />
      </div>
    </div>
  );
}
