import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { requireAuth } from "@/lib/auth-clerk";

const f = createUploadthing();

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Define as many FileRoutes as you like, each with a unique routeSlug
  imageUploader: f({ image: { maxFileSize: "4MB" } })
    // Set permissions and file types for this FileRoute
    .middleware(async () => {
      // This code runs on your server before upload
      const user = await requireAuth();

      // If you throw, the user will not be able to upload
      if (!user) throw new UploadThingError("Unauthorized");

      // Whatever is returned here is accessible in onUploadComplete as `metadata`
      return { userId: user.clerkId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      // File upload completed successfully
      return { uploadedBy: metadata.userId, url: file.url };
    }),

  // Resume photo uploader with specific constraints
  resumePhotoUploader: f({ image: { maxFileSize: "4MB" } })
    .middleware(async () => {
      const user = await requireAuth();
      if (!user) throw new UploadThingError("Unauthorized");

      return { userId: user.clerkId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // Resume photo upload completed successfully
      return { uploadedBy: metadata.userId, url: file.url };
    }),

  // Resume thumbnail uploader for generated thumbnails
  resumeThumbnailUploader: f({ image: { maxFileSize: "1MB" } })
    .middleware(async () => {
      const user = await requireAuth();
      if (!user) throw new UploadThingError("Unauthorized");

      return { userId: user.clerkId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // Resume thumbnail upload completed successfully
      return { uploadedBy: metadata.userId, url: file.url };
    }),

  // Contact form attachments uploader
  contactAttachmentsUploader: f({
    image: { maxFileSize: "8MB" },
    pdf: { maxFileSize: "8MB" },
    text: { maxFileSize: "8MB" },
    "application/msword": { maxFileSize: "8MB" },
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": { maxFileSize: "8MB" },
  })
    .middleware(async () => {
      // Contact form doesn't require authentication, but we can optionally track user if available
      try {
        const user = await requireAuth();
        return { userId: user.clerkId };
      } catch {
        // Allow anonymous uploads for contact form
        return { userId: null };
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // Contact attachment upload completed successfully
      return { uploadedBy: metadata.userId, url: file.url };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
