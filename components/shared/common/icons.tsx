import * as React from "react";

import { IconSvgProps } from "@/types";

export const Logo: React.FC<IconSvgProps> = ({ size = 36, width, height, ...props }) => (
  <svg fill="none" height={size || height} viewBox="0 0 32 32" width={size || width} {...props}>
    {/* Document background */}
    <rect
      x="6"
      y="4"
      width="16"
      height="20"
      rx="2"
      ry="2"
      fill="currentColor"
      fillOpacity="0.1"
      stroke="currentColor"
      strokeWidth="1.5"
    />
    
    {/* Document header/title bar */}
    <rect
      x="8"
      y="7"
      width="8"
      height="1.5"
      rx="0.75"
      fill="currentColor"
      fillOpacity="0.8"
    />
    
    {/* Document lines representing text */}
    <rect
      x="8"
      y="10"
      width="10"
      height="1"
      rx="0.5"
      fill="currentColor"
      fillOpacity="0.6"
    />
    <rect
      x="8"
      y="12.5"
      width="12"
      height="1"
      rx="0.5"
      fill="currentColor"
      fillOpacity="0.6"
    />
    <rect
      x="8"
      y="15"
      width="9"
      height="1"
      rx="0.5"
      fill="currentColor"
      fillOpacity="0.6"
    />
    <rect
      x="8"
      y="17.5"
      width="11"
      height="1"
      rx="0.5"
      fill="currentColor"
      fillOpacity="0.6"
    />
    
    {/* Speed/Lightning element - representing "Quick" */}
    <path
      d="M24 8L20 14h3l-4 8 6-10h-3l2-4z"
      fill="currentColor"
      fillOpacity="0.9"
    />
    
    {/* Decorative corner fold */}
    <path
      d="M19 4v3h3L19 4z"
      fill="currentColor"
      fillOpacity="0.3"
    />
  </svg>
);

export const MoonFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
  <svg
    aria-hidden="true"
    focusable="false"
    height={size || height}
    role="presentation"
    viewBox="0 0 24 24"
    width={size || width}
    {...props}
  >
    <path
      d="M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z"
      fill="currentColor"
    />
  </svg>
);

export const SunFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
  <svg
    aria-hidden="true"
    focusable="false"
    height={size || height}
    role="presentation"
    viewBox="0 0 24 24"
    width={size || width}
    {...props}
  >
    <g fill="currentColor">
      <path d="M19 12a7 7 0 11-7-7 7 7 0 017 7z" />
      <path d="M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z" />
    </g>
  </svg>
);
