/* Inter Font Family */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("./inter-normal-400.woff2") format("woff2");
}

@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("./inter-normal-500.woff2") format("woff2");
}

@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("./inter-normal-600.woff2") format("woff2");
}

@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("./inter-normal-700.woff2") format("woff2");
}

/* Amiri Font Family */
@font-face {
  font-family: "Amiri";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("./amiri-italic-400.ttf") format("truetype");
}

@font-face {
  font-family: "Amiri";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("./amiri-italic-700.ttf") format("truetype");
}

@font-face {
  font-family: "Amiri";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("./amiri-normal-400.ttf") format("truetype");
}

@font-face {
  font-family: "Amiri";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("./amiri-normal-700.ttf") format("truetype");
}

/* Cairo Font Family */
@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("./cairo-normal-200.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("./cairo-normal-300.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("./cairo-normal-400.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("./cairo-normal-500.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("./cairo-normal-600.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("./cairo-normal-700.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("./cairo-normal-800.ttf") format("truetype");
}

@font-face {
  font-family: "Cairo";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("./cairo-normal-900.ttf") format("truetype");
}

/* Noto Sans Arabic Font Family */
@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 100;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-100.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 200;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-200.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 300;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-300.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-400.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 500;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-500.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-600.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-700.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 800;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-800.ttf") format("truetype");
}

@font-face {
  font-family: "Noto Sans Arabic";
  font-style: normal;
  font-weight: 900;
  font-stretch: normal;
  font-display: swap;
  src: url("./noto-sans-arabic-normal-900.ttf") format("truetype");
}
