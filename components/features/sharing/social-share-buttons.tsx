"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody, Input, Spacer } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { SocialMediaSharing } from "@/lib/social-sharing";

interface SocialShareButtonsProps {
  resumeId: number;
}

export function SocialShareButtons({ resumeId }: SocialShareButtonsProps) {
  const [customMessage, setCustomMessage] = useState("");
  const [shareUrls, setShareUrls] = useState<{
    linkedin: string;
    twitter: string;
    facebook: string;
    whatsapp: string;
    email: string;
    resumeUrl: string;
  } | null>(null);

  // Generate social share URLs mutation
  const generateUrlsMutation = trpc.sharing.generateSocialShareUrls.useMutation({
    onSuccess: (data) => {
      setShareUrls(data);
    },
    onError: (error) => {
      toast.error("Failed to generate sharing URLs. Please try again.");
      console.error("URL generation error:", error);
    },
  });

  const handleGenerateUrls = () => {
    generateUrlsMutation.mutate({
      resumeId,
      customMessage: customMessage || undefined,
    });
  };

  const handleShare = (platform: string, url: string) => {
    window.open(url, "_blank", "width=600,height=400");
    toast.success(`Opened ${platform} for sharing`);
  };

  const handleCopyLink = async () => {
    if (!shareUrls) return;

    try {
      await SocialMediaSharing.copyToClipboard(shareUrls.resumeUrl);
      toast.success("Link copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy link. Please try again.");
    }
  };

  const socialPlatforms = [
    {
      name: "LinkedIn",
      icon: "tabler:brand-linkedin",
      color: "bg-[#0077B5] hover:bg-[#005885]",
      url: shareUrls?.linkedin,
    },
    {
      name: "Twitter",
      icon: "tabler:brand-twitter",
      color: "bg-[#1DA1F2] hover:bg-[#0d8bd9]",
      url: shareUrls?.twitter,
    },
    {
      name: "Facebook",
      icon: "tabler:brand-facebook",
      color: "bg-[#1877F2] hover:bg-[#166fe5]",
      url: shareUrls?.facebook,
    },
    {
      name: "WhatsApp",
      icon: "tabler:brand-whatsapp",
      color: "bg-[#25D366] hover:bg-[#1ebf5a]",
      url: shareUrls?.whatsapp,
    },
  ];

  return (
    <Card>
      <CardBody className="space-y-4">
        <div className="flex items-center gap-2">
          <Icon icon="tabler:share" className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Social Sharing</h3>
        </div>

        <p className="text-sm text-default-600">Share your resume on social media platforms to increase visibility.</p>

        <div className="space-y-4">
          <Input
            label="Custom Message"
            placeholder="Add a personal message to your share"
            value={customMessage}
            onValueChange={setCustomMessage}
            startContent={<Icon icon="tabler:message" className="w-4 h-4" />}
            description="This message will be included when sharing your resume"
          />

          <Button
            onPress={handleGenerateUrls}
            isLoading={generateUrlsMutation.isPending}
            color="primary"
            startContent={<Icon icon="tabler:link" className="w-4 h-4" />}
            className="w-full"
          >
            {shareUrls ? "Regenerate Links" : "Generate Share Links"}
          </Button>
        </div>

        {shareUrls && (
          <>
            <Spacer y={2} />

            {/* Social Platform Buttons */}
            <div className="space-y-3">
              <h4 className="text-md font-medium">Share on Platforms</h4>
              <div className="grid grid-cols-2 gap-3">
                {socialPlatforms.map((platform) => (
                  <Button
                    key={platform.name}
                    onPress={() => platform.url && handleShare(platform.name, platform.url)}
                    disabled={!platform.url}
                    className={`text-white ${platform.color}`}
                    startContent={<Icon icon={platform.icon} className="w-4 h-4" />}
                  >
                    {platform.name}
                  </Button>
                ))}
              </div>
            </div>

            <Spacer y={2} />

            {/* Resume URL Display */}
            <div className="space-y-3">
              <h4 className="text-md font-medium">Shareable Link</h4>
              <div className="p-3 bg-default-100 rounded-lg">
                <div className="flex items-center justify-between gap-3">
                  <p className="text-xs text-primary break-all font-mono flex-1">{shareUrls.resumeUrl}</p>
                  <Button
                    onPress={handleCopyLink}
                    size="sm"
                    variant="flat"
                    startContent={<Icon icon="tabler:copy" className="w-3 h-3" />}
                    className="shrink-0"
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}
      </CardBody>
    </Card>
  );
}
