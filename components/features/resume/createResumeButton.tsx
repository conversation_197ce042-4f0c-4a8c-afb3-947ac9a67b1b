"use client";

import { trpc } from "@/app/_trpc/client";
import { Button, Dropdown, DropdownTrigger, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useUpgradeModal } from "@/contexts/upgrade-modal-context";

interface CreateResumeButtonProps {
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

export default function CreateResumeButton({
  size = "lg",
  text = "Create Resume",
  className = "",
}: CreateResumeButtonProps) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const { showUpgradeModal } = useUpgradeModal();

  const { data: plan, isLoading: isPlanLoading } = trpc.users.getPlan.useQuery();

  const handleCreateNew = () => {
    if (!isLoaded || isPlanLoading) return;

    if (!user) {
      router.push("/sign-in");
      return;
    }

    if (plan?.isFree && plan?.resumeCount >= 1) {
      showUpgradeModal();
      return;
    }

    // Logic to open the create resume modal
  };

  const handleImportLinkedIn = () => {
    if (!isLoaded || isPlanLoading) return;

    if (!user) {
      router.push("/sign-in");
      return;
    }

    if (plan?.isFree && plan?.resumeCount >= 1) {
      showUpgradeModal();
      return;
    }

    // Logic to open the import LinkedIn modal
  };

  const isButtonDisabled = !isLoaded || isPlanLoading;

  const button = (
    <Button
      className={className}
      color="primary"
      radius="full"
      size={size}
      variant="shadow"
      endContent={<Icon icon="lucide:chevron-down" className="w-4 h-4" />}
      isLoading={!isLoaded || isPlanLoading}
      isDisabled={isButtonDisabled}
      onClick={handleCreateNew}
    >
      {text || "Get Started"}
    </Button>
  );

  return (
    <>
      <Dropdown>
        <DropdownTrigger>
          {plan?.isFree && plan?.resumeCount >= 1 ? (
            <Tooltip content="Upgrade to create more resumes" placement="top">
              {button}
            </Tooltip>
          ) : (
            button
          )}
        </DropdownTrigger>
        {/* ... (DropdownMenu with handleImportLinkedIn) */}
      </Dropdown>
      {/* ... (Modal) */}
    </>
  );
}