{"name": "quickcv", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:production": "npm run db:migrate && npm run db:seed && next build", "start": "next start -p ${PORT:-3000}", "lint": "biome check --write .", "lint:check": "biome check .", "format": "biome format --write .", "format:check": "biome format .", "clean": "./scripts/clean-dev.sh", "dev:clean": "./scripts/clean-dev.sh && bun run dev", "dev:local": "npm run db:local & npm run dev", "knip": "knip", "db:local": "$HOME/.turso/turso dev --db-file local-db.db", "db:generate": "drizzle-kit generate", "db:migrate": "bun run db/migrate.ts", "db:seed": "bun run db/seed.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@bprogress/next": "^3.2.12", "@clerk/localizations": "^3.20.5", "@clerk/nextjs": "^6.27.1", "@heroui/react": "^2.8.1", "@hugocxl/react-to-image": "^0.0.9", "@libsql/client": "^0.15.10", "@paddle/paddle-js": "^1.4.2", "@paralleldrive/cuid2": "^2.2.2", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.23", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/jszip": "^3.4.1", "@types/pg": "^8.15.4", "@types/react-dropzone": "^5.1.0", "@uploadthing/react": "^7.3.2", "async-retry": "^1.3.3", "babel-plugin-react-compiler": "^19.1.0-rc.2", "clsx": "2.1.1", "dompurify": "^3.2.6", "drizzle-orm": "^0.44.3", "framer-motion": "11.13.1", "jose": "^6.0.12", "jszip": "^3.10.1", "next": "15.3.3", "next-themes": "0.4.6", "pdf-lib": "^1.17.1", "pg": "^8.16.3", "posthog-js": "^1.258.5", "puppeteer": "^24.15.0", "react": "18.3.1", "react-autosave": "^0.5.0", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-to-print": "^3.1.1", "react-use": "^17.6.0", "recharts": "^2.15.4", "styled-jsx": "^5.1.7", "svix": "^1.69.0", "uploadthing": "^7.7.3", "usehooks-ts": "^3.1.1", "zod": "^4.0.10", "zustand": "^5.0.7"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@iconify/react": "^6.0.0", "@react-types/shared": "3.29.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.21", "drizzle-kit": "^0.31.4", "knip": "^5.62.0", "postcss": "^8.5.6", "tailwind-variants": "0.3.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "turso": "^0.1.0", "typescript": "5.7.2"}}