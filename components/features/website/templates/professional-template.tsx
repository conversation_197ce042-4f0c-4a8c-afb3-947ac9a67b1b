import Image from "next/image";
import React from "react";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";
import { WebsiteTemplateProps } from "@/components/shared/template-system/types";
import { WebsiteSection } from "./content";
import { WebsiteFooter } from "./layout";

export const ProfessionalTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");
  const displayTitle = resume.jobTitle;
  const displayBio = resume.bio;

  return (
    <div
      className={`professional-template min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50 ${className}`}
    >
      {/* Header Section */}
      <header className="relative bg-gradient-to-r from-slate-900 via-gray-900 to-slate-800 text-white py-16 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 end-10 w-40 h-40 bg-blue-500 rounded-full mix-blend-multiply animate-pulse"></div>
          <div className="absolute bottom-10 start-10 w-32 h-32 bg-indigo-500 rounded-full mix-blend-multiply animate-bounce"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-8 md:space-y-0 md:space-x-12">
            {/* Photo */}
            {resume.showPhoto && resume.photo ? (
              <div className="flex-shrink-0">
                <div className="relative">
                  <Image
                    alt={fullName}
                    className="w-40 h-40 rounded-2xl object-cover shadow-2xl ring-4 ring-white/20"
                    height={160}
                    src={resume.photo}
                    width={160}
                  />
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-gray-900/20 to-transparent"></div>
                </div>
              </div>
            ) : null}

            {/* Info */}
            <div className="text-center md:text-start flex-1">
              <h1 className="text-5xl md:text-6xl font-bold mb-4 tracking-tight">{fullName}</h1>
              <p className="text-2xl md:text-3xl text-blue-300 mb-8 font-light">{displayTitle}</p>

              {/* Contact Info */}
              <div className="flex flex-col sm:flex-row sm:flex-wrap gap-6 text-gray-300">
                {resume.email && (
                  <div className="flex items-center justify-center md:justify-start group">
                    <div className="w-10 h-10 bg-blue-600/20 rounded-full flex items-center justify-center me-3 group-hover:bg-blue-600/30 transition-colors">
                      <span className="text-lg">✉️</span>
                    </div>
                    <a href={`mailto:${resume.email}`} className="hover:text-white transition-colors font-medium">
                      {resume.email}
                    </a>
                  </div>
                )}
                {resume.website && (
                  <div className="flex items-center justify-center md:justify-start group">
                    <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center me-3 group-hover:bg-green-600/30 transition-colors">
                      <span className="text-lg">🌐</span>
                    </div>
                    <a
                      href={resume.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-white transition-colors font-medium"
                    >
                      {resume.website}
                    </a>
                  </div>
                )}
                {location && (
                  <div className="flex items-center justify-center md:justify-start group">
                    <div className="w-10 h-10 bg-purple-600/20 rounded-full flex items-center justify-center me-3">
                      <span className="text-lg">📍</span>
                    </div>
                    <span className="font-medium">{location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-5xl mx-auto px-6 py-16">
        {/* Professional Summary */}
        {displayBio && (
          <WebsiteSection title="About" className="mb-16">
            <div className="bg-gradient-to-r from-slate-50 to-blue-50 p-8 rounded-2xl border-s-4 border-slate-800 shadow-lg">
              <div dangerouslySetInnerHTML={{ __html: displayBio }} className="text-gray-700 leading-relaxed text-lg" />
            </div>
          </WebsiteSection>
        )}

        {/* Two Column Layout */}
        <div className="grid lg:grid-cols-3 gap-16">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-16">
            {/* Experience */}
            {resume.experiences && resume.experiences.length > 0 && (
              <WebsiteSection title="Experience">
                <div className="space-y-8">
                  {resume.experiences.map((experience) => (
                    <div
                      key={experience.id}
                      className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-4">
                        <div className="flex-1">
                          <h3 className="text-2xl font-bold text-gray-900 mb-2">{experience.title}</h3>
                          <p className="text-xl text-slate-700 font-semibold mb-2">{experience.company}</p>
                          {(experience.city || experience.country) && (
                            <p className="text-gray-600 flex items-center gap-2">
                              <span className="text-slate-500">📍</span>
                              <span>{formatLocation(experience.city || "", experience.country || "")}</span>
                            </p>
                          )}
                        </div>
                        <div className="mt-4 md:mt-0">
                          <div className="bg-slate-100 px-4 py-2 rounded-xl">
                            <p className="font-semibold text-slate-700">
                              {experience.startDate && experience.endDate
                                ? `${new Date(experience.startDate).getFullYear()} - ${
                                    experience.isCurrent ? "Present" : new Date(experience.endDate).getFullYear()
                                  }`
                                : ""}
                            </p>
                          </div>
                        </div>
                      </div>
                      {experience.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: experience.description,
                          }}
                          className="text-gray-700 leading-relaxed prose prose-lg max-w-none"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </WebsiteSection>
            )}

            {/* Projects */}
            {resume.projects && resume.projects.length > 0 && (
              <WebsiteSection title="Projects">
                <div className="grid gap-6">
                  {resume.projects.map((project) => (
                    <div
                      key={project.id}
                      className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="mb-4">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{project.title}</h3>
                        {project.client && (
                          <p className="text-lg text-slate-700 font-semibold mb-2">{project.client}</p>
                        )}
                        {(project.startDate || project.endDate) && (
                          <p className="text-gray-600 text-sm">
                            {project.startDate && project.endDate
                              ? `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                              : project.startDate
                                ? new Date(project.startDate).getFullYear()
                                : project.endDate
                                  ? new Date(project.endDate).getFullYear()
                                  : ""}
                          </p>
                        )}
                      </div>
                      {project.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: project.description,
                          }}
                          className="text-gray-700 leading-relaxed mb-4"
                        />
                      )}
                      {project.url && (
                        <a
                          href={project.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-slate-600 hover:text-slate-800 transition-colors font-medium"
                        >
                          <span className="me-2">🔗</span>
                          View Project
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              </WebsiteSection>
            )}
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-10">
            {/* Skills */}
            {resume.skills && resume.skills.length > 0 && (
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-6 border-b-2 border-slate-200 pb-3">Core Skills</h3>
                <div className="space-y-6">
                  {Object.entries(
                    resume.skills.reduce(
                      (acc, skill) => {
                        const category = skill.category || "Other";
                        if (!acc[category]) acc[category] = [];
                        acc[category].push(skill);
                        return acc;
                      },
                      {} as Record<string, typeof resume.skills>,
                    ),
                  ).map(([category, categorySkills]) => (
                    <div key={category}>
                      <h4 className="font-semibold text-gray-800 mb-3">{category}</h4>
                      <div className="flex flex-wrap gap-2">
                        {categorySkills.map((skill) => (
                          <span
                            key={skill.id}
                            className="px-3 py-2 bg-gradient-to-r from-slate-100 to-blue-50 text-gray-700 rounded-lg text-sm font-medium border border-slate-200 hover:from-slate-200 hover:to-blue-100 transition-colors"
                          >
                            {skill.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Education */}
            {resume.educations && resume.educations.length > 0 && (
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-6 border-b-2 border-slate-200 pb-3">Education</h3>
                <div className="space-y-6">
                  {resume.educations.map((education) => (
                    <div key={education.id} className="border-s-4 border-slate-300 ps-4">
                      <h4 className="font-bold text-gray-900 mb-1">{education.institution}</h4>
                      <p className="text-slate-700 text-sm font-medium mb-1">
                        {education.fieldOfStudy ? `${education.degree} in ${education.fieldOfStudy}` : education.degree}
                      </p>
                      <p className="text-gray-600 text-xs">
                        {education.startDate && education.endDate
                          ? `${new Date(education.startDate).getFullYear()} - ${
                              education.isCurrent ? "Present" : new Date(education.endDate).getFullYear()
                            }`
                          : ""}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-6 border-b-2 border-slate-200 pb-3">Languages</h3>
                <div className="space-y-4">
                  {resume.languages.map((language) => (
                    <div key={language.id} className="flex justify-between items-center">
                      <span className="text-gray-700 font-medium">{language.name}</span>
                      <span className="text-slate-600 text-sm bg-slate-100 px-2 py-1 rounded-md">
                        {language.proficiency >= 90
                          ? "Native"
                          : language.proficiency >= 70
                            ? "Fluent"
                            : language.proficiency >= 50
                              ? "Intermediate"
                              : "Basic"}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-6 border-b-2 border-slate-200 pb-3">
                  Certifications
                </h3>
                <div className="space-y-4">
                  {resume.certifications.map((cert) => (
                    <div key={cert.id} className="border-s-4 border-green-300 ps-4">
                      <h4 className="font-bold text-gray-900 text-sm mb-1">{cert.title}</h4>
                      <p className="text-slate-700 text-sm font-medium">{cert.issuer}</p>
                      {cert.dateReceived && (
                        <p className="text-gray-600 text-xs mt-1">{new Date(cert.dateReceived).getFullYear()}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <WebsiteFooter fullName={fullName} className="mt-16 bg-slate-100 py-8" />
    </div>
  );
};
