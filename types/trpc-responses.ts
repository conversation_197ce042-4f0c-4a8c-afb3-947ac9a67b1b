// TRPC response types for the loading system

export interface CreateResumeResponse {
  success: boolean;
  resumeId: number;
}

export interface DuplicateResumeResponse {
  success: boolean;
  resumeId: number;
}

export interface RenameResumeResponse {
  success: boolean;
  resume: {
    id: number;
    title: string;
    userId: string;
    [key: string]: any;
  };
}

export interface DeleteResumeResponse {
  success: boolean;
}

export interface ServerActionResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

// Generic TRPC mutation result type that includes both success and error responses
export type TRPCMutationResult<T> = T | { success: false; error: string };
