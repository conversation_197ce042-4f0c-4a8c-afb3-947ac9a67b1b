import Image from "next/image";
import React from "react";
import { Language } from "@/db/schema";

import {
  CertificationItem,
  ContactInfo,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
  getSectionTranslations,
  getA4PageClasses,
  getHeaderClasses,
  getPhotoClasses,
  getSectionSpacing,
  getTitleClasses,
  getTextClasses,
  getPrintSafeClasses,
  ProfessionalSummary,
  ProjectItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Section with title and horizontal line - Custom Europass styling
const EuropassSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <div className={getSectionSpacing()}>
    <div className="flex flex-col md:flex-row items-start md:items-center mb-3 md:mb-4">
      <h2 className={`${getTitleClasses(3)} w-full md:w-1/3 font-semibold text-blue-400 pr-0 md:pr-4 mb-2 md:mb-0`}>{title}</h2>
      <div className="w-full md:w-2/3 h-0.5 bg-blue-400" />
    </div>
    {children}
  </div>
);

// Custom wrapper for Europass-style layout
const EuropassContentWrapper: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => (
  <div className="flex flex-col md:flex-row">
    <div className="hidden md:block md:w-1/3" /> {/* Empty space for alignment on desktop */}
    <div className="w-full md:w-2/3">{children}</div>
  </div>
);

// Custom Europass-style language component with dots
const EuropassLanguageItem: React.FC<{
  language: Language;
  locale: string;
}> = ({ language }) => (
  <div className="flex justify-between items-center">
    <span className={`${getTextClasses('sm')} font-medium text-gray-800`}>{language.name}</span>
    <div className="flex space-x-1">
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          className={`w-2 h-2 rounded-full ${i < language.proficiency / 20 ? "bg-blue-400" : "bg-gray-300"}`}
        />
      ))}
    </div>
  </div>
);

/**
 * Nosepass Resume Template - Europass Style
 * - Clean, professional Europass-inspired layout
 * - Each section has title with horizontal line
 * - Two-column layout within each section: dates on left, content on right
 * - ATS-friendly structure with semantic HTML
 */
const NosepassTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={getPrintSafeClasses(`nosepass-template bg-white text-gray-900 font-sans ${className}`)}>
      {/* Page wrapper with A4 proportions */}
      <main className={getA4PageClasses('bg-white p-4 md:p-6 lg:p-8')}>
        {/* Header */}
        <header className={`${getHeaderClasses()} py-3 md:py-4 mb-3 md:mb-4`}>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2 md:gap-0">
            <div className="flex items-center justify-center md:justify-start space-x-2">
              <span className="text-blue-500 text-xl md:text-2xl">🧭</span>
              <span className={`${getTextClasses('sm')} text-gray-600 font-medium`}>europass</span>
            </div>
            <h1 className={`${getTitleClasses(3)} font-normal text-gray-600 text-center md:text-left order-2 md:order-none`}>Curriculum Vitae</h1>
            <h2 className={`${getTitleClasses(3)} font-semibold text-gray-800 text-center md:text-right order-1 md:order-none`}>{fullName}</h2>
          </div>
        </header>

        {/* Personal Information Section */}
        <EuropassSection title="Personal Information">
          <div className="flex flex-col md:flex-row">
            <div className="hidden md:block md:w-1/3" /> {/* Empty space for alignment on desktop */}
            <div className="w-full md:w-2/3">
              <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
                {/* Photo */}
                {resume.showPhoto && resume.photo ? (
                  <div className="flex-shrink-0">
                    <Image
                      alt={fullName}
                      className={`${getPhotoClasses()} border border-gray-300`}
                      height={128}
                      src={resume.photo}
                      width={96}
                    />
                  </div>
                ) : null}

                {/* Name, title and contact info */}
                <div className="flex-1 text-center md:text-left">
                  {/* Name and Job Title */}
                  <div className="mb-3 md:mb-4">
                    <h3 className={`${getTitleClasses(1)} text-gray-900 mb-1`}>{fullName}</h3>
                    <p className={`${getTextClasses('lg')} text-gray-700 font-medium`}>{resume.jobTitle}</p>
                  </div>

                  {/* Contact Information */}
                  <ContactInfo
                    className={`space-y-1 ${getTextClasses('sm')} [&_span]:text-gray-700 [&_a]:text-blue-500`}
                    email={resume.email}
                    location={location}
                    phone={resume.phone}
                    variant="vertical"
                    website={resume.website}
                  />
                </div>
              </div>
            </div>
          </div>
        </EuropassSection>

        {/* Profiles Section */}
        {resume.profiles && resume.profiles.length > 0 && (
          <EuropassSection title={sectionTitles.profiles}>
            <EuropassContentWrapper>
              <SocialProfile
                className={`space-y-2 md:space-y-3 [&_.flex]:space-x-3 [&_span:first-child]:text-blue-500 [&_.font-medium]:text-gray-800 [&_.text-gray-600]:${getTextClasses('xs')} [&_.text-gray-600]:block [&_.text-gray-600]:mt-0.5`}
                layout="vertical"
                profiles={resume.profiles}
                showNetworkLabel={false}
              />
            </EuropassContentWrapper>
          </EuropassSection>
        )}

        {/* Summary Section */}
        {resume.bio && (
          <EuropassSection title={sectionTitles.summary}>
            <EuropassContentWrapper>
              <ProfessionalSummary
                bio={resume.bio}
                className={`${getTextClasses('sm')} text-gray-700 leading-relaxed`}
                variant="paragraph"
              />
            </EuropassContentWrapper>
          </EuropassSection>
        )}

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <EuropassSection title={sectionTitles.experience}>
            <div className="space-y-4 md:space-y-6">
              {resume.experiences.map((exp) => (
                <ExperienceItem
                  key={exp.id}
                  className={`experience-item [&_.font-bold]:${getTextClasses('base')} [&_.font-bold]:mb-1 [&_.text-gray-700]:font-semibold [&_.text-gray-700]:text-gray-800 [&_.text-gray-700]:mb-1 [&_.text-gray-600]:mb-2`}
                  experience={exp}
                  locale={locale}
                  showWebsiteIcon={false}
                  variant="europass"
                />
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <EuropassSection title={sectionTitles.education}>
            <div className="space-y-3 md:space-y-4">
              {resume.educations.map((edu) => (
                <EducationItem
                  key={edu.id}
                  className={`education-item [&_.font-bold]:${getTextClasses('base')} [&_.text-gray-700]:font-medium [&_.text-gray-700]:text-gray-800 [&_.text-gray-700]:order-last [&_.text-gray-600]:${getTextClasses('sm')} [&_.text-gray-600]:order-first`}
                  education={edu}
                  locale={locale}
                  variant="europass"
                />
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <EuropassSection title={sectionTitles.skills}>
            <EuropassContentWrapper>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                <SkillsSection
                  className={`col-span-1 md:col-span-2 [&_.skills-section]:space-y-0 [&_.mb-3]:mb-2 [&_.font-semibold]:${getTextClasses('sm')} [&_.text-sm]:${getTextClasses('sm')}`}
                  skills={resume.skills}
                />
              </div>
            </EuropassContentWrapper>
          </EuropassSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <EuropassSection title={sectionTitles.projects}>
            <div className="space-y-3 md:space-y-4">
              {resume.projects.map((project) => (
                <div key={project.id} className="project-item mb-3 md:mb-4 flex flex-col md:flex-row">
                  {/* Date column - custom for Europass layout */}
                  <div className={`w-full md:w-1/3 ${getTextClasses('sm')} text-blue-500 font-medium pr-0 md:pr-4 mb-1 md:mb-0`}>
                    {project.startDate && project.endDate && `${project.startDate} - ${project.endDate}`}
                  </div>
                  {/* Content column */}
                  <div className="w-full md:w-2/3">
                    <ProjectItem
                      className={`[&_.flex]:block [&_.text-right]:hidden [&_.font-bold]:${getTextClasses('base')} [&_.font-bold]:mb-0 [&_.text-gray-700]:mb-1 [&_.text-blue-500]:mb-2`}
                      project={project}
                      showClient={true}
                      showTechnologies={false}
                      variant="standard"
                    />
                  </div>
                </div>
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Languages Section */}
        {resume.languages && resume.languages.length > 0 && (
          <EuropassSection title={sectionTitles.languages}>
            <EuropassContentWrapper>
              <div className="space-y-1 md:space-y-2">
                {resume.languages.map((lang) => (
                  <EuropassLanguageItem key={lang.id} language={lang} locale={locale} />
                ))}
              </div>
            </EuropassContentWrapper>
          </EuropassSection>
        )}

        {/* Certifications Section */}
        {resume.certifications && resume.certifications.length > 0 && (
          <EuropassSection title={sectionTitles.certifications}>
            <div className="space-y-2 md:space-y-3">
              {resume.certifications.map((cert) => (
                <div key={cert.id} className="certification-item flex flex-col md:flex-row">
                  {/* Date column */}
                  <div className={`w-full md:w-1/3 ${getTextClasses('sm')} text-blue-500 font-medium pr-0 md:pr-4 mb-1 md:mb-0`}>
                    {cert.dateReceived && cert.dateReceived}
                  </div>
                  {/* Content column */}
                  <div className="w-full md:w-2/3">
                    <CertificationItem
                      certification={cert}
                      className={`[&_.flex]:block [&_.text-right]:hidden [&_.font-bold]:font-semibold [&_.font-bold]:${getTextClasses('sm')} [&_.text-gray-700]:text-gray-600`}
                      showCredentialId={false}
                    />
                  </div>
                </div>
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Awards Section */}
        {resume.awards && resume.awards.length > 0 && (
          <EuropassSection title={sectionTitles.awards}>
            <div className="space-y-2 md:space-y-3">
              {resume.awards.map((award) => (
                <div key={award.id} className="award-item flex flex-col md:flex-row">
                  {/* Date column */}
                  <div className={`w-full md:w-1/3 ${getTextClasses('sm')} text-blue-500 font-medium pr-0 md:pr-4 mb-1 md:mb-0`}>
                    {award.dateReceived && formatDate(award.dateReceived, false, locale)}
                  </div>
                  {/* Content column */}
                  <div className="w-full md:w-2/3">
                    <h4 className={`font-semibold ${getTextClasses('sm')} text-gray-900`}>{award.title}</h4>
                    <p className={`text-gray-600 ${getTextClasses('sm')}`}>{award.issuer}</p>
                    {award.description && (
                      <div
                        dangerouslySetInnerHTML={{ __html: award.description }}
                        className={`text-gray-600 ${getTextClasses('sm')} mt-1`}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Volunteering Section */}
        {resume.volunteerings && resume.volunteerings.length > 0 && (
          <EuropassSection title={sectionTitles.volunteering}>
            <div className="space-y-2 md:space-y-3">
              {resume.volunteerings.map((volunteering) => (
                <div key={volunteering.id} className="volunteering-item flex flex-col md:flex-row">
                  {/* Date column */}
                  <div className={`w-full md:w-1/3 ${getTextClasses('sm')} text-blue-500 font-medium pr-0 md:pr-4 mb-1 md:mb-0`}>
                    {(volunteering.startDate || volunteering.endDate) &&
                      formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                  </div>
                  {/* Content column */}
                  <div className="w-full md:w-2/3">
                    <h4 className={`font-semibold ${getTextClasses('sm')} text-gray-900`}>{volunteering.role}</h4>
                    <p className={`text-gray-600 ${getTextClasses('sm')}`}>{volunteering.organization}</p>
                    {volunteering.description && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: volunteering.description,
                        }}
                        className={`text-gray-600 ${getTextClasses('sm')} mt-1`}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </EuropassSection>
        )}

        {/* Hobbies Section */}
        {resume.hobbies && resume.hobbies.length > 0 && (
          <EuropassSection title={sectionTitles.hobbies}>
            <EuropassContentWrapper>
              <p className={`text-gray-600 ${getTextClasses('sm')} leading-relaxed col-span-1 md:col-span-2`}>
                {resume.hobbies.map((hobby) => hobby.name).join(", ")}
              </p>
            </EuropassContentWrapper>
          </EuropassSection>
        )}

        {/* References Section */}
        {resume.references && resume.references.length > 0 ? (
          <EuropassSection title={sectionTitles.references}>
            <div className="space-y-3 md:space-y-4">
              {resume.references.map((reference) => (
                <div key={reference.id} className="reference-item">
                  <h4 className={`font-semibold ${getTextClasses('sm')} text-gray-900`}>{reference.name}</h4>
                  <p className={`text-gray-600 ${getTextClasses('sm')}`}>
                    {reference.position} at {reference.company}
                  </p>
                  <div className={`text-gray-600 ${getTextClasses('sm')} mt-1`}>
                    {reference.email && (
                      <p className="flex items-start gap-1">
                        <span>✉️</span>
                        <span className="break-all">{reference.email}</span>
                      </p>
                    )}
                    {reference.phone && (
                      <p className="flex items-center gap-1">
                        <span>📞</span>
                        <span>{reference.phone}</span>
                      </p>
                    )}
                  </div>
                  {reference.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: reference.description,
                      }}
                      className={`text-gray-600 ${getTextClasses('sm')} mt-2`}
                    />
                  )}
                </div>
              ))}
            </div>
          </EuropassSection>
        ) : (
          <EuropassSection title={sectionTitles.references}>
            <EuropassContentWrapper>
              <p className={`text-gray-600 ${getTextClasses('sm')} col-span-1 md:col-span-2`}>Available upon request</p>
            </EuropassContentWrapper>
          </EuropassSection>
        )}
      </main>
    </div>
  );
};

export default NosepassTemplate;
