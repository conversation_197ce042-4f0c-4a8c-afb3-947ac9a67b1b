export interface HeroStats {
  id: string;
  value: string;
  label: string;
  icon: string;
  color: string;
  suffix?: string;
  prefix?: string;
}

export interface SuccessStory {
  id: string;
  name: string;
  role: string;
  company?: string;
  story?: string;
  avatar?: string;
  result?: string;
  achievement?: string;
  quote?: string;
  timeframe?: string;
}

export const HERO_STATS: HeroStats[] = [
  {
    id: "users",
    value: "50,000",
    label: "Active Users",
    icon: "heroicons:users-20-solid",
    color: "primary",
    suffix: "+",
  },
  {
    id: "success-rate",
    value: "89",
    label: "Job Success Rate",
    icon: "heroicons:trophy-20-solid",
    color: "success",
    suffix: "%",
  },
  {
    id: "templates",
    value: "12",
    label: "ATS Templates",
    icon: "heroicons:rectangle-stack-20-solid",
    color: "secondary",
  },
  {
    id: "satisfaction",
    value: "4.9",
    label: "User Rating",
    icon: "heroicons:star-20-solid",
    color: "warning",
    suffix: "/5",
  },
];

export const SUCCESS_STORIES: SuccessStory[] = [
  {
    id: "sarah",
    name: "<PERSON>",
    role: "Software Engineer",
    company: "Google",
    avatar: "/avatars/sarah.jpg",
    achievement: "Got hired in 3 weeks",
    timeframe: "From 6 months of rejections to dream job",
    quote: "QuickCV's ATS optimization changed everything!",
  },
  {
    id: "ahmed",
    name: "Ahmed Hassan",
    role: "Product Manager",
    company: "Microsoft",
    avatar: "/avatars/ahmed.jpg",
    achievement: "40% salary increase",
    timeframe: "Landed 5 interviews in 2 weeks",
    quote: "The Arabic support and templates are incredible.",
  },
  {
    id: "maria",
    name: "Maria Rodriguez",
    role: "UX Designer",
    company: "Apple",
    avatar: "/avatars/maria.jpg",
    achievement: "Multiple job offers",
    timeframe: "Went from 0 to 5 offers",
    quote: "Beautiful templates that actually work!",
  },
];
