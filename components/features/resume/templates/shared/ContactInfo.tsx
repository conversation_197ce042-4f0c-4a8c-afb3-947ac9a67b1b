import React from "react";

export interface ContactInfoProps {
  location?: string;
  phone?: string;
  email: string;
  website?: string;
  variant?: "horizontal" | "vertical" | "compact";
  className?: string;
}

export const ContactInfo: React.FC<ContactInfoProps> = ({
  location,
  phone,
  email,
  website,
  variant = "vertical",
  className = "",
}) => {
  const contactItems = [
    { icon: "📍", value: location, href: null, label: "Location" },
    { icon: "📞", value: phone, href: phone ? `tel:${phone}` : null, label: "Phone" },
    { icon: "✉️", value: email, href: email ? `mailto:${email}` : null, label: "Email" },
    { icon: "🌐", value: website, href: website, label: "Website" },
  ].filter((item) => item.value);

  const containerClass = variant === "horizontal" ? "flex flex-wrap gap-4" : "space-y-2";

  return (
    <address className={`contact-section ${containerClass} ${className} not-italic`}>
      {contactItems.map((item, index) => (
        <div key={index} className="flex items-center text-sm contact-item" data-contact-type={item.label.toLowerCase()}>
          <span className="me-2" aria-hidden="true">{item.icon}</span>
          {item.href ? (
            <a className="text-blue-600 hover:text-blue-800" href={item.href} aria-label={`${item.label}: ${item.value}`}>
              {item.value}
            </a>
          ) : (
            <span>{item.value}</span>
          )}
        </div>
      ))}
    </address>
  );
};
