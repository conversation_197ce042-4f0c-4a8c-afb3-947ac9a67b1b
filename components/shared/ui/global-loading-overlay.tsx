"use client";

import { Progress, Spinner } from "@heroui/react";
import { AnimatePresence, motion } from "framer-motion";
import { useLoadingStore } from "@/lib/loading-store";

export function GlobalLoadingOverlay() {
  const { loadingStates, isGlobalLoading } = useLoadingStore();

  const mutationLoading = loadingStates.filter((s) => s.type === "mutation");
  const uploadLoading = loadingStates.filter((s) => s.type === "file-upload");
  const navigationLoading = loadingStates.filter((s) => s.type === "navigation");

  if (!isGlobalLoading) return null;

  return (
    <AnimatePresence>
      {isGlobalLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm"
          style={{ pointerEvents: "auto" }}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl min-w-[300px] max-w-md mx-4"
          >
            <div className="flex flex-col items-center space-y-4">
              {/* Show spinner for mutations */}
              {mutationLoading.length > 0 && (
                <div className="flex flex-col items-center space-y-2">
                  <Spinner size="lg" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {mutationLoading[0]?.message || "Processing..."}
                  </p>
                </div>
              )}

              {/* Show progress for file uploads */}
              {uploadLoading.length > 0 && (
                <div className="w-full space-y-2">
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {uploadLoading[0]?.message || "Uploading file..."}
                  </p>
                  <Progress value={uploadLoading[0]?.progress || 0} className="w-full" color="primary" />
                </div>
              )}

              {/* Show spinner for navigation */}
              {navigationLoading.length > 0 && mutationLoading.length === 0 && uploadLoading.length === 0 && (
                <div className="flex flex-col items-center space-y-2">
                  <Spinner size="md" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {navigationLoading[0]?.message || "Loading..."}
                  </p>
                </div>
              )}

              {/* Show count if multiple operations */}
              {loadingStates.length > 1 && (
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {loadingStates.length} operations in progress
                </p>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
