# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

QuickCV is a full-stack resume builder application built with:

- **Framework**: Next.js 15 with TypeScript and App Router
- **Database**: SQLite (development) with Drizzle ORM and Turso
- **Authentication**: Clerk for user management
- **UI Components**: HeroUI v2 with Tailwind CSS
- **Architecture**: Full-stack Next.js application using server actions
- **File Uploads**: UploadThing for photo uploads
- **Rich Text**: TipTap editor for descriptions

## Development Commands

### Core Development

```bash
# Development server
bun run dev

# Build for production
bun run build

# Start production server
bun run start

# Linting
bun run lint

# Clean development environment
bun run clean
./scripts/clean-dev.sh

# Development with clean start
bun run dev:clean
```

### Database Operations

```bash
# Generate Drizzle migrations
bun run db:generate

# Run migrations
bun run db:migrate

# Start Turso local development database
bun run db:local

# Seed database with templates
bun run db/seed.ts

# Open Drizzle Studio
bun run db:studio
```

## Core Architecture

### Database Models (Drizzle)

**Core Models:**
- **User**: Clerk-based authentication with `clerkId` and `emailAddress`
- **Resume**: Central model containing personal info and template customization
- **Template**: Resume template definitions with ATS scoring

**Nested Resume Collections:**
- **Education**: Academic background with institution, degree, dates
- **Experience**: Work history with company, title, description
- **Project**: Personal/professional projects with dates and descriptions
- **Award**: Achievements and recognitions
- **Certification**: Professional certifications
- **Skill**: Technical and soft skills with proficiency levels
- **Language**: Language proficiencies
- **Reference**: Professional references
- **Hobby**: Personal interests
- **Volunteering**: Volunteer work and community service
- **Profile**: Social media and professional profiles

### Server Actions Architecture

**Main Server Action Files:**
- `actions/resume-new.ts` - Resume CRUD operations
- `actions/templates-new.ts` - Template management
- `actions/websites.ts` - Website management operations
- `actions/resumes.ts` - Additional resume operations

**Authentication Pattern:**
All server actions use `requireAuth()` from `lib/auth-clerk.ts` to verify user authentication and get user details.

### Frontend Architecture

**Key Directories:**
- `app/` - Next.js App Router pages and API routes
- `components/` - Reusable UI components
- `components/resume/templates/` - Resume template components
- `config/` - Schemas, color schemes, and configuration
- `lib/` - Utilities and database client
- `types/` - TypeScript type definitions

**Template System:**
- 12 resume templates with Pokemon-themed names (azurill, bronzor, chikorita, etc.)
- Template registry in `components/resume/templates/template-registry.tsx`
- Each template accepts a `resume` prop with full resume data
- ATS compatibility scoring system

**Form Architecture:**
- Schema-driven forms defined in `config/schemas.ts`
- Generic form components with validation
- Auto-save functionality using React hooks
- Rich text editing with TipTap

## Resume Data Structure

### Resume Object Contains:
- **Personal Information**: firstName, lastName, jobTitle, email, website, bio
- **Contact Details**: address, city, country, street
- **Customization**: colorScheme, fontFamily, spacing, margins
- **Photo**: Optional profile photo with showPhoto flag
- **Template**: templateId for selected template
- **Nested Collections**: All resume sections as related models

### Template Customization:
- **Color Schemes**: Defined in `config/color-schemes.ts`
- **Fonts**: Font families in `config/fonts.ts`
- **Spacing/Margins**: Layout customization options

## Common Development Patterns

### Server Actions Pattern:
```typescript
export async function actionName(formData: FormData) {
  try {
    const user = await requireAuth();
    // Database operations using Drizzle
    revalidatePath('/path');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### Form Validation:
- Forms use schema definitions from `config/schemas.ts`
- Each schema defines field types, validation rules, and UI properties
- Generic form components handle rendering and validation

### Authentication:
- Clerk handles all authentication flows
- `requireAuth()` function validates user sessions
- All database operations verify user ownership

### File Uploads:
- UploadThing integration for photo uploads
- File validation and processing
- Database storage of file URLs

## Key Components

### Template Registry (`components/resume/templates/template-registry.tsx`):
- Central template switching logic
- Dynamic template component loading
- ATS compatibility indicators

### Resume Forms:
- Schema-driven form generation
- Auto-save with debouncing
- Rich text editor integration

### PDF Export:
- React-to-print implementation
- Print-optimized styling
- Fullscreen preview capabilities

## Testing

- **Frontend**: No test framework currently configured
- **Database**: Drizzle migrations and seeding
- **Development**: Clean development scripts available

## File Organization

```
/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   ├── api/               # API routes
│   └── layout.tsx         # Root layout
├── components/            # UI components
│   ├── resume/           # Resume-specific components
│   │   └── templates/    # Template components
│   └── primitives.ts     # Component primitives
├── config/               # Configuration files
│   ├── schemas.ts        # Form schemas
│   ├── color-schemes.ts  # Color configurations
│   └── fonts.ts          # Font definitions
├── lib/                  # Utilities
│   ├── auth-clerk.ts     # Authentication utilities
│   └── db.ts             # Database client
├── db/                   # Database
│   ├── schema.ts         # Drizzle database schema
│   ├── migrate.ts        # Migration runner
│   └── seed.ts           # Seed data
├── drizzle/              # Generated migrations
│   └── migrations/       # Migration files
├── types/                # TypeScript types
└── actions/              # Server actions
```

## Development Notes

### Template Development:
- Templates are React components in `components/resume/templates/`
- Each template must be registered in the template registry
- Templates receive a `resume` prop with full resume data
- Support for RTL languages and internationalization

### Database Operations:
- All database operations use Drizzle ORM client
- Nested resources follow consistent patterns
- Generic server actions handle CRUD operations
- User ownership verification on all operations

### Authentication Flow:
- Clerk handles sign-in/sign-up flows
- Middleware protects authenticated routes
- User data synced between Clerk and database

### Internationalization:
- Next-intl for multi-language support
- RTL layout support for Arabic
- Localized form validation and messages

## Important Context

- **Migration Status**: Recently migrated from Rails backend to full-stack Next.js
- **Authentication**: Switched from custom auth to Clerk
- **Database**: Migrated from Prisma to Drizzle ORM with Turso
- **Current State**: All Rails backend code has been removed
- **Template Count**: 12 resume templates available
- **File Uploads**: Using UploadThing instead of Active Storage

## Project References

- Always use project as a reference to our quickcv project /Users/<USER>/Downloads/Reactive-Resume-main

Always use context7 for documentation lookups and external API references.

## Development Tips

- Don't run format or lint fixes manually as this produces unneeded changes in git - I will run manually

## Best Practices

- Always use icons not svg

## Localization and Translation

- Always make sure when you add new text to make it translated and add new keys to locale ar and en json files

## AI Agent Guidelines

- Always use the relevant agent