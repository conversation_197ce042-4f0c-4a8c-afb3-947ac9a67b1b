import React from "react";

export interface ProfessionalSummaryProps {
  bio: string;
  variant?: "paragraph" | "bullet" | "highlight";
  className?: string;
}

export const ProfessionalSummary: React.FC<ProfessionalSummaryProps> = ({
  bio,
  variant = "paragraph",
  className = "",
}) => {
  if (!bio) return null;

  const baseClasses = "professional-summary text-gray-700";

  if (variant === "highlight") {
    return (
      <div className={`${baseClasses} bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 ${className}`}>
        <div dangerouslySetInnerHTML={{ __html: bio }} className="text-sm leading-relaxed" />
      </div>
    );
  }

  return (
    <div className={`${baseClasses} ${className}`}>
      <div dangerouslySetInnerHTML={{ __html: bio }} className="text-sm leading-relaxed" />
    </div>
  );
};
