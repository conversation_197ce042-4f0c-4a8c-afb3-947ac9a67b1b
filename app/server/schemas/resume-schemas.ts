import { z } from "zod";

// Define nested schemas for each collection
export const educationSchema = z.object({
  id: z.number().optional(),
  city: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
  institution: z.string().nullable().optional(),
  isCurrent: z.number().nullable().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  degree: z.string().nullable().optional(),
  fieldOfStudy: z.string().nullable().optional(),
  website: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const experienceSchema = z.object({
  id: z.number().optional(),
  title: z.string().nullable().optional(),
  company: z.string().nullable().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  city: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
  isCurrent: z.number().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const projectSchema = z.object({
  id: z.number().optional(),
  title: z.string().nullable().optional(),
  client: z.string().nullable().optional(),
  url: z.string().nullable().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const awardSchema = z.object({
  id: z.number().optional(),
  title: z.string().nullable().optional(),
  issuer: z.string().nullable().optional(),
  url: z.string().nullable().optional(),
  dateReceived: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const certificationSchema = z.object({
  id: z.number().optional(),
  title: z.string().nullable().optional(),
  issuer: z.string().nullable().optional(),
  url: z.string().nullable().optional(),
  dateReceived: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const skillSchema = z.object({
  id: z.number().optional(),
  name: z.string().nullable().optional(),
  proficiency: z.number().nullable().optional(),
  category: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const languageSchema = z.object({
  id: z.number().optional(),
  name: z.string().nullable().optional(),
  proficiency: z.number().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const referenceSchema = z.object({
  id: z.number().optional(),
  name: z.string().nullable().optional(),
  company: z.string().nullable().optional(),
  position: z.string().nullable().optional(),
  email: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const hobbySchema = z.object({
  id: z.number().optional(),
  name: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const volunteeringSchema = z.object({
  id: z.number().optional(),
  organization: z.string().nullable().optional(),
  role: z.string().nullable().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const profileSchema = z.object({
  id: z.number().optional(),
  url: z.string().nullable().optional(),
  username: z.string().nullable().optional(),
  network: z.string().nullable().optional(),
  icon: z.string().nullable().optional(),
  sort: z.number().nullable().optional(),
  resumeId: z.number().optional(),
});

export const updateResumeSchema = z.object({
  id: z.number(),
  title: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  jobTitle: z.string().optional(),
  email: z.string().optional(),
  website: z.string().optional(),
  bio: z.string().optional(),
  city: z.string().optional(),
  street: z.string().optional(),
  country: z.string().optional(),
  address: z.string().optional(),
  photo: z.string().optional(),
  thumbnail: z.string().optional(),
  colorScheme: z.string().optional(),
  fontFamily: z.string().optional(),
  spacing: z.string().optional(),
  margins: z.string().optional(),
  showPhoto: z.number().optional(),
  templateId: z.number().optional(),
  birthDate: z.string().optional(),
  // Nested collections
  educations: z.array(educationSchema).optional(),
  experiences: z.array(experienceSchema).optional(),
  projects: z.array(projectSchema).optional(),
  awards: z.array(awardSchema).optional(),
  certifications: z.array(certificationSchema).optional(),
  skills: z.array(skillSchema).optional(),
  languages: z.array(languageSchema).optional(),
  references: z.array(referenceSchema).optional(),
  hobbies: z.array(hobbySchema).optional(),
  volunteerings: z.array(volunteeringSchema).optional(),
  profiles: z.array(profileSchema).optional(),
});
