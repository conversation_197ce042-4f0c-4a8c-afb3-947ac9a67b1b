import { and, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { resumes } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";
import { createPDFToken } from "@/lib/pdf-tokens";

// Simple PDF generation using Browserless service
async function generatePDF(baseUrl: string, resumeId: string, pdfToken: string, locale: string): Promise<Buffer> {
  const browserWSEndpoint = process.env.BROWSER_WS_ENDPOINT;

  if (!browserWSEndpoint) {
    throw new Error("BROWSER_WS_ENDPOINT environment variable is required");
  }

  const puppeteer = await import("puppeteer");
  const browser = await puppeteer.default.connect({
    browserWSEndpoint,
  });

  try {
    const page = await browser.newPage();

    // Set viewport for consistent rendering
    await page.setViewport({
      width: 1280,
      height: 1024,
      deviceScaleFactor: 2,
    });

    // Navigate to PDF route with secure token
    const pdfUrl = `${baseUrl}/pdf/${resumeId}?token=${pdfToken}&locale=${locale}`;

    await page.goto(pdfUrl, {
      waitUntil: "networkidle0",
      timeout: 30000,
    });

    // Wait for fonts to load (Reactive-Resume approach)
    try {
      await page.evaluate(() => {
        return document.fonts.ready;
      });
    } catch (_error) {
      // Font loading timeout, proceeding anyway
    }

    // Additional wait for rendering to complete
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Generate PDF with A4 format
    const pdf = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true,
      margin: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      },
    });

    await page.close();
    return Buffer.from(pdf);
  } finally {
    await browser.close();
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();

    // Check if user has premium access
    if (!user.isPremium) {
      return NextResponse.json({ error: "Premium subscription required for PDF export" }, { status: 403 });
    }

    const { resumeId, locale } = await request.json();

    if (!resumeId) {
      return NextResponse.json({ error: "Resume ID is required" }, { status: 400 });
    }

    // Verify the resume belongs to the user
    const [resume] = await db
      .select()
      .from(resumes)
      .where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
      .limit(1);

    if (!resume) {
      return NextResponse.json({ error: "Resume not found" }, { status: 404 });
    }

    // Get the base URL for the PDF rendering route
    const protocol = request.nextUrl.protocol;
    let hostname = request.nextUrl.hostname;
    const port = request.nextUrl.port;

    // Replace localhost with host.docker.internal for Docker-based Browserless service in development
    if (process.env.NODE_ENV === "development" && (hostname === "localhost" || hostname === "127.0.0.1")) {
      hostname = "host.docker.internal";
    }

    // Construct the base URL properly
    let baseUrl;
    if (port && port !== "80" && port !== "443") {
      baseUrl = `${protocol}//${hostname}:${port}`;
    } else {
      baseUrl = `${protocol}//${hostname}`;
    }

    // Create secure PDF token for internal browser access
    const pdfToken = await createPDFToken(user.clerkId, resumeId);

    // Generate PDF using Browserless service
    const pdfBuffer = await generatePDF(baseUrl, resumeId, pdfToken, locale || "en");

    // Create response with PDF
    const response = new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${resume.title || "resume"}_${new Date().toISOString().slice(0, 10)}.pdf"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });

    return response;
  } catch (error) {
    console.error("PDF export error:", error);

    // Return more specific error information
    const errorMessage = error instanceof Error ? error.message : "Failed to export PDF";

    return NextResponse.json(
      {
        error: "PDF generation failed",
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

// Health check endpoint for Browserless service
export async function GET() {
  try {
    const browserWSEndpoint = process.env.BROWSER_WS_ENDPOINT;

    if (!browserWSEndpoint) {
      throw new Error("BROWSER_WS_ENDPOINT environment variable is required");
    }

    const puppeteer = await import("puppeteer");
    const browser = await puppeteer.default.connect({
      browserWSEndpoint,
    });

    const version = await browser.version();
    await browser.close();

    return NextResponse.json({
      status: "healthy",
      browserVersion: version,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Browserless service health check failed:", error);
    return NextResponse.json(
      {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 503 },
    );
  }
}
