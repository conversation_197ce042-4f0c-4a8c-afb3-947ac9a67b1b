"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, CardHeader } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { useFormStore } from "@/lib/form-store";
import { upperCaseFirstLetter } from "@/lib/utils";

interface FormSectionProps {
  title: string;
  description?: string;
  icon: string;
  items: any[];
  collectionName: string;
  resumeId: number;
  children: (item: any, index: number) => React.ReactNode;
  onAddItem: () => void;
}

export function FormSection({
  title,
  description,
  icon,
  items,
  collectionName,
  children,
  onAddItem,
}: FormSectionProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { removeItemFromCollection } = useFormStore();

  const handleRemoveItem = (index: number) => {
    removeItemFromCollection(collectionName as any, index);
  };

  const validItems = items.filter((item) => item.id != null);

  return (
    <Card className="w-full">
      <CardHeader
        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <Icon icon={icon} className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">{upperCaseFirstLetter(title)}</h3>
              {description && <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>}
            </div>
          </div>

          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-500 dark:text-gray-400">{validItems.length} items</span>

            <Button
              isIconOnly
              size="sm"
              variant="flat"
              color="primary"
              onPress={onAddItem}
              className="bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40"
            >
              <Icon icon="lucide:plus" className="w-4 h-4" />
            </Button>

            <Icon
              icon="lucide:chevron-down"
              className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? "rotate-180" : ""}`}
            />
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardBody className="pt-0">
          <div className="space-y-4">
            {validItems.map((item, index) => (
              <div key={`${collectionName}-${item.id || index}`} className="relative">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex justify-end mb-2">
                    <Button
                      isIconOnly
                      size="sm"
                      variant="flat"
                      color="danger"
                      onPress={() => handleRemoveItem(index)}
                      className="opacity-60 hover:opacity-100"
                    >
                      <Icon icon="lucide:trash-2" className="w-4 h-4" />
                    </Button>
                  </div>

                  {children(item, index)}
                </div>
              </div>
            ))}

            {validItems.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Icon icon="lucide:plus-circle" className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No items yet</p>
                <p className="text-xs">Click the + button to add your first item</p>
              </div>
            )}
          </div>
        </CardBody>
      )}
    </Card>
  );
}
